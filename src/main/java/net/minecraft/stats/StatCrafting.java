package net.minecraft.stats;

import net.minecraft.item.Item;
import net.minecraft.scoreboard.IScoreObjectiveCriteria;
import net.minecraft.util.IChatComponent;

public class StatCrafting extends StatBase {
    private final Item item;

    public StatCrafting(String statPrefix, String blockName, IChatComponent statNameIn, Item item) {
        super(statPrefix + blockName, statNameIn);
        this.item = item;
        int i = Item.getIdFromItem(item);

        if (i != 0) {
            IScoreObjectiveCriteria.INSTANCES.put(statPrefix + i, this.getCriteria());
        }
    }

    public Item getItem() {
        return this.item;
    }
}
