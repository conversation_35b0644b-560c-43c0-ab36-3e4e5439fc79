package net.minecraft.stats;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import net.minecraft.block.Block;
import net.minecraft.entity.EntityList;
import net.minecraft.init.Blocks;
import net.minecraft.item.Item;
import net.minecraft.item.ItemBlock;
import net.minecraft.item.ItemStack;
import net.minecraft.item.crafting.CraftingManager;
import net.minecraft.item.crafting.FurnaceRecipes;
import net.minecraft.item.crafting.IRecipe;
import net.minecraft.util.ChatComponentTranslation;
import net.minecraft.util.ResourceLocation;

import java.util.List;
import java.util.Map;
import java.util.Set;

public class StatList {
    public static final List<StatBase> allStats = Lists.newArrayList();
    public static final List<StatBase> generalStats = Lists.newArrayList();
    public static final List<StatCrafting> itemStats = Lists.newArrayList();
    public static final List<StatCrafting> objectMineStats = Lists.newArrayList();
    public static final StatBase[] mineBlockStatArray = new StatBase[4096];
    public static final StatBase[] objectCraftStats = new StatBase[32000];
    public static final StatBase[] objectUseStats = new StatBase[32000];
    public static final StatBase[] objectBreakStats = new StatBase[32000];
    protected static final Map<String, StatBase> oneShotStats = Maps.newHashMap();
    public static final StatBase leaveGameStat = (new StatBasic("stat.leaveGame", new ChatComponentTranslation("stat.leaveGame"))).initIndependentStat().registerStat();
    public static final StatBase minutesPlayedStat = (new StatBasic("stat.playOneMinute", new ChatComponentTranslation("stat.playOneMinute"), StatBase.timeStatType)).initIndependentStat().registerStat();
    public static final StatBase timeSinceDeathStat = (new StatBasic("stat.timeSinceDeath", new ChatComponentTranslation("stat.timeSinceDeath"), StatBase.timeStatType)).initIndependentStat().registerStat();
    public static final StatBase distanceWalkedStat = (new StatBasic("stat.walkOneCm", new ChatComponentTranslation("stat.walkOneCm"), StatBase.distanceStatType)).initIndependentStat().registerStat();
    public static final StatBase distanceCrouchedStat = (new StatBasic("stat.crouchOneCm", new ChatComponentTranslation("stat.crouchOneCm"), StatBase.distanceStatType)).initIndependentStat().registerStat();
    public static final StatBase distanceSprintedStat = (new StatBasic("stat.sprintOneCm", new ChatComponentTranslation("stat.sprintOneCm"), StatBase.distanceStatType)).initIndependentStat().registerStat();
    public static final StatBase distanceSwumStat = (new StatBasic("stat.swimOneCm", new ChatComponentTranslation("stat.swimOneCm"), StatBase.distanceStatType)).initIndependentStat().registerStat();
    public static final StatBase distanceFallenStat = (new StatBasic("stat.fallOneCm", new ChatComponentTranslation("stat.fallOneCm"), StatBase.distanceStatType)).initIndependentStat().registerStat();
    public static final StatBase distanceClimbedStat = (new StatBasic("stat.climbOneCm", new ChatComponentTranslation("stat.climbOneCm"), StatBase.distanceStatType)).initIndependentStat().registerStat();
    public static final StatBase distanceFlownStat = (new StatBasic("stat.flyOneCm", new ChatComponentTranslation("stat.flyOneCm"), StatBase.distanceStatType)).initIndependentStat().registerStat();
    public static final StatBase distanceDoveStat = (new StatBasic("stat.diveOneCm", new ChatComponentTranslation("stat.diveOneCm"), StatBase.distanceStatType)).initIndependentStat().registerStat();
    public static final StatBase distanceByMinecartStat = (new StatBasic("stat.minecartOneCm", new ChatComponentTranslation("stat.minecartOneCm"), StatBase.distanceStatType)).initIndependentStat().registerStat();
    public static final StatBase distanceByBoatStat = (new StatBasic("stat.boatOneCm", new ChatComponentTranslation("stat.boatOneCm"), StatBase.distanceStatType)).initIndependentStat().registerStat();
    public static final StatBase distanceByPigStat = (new StatBasic("stat.pigOneCm", new ChatComponentTranslation("stat.pigOneCm"), StatBase.distanceStatType)).initIndependentStat().registerStat();
    public static final StatBase distanceByHorseStat = (new StatBasic("stat.horseOneCm", new ChatComponentTranslation("stat.horseOneCm"), StatBase.distanceStatType)).initIndependentStat().registerStat();
    public static final StatBase jumpStat = (new StatBasic("stat.jump", new ChatComponentTranslation("stat.jump"))).initIndependentStat().registerStat();
    public static final StatBase dropStat = (new StatBasic("stat.drop", new ChatComponentTranslation("stat.drop"))).initIndependentStat().registerStat();
    public static final StatBase damageDealtStat = (new StatBasic("stat.damageDealt", new ChatComponentTranslation("stat.damageDealt"), StatBase.divideByTen)).registerStat();
    public static final StatBase damageTakenStat = (new StatBasic("stat.damageTaken", new ChatComponentTranslation("stat.damageTaken"), StatBase.divideByTen)).registerStat();
    public static final StatBase deathsStat = (new StatBasic("stat.deaths", new ChatComponentTranslation("stat.deaths"))).registerStat();
    public static final StatBase mobKillsStat = (new StatBasic("stat.mobKills", new ChatComponentTranslation("stat.mobKills"))).registerStat();
    public static final StatBase animalsBredStat = (new StatBasic("stat.animalsBred", new ChatComponentTranslation("stat.animalsBred"))).registerStat();
    public static final StatBase playerKillsStat = (new StatBasic("stat.playerKills", new ChatComponentTranslation("stat.playerKills"))).registerStat();
    public static final StatBase fishCaughtStat = (new StatBasic("stat.fishCaught", new ChatComponentTranslation("stat.fishCaught"))).registerStat();
    public static final StatBase junkFishedStat = (new StatBasic("stat.junkFished", new ChatComponentTranslation("stat.junkFished"))).registerStat();
    public static final StatBase treasureFishedStat = (new StatBasic("stat.treasureFished", new ChatComponentTranslation("stat.treasureFished"))).registerStat();
    public static final StatBase timesTalkedToVillagerStat = (new StatBasic("stat.talkedToVillager", new ChatComponentTranslation("stat.talkedToVillager"))).registerStat();
    public static final StatBase timesTradedWithVillagerStat = (new StatBasic("stat.tradedWithVillager", new ChatComponentTranslation("stat.tradedWithVillager"))).registerStat();
    public static final StatBase cakeSliceEatenStat = (new StatBasic("stat.cakeSlicesEaten", new ChatComponentTranslation("stat.cakeSlicesEaten"))).registerStat();
    public static final StatBase cauldronFilledStat = (new StatBasic("stat.cauldronFilled", new ChatComponentTranslation("stat.cauldronFilled"))).registerStat();
    public static final StatBase cauldronUsedStat = (new StatBasic("stat.cauldronUsed", new ChatComponentTranslation("stat.cauldronUsed"))).registerStat();
    public static final StatBase armorCleanedStat = (new StatBasic("stat.armorCleaned", new ChatComponentTranslation("stat.armorCleaned"))).registerStat();
    public static final StatBase bannerCleanedStat = (new StatBasic("stat.bannerCleaned", new ChatComponentTranslation("stat.bannerCleaned"))).registerStat();
    public static final StatBase brewingStandInteractStat = (new StatBasic("stat.brewingstandInteraction", new ChatComponentTranslation("stat.brewingstandInteraction"))).registerStat();
    public static final StatBase beaconInteractStat = (new StatBasic("stat.beaconInteraction", new ChatComponentTranslation("stat.beaconInteraction"))).registerStat();
    public static final StatBase dropperInspectedStat = (new StatBasic("stat.dropperInspected", new ChatComponentTranslation("stat.dropperInspected"))).registerStat();
    public static final StatBase hopperInspectedStat = (new StatBasic("stat.hopperInspected", new ChatComponentTranslation("stat.hopperInspected"))).registerStat();
    public static final StatBase dispenserInspectedStat = (new StatBasic("stat.dispenserInspected", new ChatComponentTranslation("stat.dispenserInspected"))).registerStat();
    public static final StatBase noteblockPlayedStat = (new StatBasic("stat.noteblockPlayed", new ChatComponentTranslation("stat.noteblockPlayed"))).registerStat();
    public static final StatBase noteblockTunedStat = (new StatBasic("stat.noteblockTuned", new ChatComponentTranslation("stat.noteblockTuned"))).registerStat();
    public static final StatBase flowerPottedStat = (new StatBasic("stat.flowerPotted", new ChatComponentTranslation("stat.flowerPotted"))).registerStat();
    public static final StatBase trappedChestTriggeredStat = (new StatBasic("stat.trappedChestTriggered", new ChatComponentTranslation("stat.trappedChestTriggered"))).registerStat();
    public static final StatBase enderchestOpenedStat = (new StatBasic("stat.enderchestOpened", new ChatComponentTranslation("stat.enderchestOpened"))).registerStat();
    public static final StatBase itemEnchantedStat = (new StatBasic("stat.itemEnchanted", new ChatComponentTranslation("stat.itemEnchanted"))).registerStat();
    public static final StatBase recordPlayedStat = (new StatBasic("stat.recordPlayed", new ChatComponentTranslation("stat.recordPlayed"))).registerStat();
    public static final StatBase furnaceInteractStat = (new StatBasic("stat.furnaceInteraction", new ChatComponentTranslation("stat.furnaceInteraction"))).registerStat();
    public static final StatBase craftingTableInteractStat = (new StatBasic("stat.craftingTableInteraction", new ChatComponentTranslation("stat.workbenchInteraction"))).registerStat();
    public static final StatBase chestOpenedStat = (new StatBasic("stat.chestOpened", new ChatComponentTranslation("stat.chestOpened"))).registerStat();

    public static void init() {
        initMiningStats();
        initStats();
        initItemDepleteStats();
        initCraftableStats();
        AchievementList.init();
        EntityList.init();
    }

    private static void initCraftableStats() {
        Set<Item> set = Sets.newHashSet();

        for (IRecipe irecipe : CraftingManager.getInstance().getRecipeList()) {
            if (irecipe.getRecipeOutput() != null) {
                set.add(irecipe.getRecipeOutput().getItem());
            }
        }

        for (ItemStack itemstack : FurnaceRecipes.instance().getSmeltingList().values()) {
            set.add(itemstack.getItem());
        }

        for (Item item : set) {
            if (item != null) {
                int i = Item.getIdFromItem(item);
                String s = getItemName(item);

                if (s != null) {
                    objectCraftStats[i] = (new StatCrafting("stat.craftItem.", s, new ChatComponentTranslation("stat.craftItem", (new ItemStack(item)).getChatComponent()), item)).registerStat();
                }
            }
        }

        replaceAllSimilarBlocks(objectCraftStats);
    }

    private static void initMiningStats() {
        for (Block block : Block.blockRegistry) {
            Item item = Item.getItemFromBlock(block);

            if (item != null) {
                int i = Block.getIdFromBlock(block);
                String s = getItemName(item);

                if (s != null && block.getEnableStats()) {
                    mineBlockStatArray[i] = (new StatCrafting("stat.mineBlock.", s, new ChatComponentTranslation("stat.mineBlock", (new ItemStack(block)).getChatComponent()), item)).registerStat();
                    objectMineStats.add((StatCrafting) mineBlockStatArray[i]);
                }
            }
        }

        replaceAllSimilarBlocks(mineBlockStatArray);
    }

    private static void initStats() {
        for (Item item : Item.itemRegistry) {
            if (item != null) {
                int i = Item.getIdFromItem(item);
                String s = getItemName(item);

                if (s != null) {
                    objectUseStats[i] = (new StatCrafting("stat.useItem.", s, new ChatComponentTranslation("stat.useItem", (new ItemStack(item)).getChatComponent()), item)).registerStat();

                    if (!(item instanceof ItemBlock)) {
                        itemStats.add((StatCrafting) objectUseStats[i]);
                    }
                }
            }
        }

        replaceAllSimilarBlocks(objectUseStats);
    }

    private static void initItemDepleteStats() {
        for (Item item : Item.itemRegistry) {
            if (item != null) {
                int i = Item.getIdFromItem(item);
                String s = getItemName(item);

                if (s != null && item.isDamageable()) {
                    objectBreakStats[i] = (new StatCrafting("stat.breakItem.", s, new ChatComponentTranslation("stat.breakItem", (new ItemStack(item)).getChatComponent()), item)).registerStat();
                }
            }
        }

        replaceAllSimilarBlocks(objectBreakStats);
    }

    private static String getItemName(Item itemIn) {
        ResourceLocation resourcelocation = Item.itemRegistry.getNameForObject(itemIn);
        return resourcelocation != null ? resourcelocation.toString().replace(':', '.') : null;
    }

    private static void replaceAllSimilarBlocks(StatBase[] stat) {
        mergeStatBases(stat, Blocks.water, Blocks.flowing_water);
        mergeStatBases(stat, Blocks.lava, Blocks.flowing_lava);
        mergeStatBases(stat, Blocks.lit_pumpkin, Blocks.pumpkin);
        mergeStatBases(stat, Blocks.lit_furnace, Blocks.furnace);
        mergeStatBases(stat, Blocks.lit_redstone_ore, Blocks.redstone_ore);
        mergeStatBases(stat, Blocks.powered_repeater, Blocks.unpowered_repeater);
        mergeStatBases(stat, Blocks.powered_comparator, Blocks.unpowered_comparator);
        mergeStatBases(stat, Blocks.redstone_torch, Blocks.unlit_redstone_torch);
        mergeStatBases(stat, Blocks.lit_redstone_lamp, Blocks.redstone_lamp);
        mergeStatBases(stat, Blocks.double_stone_slab, Blocks.stone_slab);
        mergeStatBases(stat, Blocks.double_wooden_slab, Blocks.wooden_slab);
        mergeStatBases(stat, Blocks.double_stone_slab2, Blocks.stone_slab2);
        mergeStatBases(stat, Blocks.grass, Blocks.dirt);
        mergeStatBases(stat, Blocks.farmland, Blocks.dirt);
    }

    private static void mergeStatBases(StatBase[] statBaseIn, Block block1, Block block2) {
        int i = Block.getIdFromBlock(block1);
        int j = Block.getIdFromBlock(block2);

        if (statBaseIn[i] != null && statBaseIn[j] == null) {
            statBaseIn[j] = statBaseIn[i];
        } else {
            allStats.remove(statBaseIn[i]);
            objectMineStats.remove(statBaseIn[i]);
            generalStats.remove(statBaseIn[i]);
            statBaseIn[i] = statBaseIn[j];
        }
    }

    public static StatBase getStatKillEntity(EntityList.EntityEggInfo eggInfo) {
        String s = EntityList.getStringFromID(eggInfo.spawnedID);
        return s == null ? null : (new StatBase("stat.killEntity." + s, new ChatComponentTranslation("stat.entityKill", new ChatComponentTranslation("entity." + s + ".name")))).registerStat();
    }

    public static StatBase getStatEntityKilledBy(EntityList.EntityEggInfo eggInfo) {
        String s = EntityList.getStringFromID(eggInfo.spawnedID);
        return s == null ? null : (new StatBase("stat.entityKilledBy." + s, new ChatComponentTranslation("stat.entityKilledBy", new ChatComponentTranslation("entity." + s + ".name")))).registerStat();
    }

    public static StatBase getOneShotStat(String statName) {
        return oneShotStats.get(statName);
    }
}
