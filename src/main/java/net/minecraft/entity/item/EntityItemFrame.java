package net.minecraft.entity.item;

import net.minecraft.entity.Entity;
import net.minecraft.entity.EntityHanging;
import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.init.Blocks;
import net.minecraft.init.Items;
import net.minecraft.item.ItemMap;
import net.minecraft.item.ItemStack;
import net.minecraft.nbt.NBTTagCompound;
import net.minecraft.util.BlockPos;
import net.minecraft.util.DamageSource;
import net.minecraft.util.EnumFacing;
import net.minecraft.world.World;
import net.minecraft.world.storage.MapData;

public class EntityItemFrame extends EntityHanging {
    private float itemDropChance = 1.0F;

    public EntityItemFrame(World worldIn) {
        super(worldIn);
    }

    public EntityItemFrame(World worldIn, BlockPos pos, EnumFacing facing) {
        super(worldIn, pos);
        this.updateFacingWithBoundingBox(facing);
    }

    protected void entityInit() {
        this.getDataWatcher().addObjectByDataType(8, 5);
        this.getDataWatcher().addObject(9, (byte) 0);
    }

    public float getCollisionBorderSize() {
        return 0.0F;
    }

    public boolean attackEntityFrom(DamageSource source, float amount) {
        if (this.isEntityInvulnerable(source)) {
            return false;
        } else if (!source.isExplosion() && this.getDisplayedItem() != null) {
            if (!this.worldObj.isRemote) {
                this.dropItemOrSelf(source.getEntity(), false);
                this.setDisplayedItem(null);
            }

            return true;
        } else {
            return super.attackEntityFrom(source, amount);
        }
    }

    public int getWidthPixels() {
        return 12;
    }

    public int getHeightPixels() {
        return 12;
    }

    public boolean isInRangeToRenderDist(double distance) {
        double d0 = 16.0D;
        d0 = d0 * 64.0D * this.renderDistanceWeight;
        return distance < d0 * d0;
    }

    public void onBroken(Entity brokenEntity) {
        this.dropItemOrSelf(brokenEntity, true);
    }

    public void dropItemOrSelf(Entity entityIn, boolean drop) {
        if (this.worldObj.getGameRules().getBoolean("doEntityDrops")) {
            ItemStack itemstack = this.getDisplayedItem();

            if (entityIn instanceof EntityPlayer entityplayer) {

                if (entityplayer.capabilities.isCreativeMode) {
                    this.removeFrameFromMap(itemstack);
                    return;
                }
            }

            if (drop) {
                this.entityDropItem(new ItemStack(Items.item_frame), 0.0F);
            }

            if (itemstack != null && this.rand.nextFloat() < this.itemDropChance) {
                itemstack = itemstack.copy();
                this.removeFrameFromMap(itemstack);
                this.entityDropItem(itemstack, 0.0F);
            }
        }
    }

    private void removeFrameFromMap(ItemStack stack) {
        if (stack != null) {
            if (stack.getItem() == Items.filled_map) {
                MapData mapdata = ((ItemMap) stack.getItem()).getMapData(stack, this.worldObj);
                mapdata.mapDecorations.remove("frame-" + this.getEntityId());
            }

            stack.setItemFrame(null);
        }
    }

    public ItemStack getDisplayedItem() {
        return this.getDataWatcher().getWatchableObjectItemStack(8);
    }

    public void setDisplayedItem(ItemStack stack) {
        this.setDisplayedItemWithUpdate(stack, true);
    }

    private void setDisplayedItemWithUpdate(ItemStack stack, boolean update) {
        if (stack != null) {
            stack = stack.copy();
            stack.stackSize = 1;
            stack.setItemFrame(this);
        }

        this.getDataWatcher().updateObject(8, stack);
        this.getDataWatcher().setObjectWatched(8);

        if (update && this.hangingPosition != null) {
            this.worldObj.updateComparatorOutputLevel(this.hangingPosition, Blocks.air);
        }
    }

    public int getItemRotation() {
        return this.getDataWatcher().getWatchableObjectByte(9);
    }

    public void setItemRotation(int rotationIn) {
        this.setItemRotation(rotationIn, true);
    }

    private void setItemRotation(int rotationIn, boolean update) {
        this.getDataWatcher().updateObject(9, (byte) (rotationIn % 8));

        if (update && this.hangingPosition != null) {
            this.worldObj.updateComparatorOutputLevel(this.hangingPosition, Blocks.air);
        }
    }

    public void writeEntityToNBT(NBTTagCompound tagCompound) {
        if (this.getDisplayedItem() != null) {
            tagCompound.setTag("Item", this.getDisplayedItem().writeToNBT(new NBTTagCompound()));
            tagCompound.setByte("ItemRotation", (byte) this.getItemRotation());
            tagCompound.setFloat("ItemDropChance", this.itemDropChance);
        }

        super.writeEntityToNBT(tagCompound);
    }

    public void readEntityFromNBT(NBTTagCompound tagCompund) {
        NBTTagCompound nbttagcompound = tagCompund.getCompoundTag("Item");

        if (nbttagcompound != null && !nbttagcompound.hasNoTags()) {
            this.setDisplayedItemWithUpdate(ItemStack.loadItemStackFromNBT(nbttagcompound), false);
            this.setItemRotation(tagCompund.getByte("ItemRotation"), false);

            if (tagCompund.hasKey("ItemDropChance", 99)) {
                this.itemDropChance = tagCompund.getFloat("ItemDropChance");
            }

            if (tagCompund.hasKey("Direction")) {
                this.setItemRotation(this.getItemRotation() * 2, false);
            }
        }

        super.readEntityFromNBT(tagCompund);
    }

    public boolean interactFirst(EntityPlayer playerIn) {
        if (this.getDisplayedItem() == null) {
            ItemStack itemstack = playerIn.getHeldItem();

            if (itemstack != null && !this.worldObj.isRemote) {
                this.setDisplayedItem(itemstack);

                if (!playerIn.capabilities.isCreativeMode && --itemstack.stackSize <= 0) {
                    playerIn.inventory.setInventorySlotContents(playerIn.inventory.currentItem, null);
                }
            }
        } else if (!this.worldObj.isRemote) {
            this.setItemRotation(this.getItemRotation() + 1);
        }

        return true;
    }

    public int getAnalogOutput() {
        return this.getDisplayedItem() == null ? 0 : this.getItemRotation() % 8 + 1;
    }
}
