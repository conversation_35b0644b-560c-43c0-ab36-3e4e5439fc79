package net.minecraft.util;

import net.bloom.bloomclient.BloomClient;
import net.bloom.bloomclient.event.MoveInputEvent;
import net.minecraft.client.settings.GameSettings;
import net.bloom.bloomclient.utils.player.MovementUtils;
import net.bloom.bloomclient.features.component.components.player.MovementCorrection;
import net.bloom.bloomclient.features.component.components.player.RotationComponent;
import static net.minecraft.client.MinecraftInstance.mc;

public class MovementInputFromOptions extends MovementInput {
    private final GameSettings gameSettings;

    public MovementInputFromOptions(GameSettings gameSettingsIn) {
        this.gameSettings = gameSettingsIn;
    }

    public void updatePlayerMoveState() {
        final float lastForward = this.moveForward;
        final float lastStrafe = this.moveStrafe;

        this.moveStrafe = 0.0F;
        this.moveForward = 0.0F;

        if (this.gameSettings.keyBindForward.isKeyDown()) {
            ++this.moveForward;
        }

        if (this.gameSettings.keyBindBack.isKeyDown()) {
            --this.moveForward;
        }

        if (this.gameSettings.keyBindLeft.isKeyDown()) {
            ++this.moveStrafe;
        }

        if (this.gameSettings.keyBindRight.isKeyDown()) {
            --this.moveStrafe;
        }

        this.jump = this.gameSettings.keyBindJump.isKeyDown();
        this.sneak = this.gameSettings.keyBindSneak.isKeyDown();
        final MoveInputEvent event = new MoveInputEvent(this.moveForward, this.moveStrafe, lastForward, lastStrafe, this.jump, this.sneak, 0.3D);

        if (mc.thePlayer != null) {
            Rotation rotation = mc.thePlayer.getRotation();
            VectorMovement predictedMovement = MovementUtils.handleSilentMove(event.getStrafe(), event.getForward(), rotation);

            float diffForward = event.getLastForward() - predictedMovement.forward;
            float diffStrafe = event.getLastStrafe() - predictedMovement.strafe;

            if (diffForward >= 2.0f || diffForward <= -2.0f) {
                predictedMovement.forward = 0.0f;
            }

            if (diffStrafe >= 2.0f || diffStrafe <= -2.0f) {
                predictedMovement.strafe = 0.0f;
            }

            float forward = event.getForward();
            float strafe = event.getStrafe();

            if (MovementCorrection.type == MovementCorrection.Type.FULL && mc.thePlayer.getPlayerRotation() != mc.thePlayer.getRotation()) {
                event.setStrafe(Math.max(-1f, Math.min(1f, predictedMovement.strafe)));
                event.setForward(Math.max(-1f, Math.min(1f, predictedMovement.forward)));
            }

            if (MovementCorrection.type == MovementCorrection.Type.SILENT && mc.thePlayer.getPlayerRotation() != mc.thePlayer.getRotation()) {
                float angle = (float)Math.toRadians(mc.thePlayer.playerYaw - rotation.getYaw());
                event.setForward(Math.round(forward * Math.cos(angle) + strafe * Math.sin(angle)));
                event.setStrafe(Math.round(strafe * Math.cos(angle) - forward * Math.sin(angle)));
            }
        }

        BloomClient.eventManager.call(event);
        this.moveForward = event.getForward();
        this.moveStrafe = event.getStrafe();
        this.jump = event.getJump();
        this.sneak = event.getSneak();

        final double sneakMultiplier = event.getSneakMultiplier();

        if (this.sneak) {
            this.moveStrafe = (float) (this.moveStrafe * sneakMultiplier);
            this.moveForward = (float) (this.moveForward * sneakMultiplier);
        }
    }
}
