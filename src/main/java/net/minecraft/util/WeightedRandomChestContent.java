package net.minecraft.util;

import com.google.common.collect.Lists;
import net.minecraft.inventory.IInventory;
import net.minecraft.item.Item;
import net.minecraft.item.ItemStack;
import net.minecraft.tileentity.TileEntityDispenser;

import java.util.Collections;
import java.util.List;
import java.util.Random;

public class WeightedRandomChestContent extends WeightedRandom.Item {
    private final ItemStack theItemId;
    private final int minStackSize;
    private final int maxStackSize;

    public WeightedRandomChestContent(Item item, int itemId, int minimumChance, int maximumChance, int itemWeightIn) {
        super(itemWeightIn);
        this.theItemId = new ItemStack(item, 1, itemId);
        this.minStackSize = minimumChance;
        this.maxStackSize = maximumChance;
    }

    public WeightedRandomChestContent(ItemStack stack, int minimumChance, int maximumChance, int itemWeightIn) {
        super(itemWeightIn);
        this.theItemId = stack;
        this.minStackSize = minimumChance;
        this.maxStackSize = maximumChance;
    }

    public static void generateChestContents(Random random, List<WeightedRandomChestContent> listIn, IInventory inv, int max) {
        for (int i = 0; i < max; ++i) {
            WeightedRandomChestContent weightedrandomchestcontent = WeightedRandom.getRandomItem(random, listIn);
            int j = weightedrandomchestcontent.minStackSize + random.nextInt(weightedrandomchestcontent.maxStackSize - weightedrandomchestcontent.minStackSize + 1);

            if (weightedrandomchestcontent.theItemId.getMaxStackSize() >= j) {
                ItemStack itemstack1 = weightedrandomchestcontent.theItemId.copy();
                itemstack1.stackSize = j;
                inv.setInventorySlotContents(random.nextInt(inv.getSizeInventory()), itemstack1);
            } else {
                for (int k = 0; k < j; ++k) {
                    ItemStack itemstack = weightedrandomchestcontent.theItemId.copy();
                    itemstack.stackSize = 1;
                    inv.setInventorySlotContents(random.nextInt(inv.getSizeInventory()), itemstack);
                }
            }
        }
    }

    public static void generateDispenserContents(Random random, List<WeightedRandomChestContent> listIn, TileEntityDispenser dispenser, int max) {
        for (int i = 0; i < max; ++i) {
            WeightedRandomChestContent weightedrandomchestcontent = WeightedRandom.getRandomItem(random, listIn);
            int j = weightedrandomchestcontent.minStackSize + random.nextInt(weightedrandomchestcontent.maxStackSize - weightedrandomchestcontent.minStackSize + 1);

            if (weightedrandomchestcontent.theItemId.getMaxStackSize() >= j) {
                ItemStack itemstack1 = weightedrandomchestcontent.theItemId.copy();
                itemstack1.stackSize = j;
                dispenser.setInventorySlotContents(random.nextInt(dispenser.getSizeInventory()), itemstack1);
            } else {
                for (int k = 0; k < j; ++k) {
                    ItemStack itemstack = weightedrandomchestcontent.theItemId.copy();
                    itemstack.stackSize = 1;
                    dispenser.setInventorySlotContents(random.nextInt(dispenser.getSizeInventory()), itemstack);
                }
            }
        }
    }

    public static List<WeightedRandomChestContent> addContents(List<WeightedRandomChestContent> contentList, WeightedRandomChestContent... contents) {
        List<WeightedRandomChestContent> list = Lists.newArrayList(contentList);
        Collections.addAll(list, contents);
        return list;
    }
}