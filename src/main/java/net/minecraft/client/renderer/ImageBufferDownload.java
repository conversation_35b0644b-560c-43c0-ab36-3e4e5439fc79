package net.minecraft.client.renderer;

import java.awt.*;
import java.awt.image.BufferedImage;
import java.awt.image.DataBufferInt;

public class ImageBufferDownload implements IImageBuffer {
    private int[] imageData;
    private int imageWidth;

    public BufferedImage parseUserSkin(BufferedImage image) {
        if (image == null) {
            return null;
        } else {
            this.imageWidth = 64;
            int imageHeight = 64;
            int i = image.getWidth();
            int j = image.getHeight();
            int k;

            for (k = 1; this.imageWidth < i || imageHeight < j; k *= 2) {
                this.imageWidth *= 2;
                imageHeight *= 2;
            }

            BufferedImage bufferedimage = new BufferedImage(this.imageWidth, imageHeight, 2);
            Graphics graphics = bufferedimage.getGraphics();
            graphics.drawImage(image, 0, 0, null);

            if (image.getHeight() == 32 * k) {
                graphics.drawImage(bufferedimage, 24 * k, 48 * k, 20 * k, 52 * k, 4 * k, 16 * k, 8 * k, 20 * k, null);
                graphics.drawImage(bufferedimage, 28 * k, 48 * k, 24 * k, 52 * k, 8 * k, 16 * k, 12 * k, 20 * k, null);
                graphics.drawImage(bufferedimage, 20 * k, 52 * k, 16 * k, 64 * k, 8 * k, 20 * k, 12 * k, 32 * k, null);
                graphics.drawImage(bufferedimage, 24 * k, 52 * k, 20 * k, 64 * k, 4 * k, 20 * k, 8 * k, 32 * k, null);
                graphics.drawImage(bufferedimage, 28 * k, 52 * k, 24 * k, 64 * k, 0, 20 * k, 4 * k, 32 * k, null);
                graphics.drawImage(bufferedimage, 32 * k, 52 * k, 28 * k, 64 * k, 12 * k, 20 * k, 16 * k, 32 * k, null);
                graphics.drawImage(bufferedimage, 40 * k, 48 * k, 36 * k, 52 * k, 44 * k, 16 * k, 48 * k, 20 * k, null);
                graphics.drawImage(bufferedimage, 44 * k, 48 * k, 40 * k, 52 * k, 48 * k, 16 * k, 52 * k, 20 * k, null);
                graphics.drawImage(bufferedimage, 36 * k, 52 * k, 32 * k, 64 * k, 48 * k, 20 * k, 52 * k, 32 * k, null);
                graphics.drawImage(bufferedimage, 40 * k, 52 * k, 36 * k, 64 * k, 44 * k, 20 * k, 48 * k, 32 * k, null);
                graphics.drawImage(bufferedimage, 44 * k, 52 * k, 40 * k, 64 * k, 40 * k, 20 * k, 44 * k, 32 * k, null);
                graphics.drawImage(bufferedimage, 48 * k, 52 * k, 44 * k, 64 * k, 52 * k, 20 * k, 56 * k, 32 * k, null);
            }

            graphics.dispose();
            this.imageData = ((DataBufferInt) bufferedimage.getRaster().getDataBuffer()).getData();
            this.setAreaOpaque(0, 0, 32 * k, 16 * k);
            this.setAreaTransparent(32 * k, 0, 64 * k, 32 * k);
            this.setAreaOpaque(0, 16 * k, 64 * k, 32 * k);
            this.setAreaTransparent(0, 32 * k, 16 * k, 48 * k);
            this.setAreaTransparent(16 * k, 32 * k, 40 * k, 48 * k);
            this.setAreaTransparent(40 * k, 32 * k, 56 * k, 48 * k);
            this.setAreaTransparent(0, 48 * k, 16 * k, 64 * k);
            this.setAreaOpaque(16 * k, 48 * k, 48 * k, 64 * k);
            this.setAreaTransparent(48 * k, 48 * k, 64 * k, 64 * k);
            return bufferedimage;
        }
    }

    public void skinAvailable() {
    }

    private void setAreaTransparent(int startX, int startY, int endX, int endY) {
        if (!this.hasTransparency(startX, startY, endX, endY)) {
            for (int i = startX; i < endX; ++i) {
                for (int j = startY; j < endY; ++j) {
                    this.imageData[i + j * this.imageWidth] &= 16777215;
                }
            }
        }
    }

    private void setAreaOpaque(int x, int y, int width, int height) {
        for (int i = x; i < width; ++i) {
            for (int j = y; j < height; ++j) {
                this.imageData[i + j * this.imageWidth] |= -16777216;
            }
        }
    }

    private boolean hasTransparency(int startX, int startY, int endX, int endY) {
        for (int i = startX; i < endX; ++i) {
            for (int j = startY; j < endY; ++j) {
                int k = this.imageData[i + j * this.imageWidth];

                if ((k >> 24 & 255) < 128) {
                    return true;
                }
            }
        }

        return false;
    }
}
