package net.minecraft.client.renderer.texture;

import net.minecraft.client.Minecraft;
import net.minecraft.util.BlockPos;
import net.minecraft.util.MathHelper;
import net.minecraft.world.World;

public class TextureCompass extends TextureAtlasSprite {
    public static String locationSprite;
    public double currentAngle;
    public double angleDelta;

    public TextureCompass(String iconName) {
        super(iconName);
        locationSprite = iconName;
    }

    public void updateAnimation() {
        Minecraft minecraft = Minecraft.getMinecraft();

        if (minecraft.theWorld != null && minecraft.thePlayer != null) {
            this.updateCompass(minecraft.theWorld, minecraft.thePlayer.posX, minecraft.thePlayer.posZ, minecraft.thePlayer.rotationYaw, false, false);
        } else {
            this.updateCompass(null, 0.0D, 0.0D, 0.0D, true, false);
        }
    }

    public void updateCompass(World worldIn, double x, double z, double yaw, boolean randomYaw, boolean reset) {
        if (!this.framesTextureData.isEmpty()) {
            double d0 = 0.0D;

            if (worldIn != null && !randomYaw) {
                BlockPos blockpos = worldIn.getSpawnPoint();
                double d1 = (double) blockpos.getX() - x;
                double d2 = (double) blockpos.getZ() - z;
                yaw = yaw % 360.0D;
                d0 = -((yaw - 90.0D) * Math.PI / 180.0D - Math.atan2(d2, d1));

                if (!worldIn.provider.isSurfaceWorld()) {
                    d0 = Math.random() * Math.PI * 2.0D;
                }
            }

            if (reset) {
                this.currentAngle = d0;
            } else {
                double d3;

                for (d3 = d0 - this.currentAngle; d3 < -Math.PI; d3 += (Math.PI * 2D)) {
                }

                while (d3 >= Math.PI) {
                    d3 -= (Math.PI * 2D);
                }

                d3 = MathHelper.clamp_double(d3, -1.0D, 1.0D);
                this.angleDelta += d3 * 0.1D;
                this.angleDelta *= 0.8D;
                this.currentAngle += this.angleDelta;
            }

            int i;

            for (i = (int) ((this.currentAngle / (Math.PI * 2D) + 1.0D) * (double) this.framesTextureData.size()) % this.framesTextureData.size(); i < 0; i = (i + this.framesTextureData.size()) % this.framesTextureData.size()) {
            }

            if (i != this.frameCounter) {
                this.frameCounter = i;
                TextureUtil.uploadTextureMipmap(this.framesTextureData.get(this.frameCounter), this.width, this.height, this.originX, this.originY, false, false);
            }
        }
    }
}
