package net.minecraft.client.renderer.texture;

import net.minecraft.client.Minecraft;
import net.minecraft.client.renderer.GLAllocation;
import net.minecraft.client.renderer.GlStateManager;
import net.minecraft.client.resources.IResourceManager;
import net.minecraft.src.Config;
import net.minecraft.util.ResourceLocation;
import net.optifine.Mipmaps;
import org.apache.commons.io.IOUtils;
import org.lwjgl.opengl.GL11;
import org.lwjgl.opengl.GL12;
import org.lwjgl.opengl.GL14;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.io.InputStream;
import java.nio.ByteBuffer;
import java.nio.IntBuffer;

public class TextureUtil {
    public static final DynamicTexture missingTexture = new DynamicTexture(16, 16);
    public static final int[] missingTextureData = missingTexture.getTextureData();
    private static final IntBuffer dataBuffer = GLAllocation.createDirectIntBuffer(4194304);
    private static final int[] dataArray = new int[4194304];

    static {
        int[] aint = new int[]{-524040, -524040, -524040, -524040, -524040, -524040, -524040, -524040};
        int[] aint1 = new int[]{-16777216, -16777216, -16777216, -16777216, -16777216, -16777216, -16777216, -16777216};
        int k = aint.length;

        for (int l = 0; l < 16; ++l) {
            System.arraycopy(l < k ? aint : aint1, 0, missingTextureData, 16 * l, k);
            System.arraycopy(l < k ? aint1 : aint, 0, missingTextureData, 16 * l + k, k);
        }

        missingTexture.updateDynamicTexture();
    }

    public static int glGenTextures() {
        return GlStateManager.generateTexture();
    }

    public static void deleteTexture(int textureId) {
        GlStateManager.deleteTexture(textureId);
    }

    public static void uploadTextureImage(int textureId, BufferedImage texture) {
        uploadTextureImageAllocate(textureId, texture, false, false);
    }

    public static void uploadTexture(int textureId, int[] textureData, int width, int height) {
        bindTexture(textureId);
        uploadTextureSub(0, textureData, width, height, 0, 0, false, false, false);
    }

    public static int[][] generateMipmapData(int level, int width, int[][] frameData) {
        int[][] aint = new int[level + 1][];
        aint[0] = frameData[0];

        if (level > 0) {
            boolean flag = false;

            for (int i = 0; i < frameData[0].length; ++i) {
                if (frameData[0][i] >> 24 == 0) {
                    flag = true;
                    break;
                }
            }

            for (int l1 = 1; l1 <= level; ++l1) {
                if (frameData[l1] != null) {
                    aint[l1] = frameData[l1];
                } else {
                    int[] aint1 = aint[l1 - 1];
                    int[] aint2 = new int[aint1.length >> 2];
                    int j = width >> l1;
                    int k = aint2.length / j;
                    int l = j << 1;

                    for (int i1 = 0; i1 < j; ++i1) {
                        for (int j1 = 0; j1 < k; ++j1) {
                            int k1 = 2 * (i1 + j1 * l);
                            aint2[i1 + j1 * j] = blendColors(aint1[k1], aint1[k1 + 1], aint1[k1 + l], aint1[k1 + 1 + l], flag);
                        }
                    }

                    aint[l1] = aint2;
                }
            }
        }

        return aint;
    }

    private static int blendColors(int color1, int color2, int color3, int color4, boolean ignored) {
        return Mipmaps.alphaBlend(color1, color2, color3, color4);
    }

    private static int blendColorComponent(int color1, int color2, int color3, int color4, int shift) {
        float f = (float) Math.pow((float) (color1 >> shift & 255) / 255.0F, 2.2D);
        float f1 = (float) Math.pow((float) (color2 >> shift & 255) / 255.0F, 2.2D);
        float f2 = (float) Math.pow((float) (color3 >> shift & 255) / 255.0F, 2.2D);
        float f3 = (float) Math.pow((float) (color4 >> shift & 255) / 255.0F, 2.2D);
        float f4 = (float) Math.pow((double) (f + f1 + f2 + f3) * 0.25D, 0.45454545454545453D);
        return (int) ((double) f4 * 255.0D);
    }

    public static void uploadTextureMipmap(int[][] mipmapData, int width, int height, int x, int y, boolean blur, boolean clamp) {
        for (int i = 0; i < mipmapData.length; ++i) {
            int[] aint = mipmapData[i];
            uploadTextureSub(i, aint, width >> i, height >> i, x >> i, y >> i, blur, clamp, mipmapData.length > 1);
        }
    }

    private static void uploadTextureSub(int level, int[] data, int width, int height, int x, int y, boolean blur, boolean clamp, boolean mipmap) {
        int i = 4194304 / width;
        setTextureBlurMipmap(blur, mipmap);
        setTextureClamped(clamp);
        int j;

        for (int k = 0; k < width * height; k += width * j) {
            int l = k / width;
            j = Math.min(i, height - l);
            int i1 = width * j;
            copyToBufferPos(data, k, i1);
            GL11.glTexSubImage2D(GL11.GL_TEXTURE_2D, level, x, y + l, width, j, GL12.GL_BGRA, GL12.GL_UNSIGNED_INT_8_8_8_8_REV, dataBuffer);
        }
    }

    public static int uploadTextureImageAllocate(int textureId, BufferedImage texture, boolean blur, boolean clamp) {
        allocateTexture(textureId, texture.getWidth(), texture.getHeight());
        return uploadTextureImageSub(textureId, texture, 0, 0, blur, clamp);
    }

    public static void allocateTexture(int textureId, int width, int height) {
        allocateTextureImpl(textureId, 0, width, height);
    }

    public static void allocateTextureImpl(int glTextureId, int mipmapLevels, int width, int height) {
        Object object = TextureUtil.class;

        synchronized (object) {
            deleteTexture(glTextureId);
            bindTexture(glTextureId);
        }

        if (mipmapLevels >= 0) {
            GL11.glTexParameteri(GL11.GL_TEXTURE_2D, GL12.GL_TEXTURE_MAX_LEVEL, mipmapLevels);
            GL11.glTexParameterf(GL11.GL_TEXTURE_2D, GL12.GL_TEXTURE_MIN_LOD, 0.0F);
            GL11.glTexParameterf(GL11.GL_TEXTURE_2D, GL12.GL_TEXTURE_MAX_LOD, (float) mipmapLevels);
            GL11.glTexParameterf(GL11.GL_TEXTURE_2D, GL14.GL_TEXTURE_LOD_BIAS, 0.0F);
        }

        for (int i = 0; i <= mipmapLevels; ++i) {
            GL11.glTexImage2D(GL11.GL_TEXTURE_2D, i, GL11.GL_RGBA, width >> i, height >> i, 0, GL12.GL_BGRA, GL12.GL_UNSIGNED_INT_8_8_8_8_REV, (ByteBuffer) null);
        }
    }

    public static int uploadTextureImageSub(int textureId, BufferedImage buffer, int x, int y, boolean blur, boolean clamp) {
        bindTexture(textureId);
        uploadTextureImageSubImpl(buffer, x, y, blur, clamp);
        return textureId;
    }

    private static void uploadTextureImageSubImpl(BufferedImage buffer, int x, int y, boolean blur, boolean clamp) {
        int i = buffer.getWidth();
        int j = buffer.getHeight();
        int k = 4194304 / i;
        int[] aint = dataArray;
        setTextureBlurred(blur);
        setTextureClamped(clamp);

        for (int l = 0; l < i * j; l += i * k) {
            int i1 = l / i;
            int j1 = Math.min(k, j - i1);
            int k1 = i * j1;
            buffer.getRGB(0, i1, i, j1, aint, 0, i);
            copyToBuffer(aint, k1);
            GL11.glTexSubImage2D(GL11.GL_TEXTURE_2D, 0, x, y + i1, i, j1, GL12.GL_BGRA, GL12.GL_UNSIGNED_INT_8_8_8_8_REV, dataBuffer);
        }
    }

    public static void setTextureClamped(boolean clamp) {
        if (clamp) {
            GL11.glTexParameteri(GL11.GL_TEXTURE_2D, GL11.GL_TEXTURE_WRAP_S, GL12.GL_CLAMP_TO_EDGE);
            GL11.glTexParameteri(GL11.GL_TEXTURE_2D, GL11.GL_TEXTURE_WRAP_T, GL12.GL_CLAMP_TO_EDGE);
        } else {
            GL11.glTexParameteri(GL11.GL_TEXTURE_2D, GL11.GL_TEXTURE_WRAP_S, GL11.GL_REPEAT);
            GL11.glTexParameteri(GL11.GL_TEXTURE_2D, GL11.GL_TEXTURE_WRAP_T, GL11.GL_REPEAT);
        }
    }

    private static void setTextureBlurred(boolean blur) {
        setTextureBlurMipmap(blur, false);
    }

    public static void setTextureBlurMipmap(boolean blur, boolean mipmap) {
        if (blur) {
            GL11.glTexParameteri(GL11.GL_TEXTURE_2D, GL11.GL_TEXTURE_MIN_FILTER, mipmap ? GL11.GL_LINEAR_MIPMAP_LINEAR : GL11.GL_LINEAR);
            GL11.glTexParameteri(GL11.GL_TEXTURE_2D, GL11.GL_TEXTURE_MAG_FILTER, GL11.GL_LINEAR);
        } else {
            int i = Config.getMipmapType();
            GL11.glTexParameteri(GL11.GL_TEXTURE_2D, GL11.GL_TEXTURE_MIN_FILTER, mipmap ? i : GL11.GL_NEAREST);
            GL11.glTexParameteri(GL11.GL_TEXTURE_2D, GL11.GL_TEXTURE_MAG_FILTER, GL11.GL_NEAREST);
        }
    }

    private static void copyToBuffer(int[] data, int length) {
        copyToBufferPos(data, 0, length);
    }

    private static void copyToBufferPos(int[] data, int start, int length) {
        int[] aint = data;

        if (Minecraft.getMinecraft().gameSettings.anaglyph) {
            aint = updateAnaglyph(data);
        }

        dataBuffer.clear();
        dataBuffer.put(aint, start, length);
        dataBuffer.position(0).limit(length);
    }

    static void bindTexture(int glTextureId) {
        GlStateManager.bindTexture(glTextureId);
    }

    public static int[] readImageData(IResourceManager resourceManager, ResourceLocation imageLocation) throws IOException {
        BufferedImage bufferedimage = readBufferedImage(resourceManager.getResource(imageLocation).getInputStream());

        if (bufferedimage == null) {
            return null;
        } else {
            int i = bufferedimage.getWidth();
            int j = bufferedimage.getHeight();
            int[] aint = new int[i * j];
            bufferedimage.getRGB(0, 0, i, j, aint, 0, i);
            return aint;
        }
    }

    public static BufferedImage readBufferedImage(InputStream imageStream) throws IOException {
        if (imageStream == null) {
            return null;
        } else {
            BufferedImage bufferedimage;

            try {
                bufferedimage = ImageIO.read(imageStream);
            } finally {
                IOUtils.closeQuietly(imageStream);
            }

            return bufferedimage;
        }
    }

    public static int[] updateAnaglyph(int[] data) {
        int[] aint = new int[data.length];

        for (int i = 0; i < data.length; ++i) {
            aint[i] = anaglyphColor(data[i]);
        }

        return aint;
    }

    public static int anaglyphColor(int color) {
        int i = color >> 24 & 255;
        int j = color >> 16 & 255;
        int k = color >> 8 & 255;
        int l = color & 255;
        int i1 = (j * 30 + k * 59 + l * 11) / 100;
        int j1 = (j * 30 + k * 70) / 100;
        int k1 = (j * 30 + l * 70) / 100;
        return i << 24 | i1 << 16 | j1 << 8 | k1;
    }

    public static void processPixelValues(int[] data, int width, int height) {
        int[] aint = new int[width];
        int i = height / 2;

        for (int j = 0; j < i; ++j) {
            System.arraycopy(data, j * width, aint, 0, width);
            System.arraycopy(data, (height - 1 - j) * width, data, j * width, width);
            System.arraycopy(aint, 0, data, (height - 1 - j) * width, width);
        }
    }
}
