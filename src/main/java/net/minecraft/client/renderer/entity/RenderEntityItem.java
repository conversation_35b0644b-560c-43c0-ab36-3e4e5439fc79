package net.minecraft.client.renderer.entity;

import net.bloom.bloomclient.features.module.modules.render.ModuleItemPhysics;
import net.minecraft.client.renderer.GlStateManager;
import net.minecraft.client.renderer.block.model.ItemCameraTransforms;
import net.minecraft.client.renderer.texture.TextureMap;
import net.minecraft.client.resources.model.IBakedModel;
import net.minecraft.entity.item.EntityItem;
import net.minecraft.item.Item;
import net.minecraft.item.ItemStack;
import net.minecraft.util.MathHelper;
import net.minecraft.util.ResourceLocation;
import org.lwjgl.opengl.GL11;

import java.util.Random;

public class RenderEntityItem extends Render<EntityItem> {
    private final RenderItem itemRenderer;
    private final Random random = new Random();

    public RenderEntityItem(RenderManager renderManagerIn, RenderItem itemRendererIn) {
        super(renderManagerIn);
        this.itemRenderer = itemRendererIn;
        this.shadowSize = 0.15F;
        this.shadowOpaque = 0.75F;
    }

    // Override by Bloom Client
    private int transformModelCount(EntityItem itemIn, double x, double y, double z, float partialTicks, IBakedModel model) {
        ItemStack itemstack = itemIn.getEntityItem();

        if (itemstack == null)
            return 0;

        Item item = itemstack.getItem();

        if (item == null)
            return 0;

        GL11.glEnable(GL11.GL_CULL_FACE);
        GL11.glCullFace(GL11.GL_BACK);

        String mode = ModuleItemPhysics.INSTANCE.getMode().get();
        boolean isPhysicState = ModuleItemPhysics.INSTANCE.getState() && !mode.equalsIgnoreCase("none");
        float weight = (isPhysicState) ? ModuleItemPhysics.INSTANCE.getWeight().get() : 0f;

        boolean isGui3D = model.isGui3d();
        int count = this.getModelCount(itemstack);
        float age = itemIn.getAge() + partialTicks;
        float f1 = MathHelper.sin(age / 10.0F + itemIn.hoverStart) * 0.1F + 0.1F;

        if (isPhysicState)
            f1 = 0f;

        float f2 = model.getItemCameraTransforms().getTransform(ItemCameraTransforms.TransformType.GROUND).scale.y;

        if (isPhysicState) {
            GlStateManager.translate(x, y, z);
        } else {
            GlStateManager.translate(x, y + f1 + 0.25F * f2, z);
        }

        if (isGui3D) {
            GlStateManager.translate(0, 0, -0.08);
        } else {
            GlStateManager.translate(0, 0, -0.04);
        }

        if (isGui3D || this.renderManager.options != null) {
            float rotationYaw = (age / 20.0F + itemIn.hoverStart) * (180F / (float) Math.PI);

            rotationYaw *= ModuleItemPhysics.INSTANCE.getRotationSpeed().get() * (1.0F + Math.min(age / 360.0F, 1.0F));

            if (isPhysicState) {
                if (itemIn.onGround) {
                    GL11.glRotatef(90.0f, 1.0f, 0.0f, 0.0f);

                    if (mode.equalsIgnoreCase("flat"))
                        GL11.glRotatef(itemIn.rotationYaw, 0.0f, 0.0f, 1.0f);
                    else
                        GL11.glRotatef(itemIn.rotationYaw, 0.0f, 1.0f, 0.6f);
                } else {
                    for (int a = 0; a < 7; ++a)
                        GL11.glRotatef(rotationYaw, weight, weight, 1.35f);
                }
            } else {
                GlStateManager.rotate(rotationYaw, 0.0F, 1.0F, 0.0F);
            }
        }

        if (!isGui3D) {
            float offsetX = -0.0F * (float) (count - 1) * 0.5F;
            float offsetY = -0.0F * (float) (count - 1) * 0.5F;
            float offsetZ = -0.09375F * (float) (count - 1) * 0.5F;
            GlStateManager.translate(offsetX, offsetY, offsetZ);
        }

        GL11.glDisable(GL11.GL_CULL_FACE);

        if (ModuleItemPhysics.INSTANCE.getState()) {
            float scale = ModuleItemPhysics.INSTANCE.getScale().get();
            GlStateManager.scale(scale, scale, scale);
            GlStateManager.scale(1f, 1f, 1f);
        }

        GlStateManager.color(1.0F, 1.0F, 1.0F, 1.0F);
        return count;
    }

    private int getModelCount(ItemStack stack) {
        int i = 1;

        if (stack.stackSize > 48) {
            i = 5;
        } else if (stack.stackSize > 32) {
            i = 4;
        } else if (stack.stackSize > 16) {
            i = 3;
        } else if (stack.stackSize > 1) {
            i = 2;
        }

        return i;
    }

    public void doRender(EntityItem entity, double x, double y, double z, float entityYaw, float partialTicks) {
        ItemStack itemstack = entity.getEntityItem();
        this.random.setSeed(187L);
        boolean flag = false;

        if (this.bindEntityTexture(entity)) {
            this.renderManager.renderEngine.getTexture(this.getEntityTexture(entity)).setBlurMipmap(false, false);
            flag = true;
        }

        GlStateManager.enableRescaleNormal();
        GlStateManager.alphaFunc(516, 0.1F);
        GlStateManager.enableBlend();
        GlStateManager.tryBlendFuncSeparate(770, 771, 1, 0);
        GlStateManager.pushMatrix();
        IBakedModel ibakedmodel = this.itemRenderer.getItemModelMesher().getItemModel(itemstack);
        int i = this.transformModelCount(entity, x, y, z, partialTicks, ibakedmodel);

        for (int j = 0; j < i; ++j) {
            if (ibakedmodel.isGui3d()) {
                GlStateManager.pushMatrix();

                if (j > 0) {
                    float f = (this.random.nextFloat() * 2.0F - 1.0F) * 0.15F;
                    float f1 = (this.random.nextFloat() * 2.0F - 1.0F) * 0.15F;
                    float f2 = (this.random.nextFloat() * 2.0F - 1.0F) * 0.15F;
                    GlStateManager.translate(f, f1, f2);
                }

                GlStateManager.scale(0.5F, 0.5F, 0.5F);
                ibakedmodel.getItemCameraTransforms().applyTransform(ItemCameraTransforms.TransformType.GROUND);
                this.itemRenderer.renderItem(itemstack, ibakedmodel);
                GlStateManager.popMatrix();
            } else {
                GlStateManager.pushMatrix();
                ibakedmodel.getItemCameraTransforms().applyTransform(ItemCameraTransforms.TransformType.GROUND);
                this.itemRenderer.renderItem(itemstack, ibakedmodel);
                GlStateManager.popMatrix();
                float f3 = ibakedmodel.getItemCameraTransforms().ground.scale.x;
                float f4 = ibakedmodel.getItemCameraTransforms().ground.scale.y;
                float f5 = ibakedmodel.getItemCameraTransforms().ground.scale.z;
                GlStateManager.translate(0.0F * f3, 0.0F * f4, 0.046875F * f5);
            }
        }

        GlStateManager.popMatrix();
        GlStateManager.disableRescaleNormal();
        GlStateManager.disableBlend();
        this.bindEntityTexture(entity);

        if (flag) {
            this.renderManager.renderEngine.getTexture(this.getEntityTexture(entity)).restoreLastBlurMipmap();
        }

        super.doRender(entity, x, y, z, entityYaw, partialTicks);
    }

    protected ResourceLocation getEntityTexture(EntityItem entity) {
        return TextureMap.locationBlocksTexture;
    }
}
