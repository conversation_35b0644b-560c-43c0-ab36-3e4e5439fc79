package net.minecraft.client.audio;

import com.google.common.collect.Maps;
import net.minecraft.util.RegistrySimple;
import net.minecraft.util.ResourceLocation;

import java.util.Map;

public class SoundRegistry extends RegistrySimple<ResourceLocation, SoundEventAccessorComposite> {
    private Map<ResourceLocation, SoundEventAccessorComposite> soundRegistry;

    protected Map<ResourceLocation, SoundEventAccessorComposite> createUnderlyingMap() {
        this.soundRegistry = Maps.newHashMap();
        return this.soundRegistry;
    }

    public void registerSound(SoundEventAccessorComposite registry) {
        this.putObject(registry.getSoundEventLocation(), registry);
    }

    public void clearMap() {
        this.soundRegistry.clear();
    }
}
