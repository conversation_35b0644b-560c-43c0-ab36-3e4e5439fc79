package net.minecraft.client.gui.spectator.categories;

import com.google.common.base.MoreObjects;
import net.minecraft.client.gui.spectator.ISpectatorMenuObject;
import net.minecraft.client.gui.spectator.SpectatorMenu;

import java.util.List;

public class SpectatorDetails {
    private final List<ISpectatorMenuObject> items;
    private final int selectedSlot;

    public SpectatorDetails(List<ISpectatorMenuObject> itemsIn, int selectedIndex) {
        this.items = itemsIn;
        this.selectedSlot = selectedIndex;
    }

    public ISpectatorMenuObject getObject(int index) {
        return index >= 0 && index < this.items.size() ? MoreObjects.firstNonNull(this.items.get(index), SpectatorMenu.EMPTY_SLOT) : SpectatorMenu.EMPTY_SLOT;
    }

    public int getSelectedSlot() {
        return this.selectedSlot;
    }
}
