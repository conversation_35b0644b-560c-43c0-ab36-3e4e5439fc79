package net.minecraft.client.gui;

import com.google.common.collect.Lists;
import net.minecraft.client.Minecraft;
import net.minecraft.client.renderer.GlStateManager;
import net.minecraft.client.resources.I18n;

import java.util.List;

public class GuiLabel extends Gui {
    public final int x;
    public final int id;
    protected final int width;
    protected final int height;
    private final List<String> labels;
    private final boolean labelBgEnabled;
    private final int textColor;
    private final int backColor;
    private final int ulColor;
    private final int brColor;
    private final MinecraftFontRenderer minecraftFontRenderer;
    private final int border;
    public int y;
    public boolean visible = true;
    private boolean centered;

    public GuiLabel(MinecraftFontRenderer minecraftFontRendererObj, int labelId, int xIn, int yIn, int widthIn, int heightIn, int colorIn) {
        this.minecraftFontRenderer = minecraftFontRendererObj;
        this.id = labelId;
        this.x = xIn;
        this.y = yIn;
        this.width = widthIn;
        this.height = heightIn;
        this.labels = Lists.newArrayList();
        this.centered = false;
        this.labelBgEnabled = false;
        this.textColor = colorIn;
        this.backColor = -1;
        this.ulColor = -1;
        this.brColor = -1;
        this.border = 0;
    }

    public void addLine(String id) {
        this.labels.add(I18n.format(id));
    }

    public void setCentered() {
        this.centered = true;
    }

    public void drawLabel(Minecraft mc, int mouseX, int mouseY) {
        if (this.visible) {
            GlStateManager.enableBlend();
            GlStateManager.tryBlendFuncSeparate(770, 771, 1, 0);
            this.drawLabelBackground(mc, mouseX, mouseY);
            int i = this.y + this.height / 2 + this.border / 2;
            int j = i - this.labels.size() * 10 / 2;

            for (int k = 0; k < this.labels.size(); ++k) {
                if (this.centered) {
                    this.drawCenteredString(this.minecraftFontRenderer, this.labels.get(k), this.x + this.width / 2, j + k * 10, this.textColor);
                } else {
                    this.drawString(this.minecraftFontRenderer, this.labels.get(k), this.x, j + k * 10, this.textColor);
                }
            }
        }
    }

    protected void drawLabelBackground(Minecraft mcIn, int mouseX, int mouseY) {
        if (this.labelBgEnabled) {
            int i = this.width + this.border * 2;
            int j = this.height + this.border * 2;
            int k = this.x - this.border;
            int l = this.y - this.border;
            drawRect(k, l, k + i, l + j, this.backColor);
            this.drawHorizontalLine(k, k + i, l, this.ulColor);
            this.drawHorizontalLine(k, k + i, l + j, this.brColor);
            this.drawVerticalLine(k, l, l + j, this.ulColor);
            this.drawVerticalLine(k + i, l, l + j, this.brColor);
        }
    }
}
