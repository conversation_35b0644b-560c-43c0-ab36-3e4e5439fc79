package net.minecraft.client.gui;

import net.minecraft.client.resources.I18n;
import net.minecraft.util.ChatComponentText;
import net.minecraft.util.ChatComponentTranslation;
import net.minecraft.util.IChatComponent;
import net.minecraft.world.WorldSettings;

public class GuiShareToLan extends GuiScreen {
    private final GuiScreen lastScreen;
    private GuiButton allowCheatsButton;
    private GuiButton gameModeButton;
    private String gameMode = "survival";
    private boolean allowCheats;

    public GuiShareToLan(GuiScreen lastScreenIn) {
        this.lastScreen = lastScreenIn;
    }

    public void initGui() {
        this.buttonList.clear();
        this.buttonList.add(new GuiButton(101, this.width / 2 - 155, this.height - 28, 150, 20, I18n.format("lanServer.start")));
        this.buttonList.add(new GuiButton(102, this.width / 2 + 5, this.height - 28, 150, 20, I18n.format("gui.cancel")));
        this.buttonList.add(this.gameModeButton = new GuiButton(104, this.width / 2 - 155, 100, 150, 20, I18n.format("selectWorld.gameMode")));
        this.buttonList.add(this.allowCheatsButton = new GuiButton(103, this.width / 2 + 5, 100, 150, 20, I18n.format("selectWorld.allowCommands")));
        this.updateDisplayNames();
    }

    private void updateDisplayNames() {
        this.gameModeButton.displayString = I18n.format("selectWorld.gameMode") + " " + I18n.format("selectWorld.gameMode." + this.gameMode);
        this.allowCheatsButton.displayString = I18n.format("selectWorld.allowCommands") + " ";

        if (this.allowCheats) {
            this.allowCheatsButton.displayString = this.allowCheatsButton.displayString + I18n.format("options.on");
        } else {
            this.allowCheatsButton.displayString = this.allowCheatsButton.displayString + I18n.format("options.off");
        }
    }

    protected void actionPerformed(GuiButton button) {
        if (button.id == 102) {
            this.mc.displayGuiScreen(this.lastScreen);
        } else if (button.id == 104) {
            switch (this.gameMode) {
                case "spectator":
                    this.gameMode = "creative";
                    break;
                case "creative":
                    this.gameMode = "adventure";
                    break;
                case "adventure":
                    this.gameMode = "survival";
                    break;
                default:
                    this.gameMode = "spectator";
                    break;
            }

            this.updateDisplayNames();
        } else if (button.id == 103) {
            this.allowCheats = !this.allowCheats;
            this.updateDisplayNames();
        } else if (button.id == 101) {
            this.mc.displayGuiScreen(null);
            String s = this.mc.getIntegratedServer().shareToLAN(WorldSettings.GameType.getByName(this.gameMode), this.allowCheats);
            IChatComponent ichatcomponent;

            if (s != null) {
                ichatcomponent = new ChatComponentTranslation("commands.publish.started", s);
            } else {
                ichatcomponent = new ChatComponentText("commands.publish.failed");
            }

            this.mc.ingameGUI.getChatGUI().printChatMessage(ichatcomponent);
        }
    }

    public void drawScreen(int mouseX, int mouseY, float partialTicks) {
        this.drawDefaultBackground();
        this.drawCenteredString(this.minecraftFontRendererObj, I18n.format("lanServer.title"), this.width / 2, 50, 16777215);
        this.drawCenteredString(this.minecraftFontRendererObj, I18n.format("lanServer.otherPlayers"), this.width / 2, 82, 16777215);
        super.drawScreen(mouseX, mouseY, partialTicks);
    }
}
