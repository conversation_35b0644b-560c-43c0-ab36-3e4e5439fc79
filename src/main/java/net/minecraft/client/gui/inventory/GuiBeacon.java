package net.minecraft.client.gui.inventory;

import io.netty.buffer.Unpooled;
import net.minecraft.client.Minecraft;
import net.minecraft.client.gui.GuiButton;
import net.minecraft.client.renderer.GlStateManager;
import net.minecraft.client.renderer.RenderHelper;
import net.minecraft.client.resources.I18n;
import net.minecraft.entity.player.InventoryPlayer;
import net.minecraft.init.Items;
import net.minecraft.inventory.ContainerBeacon;
import net.minecraft.inventory.IInventory;
import net.minecraft.item.ItemStack;
import net.minecraft.network.PacketBuffer;
import net.minecraft.network.play.client.C17PacketCustomPayload;
import net.minecraft.potion.Potion;
import net.minecraft.tileentity.TileEntityBeacon;
import net.minecraft.util.ResourceLocation;

public class GuiBeacon extends GuiContainer {
    private static final ResourceLocation beaconGuiTextures = new ResourceLocation("textures/gui/container/beacon.png");
    public final IInventory tileBeacon;
    private GuiBeacon.ConfirmButton beaconConfirmButton;
    private boolean buttonsNotDrawn;

    public GuiBeacon(InventoryPlayer playerInventory, IInventory tileBeaconIn) {
        super(new ContainerBeacon(playerInventory, tileBeaconIn));
        this.tileBeacon = tileBeaconIn;
        this.xSize = 230;
        this.ySize = 219;
    }

    public void initGui() {
        super.initGui();
        this.buttonList.add(this.beaconConfirmButton = new GuiBeacon.ConfirmButton(-1, this.guiLeft + 164, this.guiTop + 107));
        this.buttonList.add(new GuiBeacon.CancelButton(-2, this.guiLeft + 190, this.guiTop + 107));
        this.buttonsNotDrawn = true;
        this.beaconConfirmButton.enabled = false;
    }

    public void updateScreen() {
        super.updateScreen();
        int i = this.tileBeacon.getField(0);
        int j = this.tileBeacon.getField(1);
        int k = this.tileBeacon.getField(2);

        if (this.buttonsNotDrawn && i >= 0) {
            this.buttonsNotDrawn = false;

            for (int l = 0; l <= 2; ++l) {
                int i1 = TileEntityBeacon.effectsList[l].length;
                int j1 = i1 * 22 + (i1 - 1) * 2;

                for (int k1 = 0; k1 < i1; ++k1) {
                    int l1 = TileEntityBeacon.effectsList[l][k1].id;
                    GuiBeacon.PowerButton guibeacon$powerbutton = new GuiBeacon.PowerButton(l << 8 | l1, this.guiLeft + 76 + k1 * 24 - j1 / 2, this.guiTop + 22 + l * 25, l1, l);
                    this.buttonList.add(guibeacon$powerbutton);

                    if (l >= i) {
                        guibeacon$powerbutton.enabled = false;
                    } else if (l1 == j) {
                        guibeacon$powerbutton.setSelected(true);
                    }
                }
            }

            int i2 = 3;
            int j2 = TileEntityBeacon.effectsList[i2].length + 1;
            int k2 = j2 * 22 + (j2 - 1) * 2;

            for (int l2 = 0; l2 < j2 - 1; ++l2) {
                int i3 = TileEntityBeacon.effectsList[i2][l2].id;
                GuiBeacon.PowerButton guibeacon$powerbutton2 = new GuiBeacon.PowerButton(i2 << 8 | i3, this.guiLeft + 167 + l2 * 24 - k2 / 2, this.guiTop + 47, i3, i2);
                this.buttonList.add(guibeacon$powerbutton2);

                if (i2 >= i) {
                    guibeacon$powerbutton2.enabled = false;
                } else if (i3 == k) {
                    guibeacon$powerbutton2.setSelected(true);
                }
            }

            if (j > 0) {
                GuiBeacon.PowerButton guibeacon$powerbutton1 = new GuiBeacon.PowerButton(i2 << 8 | j, this.guiLeft + 167 + (j2 - 1) * 24 - k2 / 2, this.guiTop + 47, j, i2);
                this.buttonList.add(guibeacon$powerbutton1);

                if (i2 >= i) {
                    guibeacon$powerbutton1.enabled = false;
                } else if (j == k) {
                    guibeacon$powerbutton1.setSelected(true);
                }
            }
        }

        this.beaconConfirmButton.enabled = this.tileBeacon.getStackInSlot(0) != null && j > 0;
    }

    protected void actionPerformed(GuiButton button) {
        if (button.id == -2) {
            this.mc.displayGuiScreen(null);
        } else if (button.id == -1) {
            String s = "MC|Beacon";
            PacketBuffer packetbuffer = new PacketBuffer(Unpooled.buffer());
            packetbuffer.writeInt(this.tileBeacon.getField(1));
            packetbuffer.writeInt(this.tileBeacon.getField(2));
            this.mc.getNetHandler().addToSendQueue(new C17PacketCustomPayload(s, packetbuffer));
            this.mc.displayGuiScreen(null);
        } else if (button instanceof GuiBeacon.PowerButton) {
            if (((GuiBeacon.PowerButton) button).isSelected()) {
                return;
            }

            int j = button.id;
            int k = j & 255;
            int i = j >> 8;

            if (i < 3) {
                this.tileBeacon.setField(1, k);
            } else {
                this.tileBeacon.setField(2, k);
            }

            this.buttonList.clear();
            this.initGui();
            this.updateScreen();
        }
    }

    protected void drawGuiContainerForegroundLayer(int mouseX, int mouseY) {
        RenderHelper.disableStandardItemLighting();
        this.drawCenteredString(this.minecraftFontRendererObj, I18n.format("tile.beacon.primary"), 62, 10, 14737632);
        this.drawCenteredString(this.minecraftFontRendererObj, I18n.format("tile.beacon.secondary"), 169, 10, 14737632);

        for (GuiButton guibutton : this.buttonList) {
            if (guibutton.isMouseOver()) {
                guibutton.drawButtonForegroundLayer(mouseX - this.guiLeft, mouseY - this.guiTop);
                break;
            }
        }

        RenderHelper.enableGUIStandardItemLighting();
    }

    protected void drawGuiContainerBackgroundLayer(float partialTicks, int mouseX, int mouseY) {
        GlStateManager.color(1.0F, 1.0F, 1.0F, 1.0F);
        this.mc.getTextureManager().bindTexture(beaconGuiTextures);
        int i = (this.width - this.xSize) / 2;
        int j = (this.height - this.ySize) / 2;
        this.drawTexturedModalRect(i, j, 0, 0, this.xSize, this.ySize);
        this.itemRender.zLevel = 100.0F;
        this.itemRender.renderItemAndEffectIntoGUI(new ItemStack(Items.emerald), i + 42, j + 109);
        this.itemRender.renderItemAndEffectIntoGUI(new ItemStack(Items.diamond), i + 42 + 22, j + 109);
        this.itemRender.renderItemAndEffectIntoGUI(new ItemStack(Items.gold_ingot), i + 42 + 44, j + 109);
        this.itemRender.renderItemAndEffectIntoGUI(new ItemStack(Items.iron_ingot), i + 42 + 66, j + 109);
        this.itemRender.zLevel = 0.0F;
    }

    static class Button extends GuiButton {
        private final ResourceLocation iconTexture;
        private final int iconX;
        private final int iconY;
        private boolean selected;

        protected Button(int buttonId, int x, int y, ResourceLocation iconTextureIn, int iconXIn, int iconYIn) {
            super(buttonId, x, y, 22, 22, "");
            this.iconTexture = iconTextureIn;
            this.iconX = iconXIn;
            this.iconY = iconYIn;
        }

        public void drawButton(Minecraft mc, int mouseX, int mouseY) {
            if (this.visible) {
                mc.getTextureManager().bindTexture(GuiBeacon.beaconGuiTextures);
                GlStateManager.color(1.0F, 1.0F, 1.0F, 1.0F);
                this.hovered = mouseX >= this.xPosition && mouseY >= this.yPosition && mouseX < this.xPosition + this.width && mouseY < this.yPosition + this.height;
                int i = 219;
                int j = 0;

                if (!this.enabled) {
                    j += this.width * 2;
                } else if (this.selected) {
                    j += this.width;
                } else if (this.hovered) {
                    j += this.width * 3;
                }

                this.drawTexturedModalRect(this.xPosition, this.yPosition, j, i, this.width, this.height);

                if (!GuiBeacon.beaconGuiTextures.equals(this.iconTexture)) {
                    mc.getTextureManager().bindTexture(this.iconTexture);
                }

                this.drawTexturedModalRect(this.xPosition + 2, this.yPosition + 2, this.iconX, this.iconY, 18, 18);
            }
        }

        public boolean isSelected() {
            return this.selected;
        }

        public void setSelected(boolean selectedIn) {
            this.selected = selectedIn;
        }
    }

    class CancelButton extends GuiBeacon.Button {
        public CancelButton(int buttonId, int x, int y) {
            super(buttonId, x, y, GuiBeacon.beaconGuiTextures, 112, 220);
        }

        public void drawButtonForegroundLayer(int mouseX, int mouseY) {
            GuiBeacon.this.drawCreativeTabHoveringText(I18n.format("gui.cancel"), mouseX, mouseY);
        }
    }

    class ConfirmButton extends GuiBeacon.Button {
        public ConfirmButton(int buttonId, int x, int y) {
            super(buttonId, x, y, GuiBeacon.beaconGuiTextures, 90, 220);
        }

        public void drawButtonForegroundLayer(int mouseX, int mouseY) {
            GuiBeacon.this.drawCreativeTabHoveringText(I18n.format("gui.done"), mouseX, mouseY);
        }
    }

    class PowerButton extends GuiBeacon.Button {
        private final int potionId;
        private final int tier;

        public PowerButton(int buttonId, int x, int y, int potionId, int tier) {
            super(buttonId, x, y, GuiContainer.inventoryBackground, Potion.potionTypes[potionId].getStatusIconIndex() % 8 * 18, 198 + Potion.potionTypes[potionId].getStatusIconIndex() / 8 * 18);
            this.potionId = potionId;
            this.tier = tier;
        }

        public void drawButtonForegroundLayer(int mouseX, int mouseY) {
            String s = I18n.format(Potion.potionTypes[this.potionId].getName());

            if (this.tier >= 3 && this.potionId != Potion.regeneration.id) {
                s = s + " II";
            }

            GuiBeacon.this.drawCreativeTabHoveringText(s, mouseX, mouseY);
        }
    }
}
