package net.minecraft.client.gui.achievement;

import com.google.common.collect.Lists;
import net.minecraft.client.Minecraft;
import net.minecraft.client.audio.PositionedSoundRecord;
import net.minecraft.client.gui.GuiButton;
import net.minecraft.client.gui.GuiScreen;
import net.minecraft.client.gui.GuiSlot;
import net.minecraft.client.gui.IProgressMeter;
import net.minecraft.client.renderer.GlStateManager;
import net.minecraft.client.renderer.RenderHelper;
import net.minecraft.client.renderer.Tessellator;
import net.minecraft.client.renderer.WorldRenderer;
import net.minecraft.client.renderer.vertex.DefaultVertexFormats;
import net.minecraft.client.resources.I18n;
import net.minecraft.entity.EntityList;
import net.minecraft.item.Item;
import net.minecraft.item.ItemStack;
import net.minecraft.network.play.client.C16PacketClientStatus;
import net.minecraft.stats.StatBase;
import net.minecraft.stats.StatCrafting;
import net.minecraft.stats.StatFileWriter;
import net.minecraft.stats.StatList;
import net.minecraft.util.ResourceLocation;
import org.lwjgl.input.Mouse;

import java.io.IOException;
import java.util.Comparator;
import java.util.List;

public class GuiStats extends GuiScreen implements IProgressMeter {
    protected final GuiScreen parentScreen;
    private final StatFileWriter stats;
    protected String screenTitle = "Select world";
    private GuiStats.StatsGeneral generalStats;
    private GuiStats.StatsItem itemStats;
    private GuiStats.StatsBlock blockStats;
    private GuiStats.StatsMobsList mobStats;
    private GuiSlot displaySlot;
    private boolean doesGuiPauseGame = true;

    public GuiStats(GuiScreen parent, StatFileWriter manager) {
        this.parentScreen = parent;
        this.stats = manager;
    }

    public void initGui() {
        this.screenTitle = I18n.format("gui.stats");
        this.doesGuiPauseGame = true;
        this.mc.getNetHandler().addToSendQueue(new C16PacketClientStatus(C16PacketClientStatus.EnumState.REQUEST_STATS));
    }

    public void handleMouseInput() throws IOException {
        super.handleMouseInput();

        if (this.displaySlot != null) {
            this.displaySlot.handleMouseInput();
        }
    }

    public void initLists() {
        this.generalStats = new GuiStats.StatsGeneral(this.mc);
        this.generalStats.registerScrollButtons(1, 1);
        this.itemStats = new GuiStats.StatsItem(this.mc);
        this.itemStats.registerScrollButtons(1, 1);
        this.blockStats = new GuiStats.StatsBlock(this.mc);
        this.blockStats.registerScrollButtons(1, 1);
        this.mobStats = new GuiStats.StatsMobsList(this.mc);
        this.mobStats.registerScrollButtons(1, 1);
    }

    public void createButtons() {
        this.buttonList.add(new GuiButton(0, this.width / 2 + 4, this.height - 28, 150, 20, I18n.format("gui.done")));
        this.buttonList.add(new GuiButton(1, this.width / 2 - 160, this.height - 52, 80, 20, I18n.format("stat.generalButton")));
        GuiButton guibutton;
        this.buttonList.add(guibutton = new GuiButton(2, this.width / 2 - 80, this.height - 52, 80, 20, I18n.format("stat.blocksButton")));
        GuiButton guibutton1;
        this.buttonList.add(guibutton1 = new GuiButton(3, this.width / 2, this.height - 52, 80, 20, I18n.format("stat.itemsButton")));
        GuiButton guibutton2;
        this.buttonList.add(guibutton2 = new GuiButton(4, this.width / 2 + 80, this.height - 52, 80, 20, I18n.format("stat.mobsButton")));

        if (this.blockStats.getSize() == 0) {
            guibutton.enabled = false;
        }

        if (this.itemStats.getSize() == 0) {
            guibutton1.enabled = false;
        }

        if (this.mobStats.getSize() == 0) {
            guibutton2.enabled = false;
        }
    }

    protected void actionPerformed(GuiButton button) {
        if (button.enabled) {
            if (button.id == 0) {
                this.mc.displayGuiScreen(this.parentScreen);
            } else if (button.id == 1) {
                this.displaySlot = this.generalStats;
            } else if (button.id == 3) {
                this.displaySlot = this.itemStats;
            } else if (button.id == 2) {
                this.displaySlot = this.blockStats;
            } else if (button.id == 4) {
                this.displaySlot = this.mobStats;
            } else {
                this.displaySlot.actionPerformed(button);
            }
        }
    }

    public void drawScreen(int mouseX, int mouseY, float partialTicks) {
        if (this.doesGuiPauseGame) {
            this.drawDefaultBackground();
            this.drawCenteredString(this.minecraftFontRendererObj, I18n.format("multiplayer.downloadingStats"), this.width / 2, this.height / 2, 16777215);
            this.drawCenteredString(this.minecraftFontRendererObj, lanSearchStates[(int) (Minecraft.getSystemTime() / 150L % (long) lanSearchStates.length)], this.width / 2, this.height / 2 + this.minecraftFontRendererObj.FONT_HEIGHT * 2, 16777215);
        } else {
            this.displaySlot.drawScreen(mouseX, mouseY, partialTicks);
            this.drawCenteredString(this.minecraftFontRendererObj, this.screenTitle, this.width / 2, 20, 16777215);
            super.drawScreen(mouseX, mouseY, partialTicks);
        }
    }

    public void doneLoading() {
        if (this.doesGuiPauseGame) {
            this.initLists();
            this.createButtons();
            this.displaySlot = this.generalStats;
            this.doesGuiPauseGame = false;
        }
    }

    public boolean doesGuiPauseGame() {
        return !this.doesGuiPauseGame;
    }

    private void drawStatsScreen(int x, int y, Item itemIn) {
        this.drawButtonBackground(x + 1, y + 1);
        GlStateManager.enableRescaleNormal();
        RenderHelper.enableGUIStandardItemLighting();
        this.itemRender.renderItemIntoGUI(new ItemStack(itemIn, 1, 0), x + 2, y + 2);
        RenderHelper.disableStandardItemLighting();
        GlStateManager.disableRescaleNormal();
    }

    private void drawButtonBackground(int x, int y) {
        this.drawSprite(x, y, 0, 0);
    }

    private void drawSprite(int x, int y, int u, int v) {
        GlStateManager.color(1.0F, 1.0F, 1.0F, 1.0F);
        this.mc.getTextureManager().bindTexture(statIcons);
        Tessellator tessellator = Tessellator.getInstance();
        WorldRenderer worldrenderer = tessellator.getWorldRenderer();
        worldrenderer.begin(7, DefaultVertexFormats.POSITION_TEX);
        worldrenderer.pos(x, y + 18, this.zLevel).tex((float) (u) * 0.0078125F, (float) (v + 18) * 0.0078125F).endVertex();
        worldrenderer.pos(x + 18, y + 18, this.zLevel).tex((float) (u + 18) * 0.0078125F, (float) (v + 18) * 0.0078125F).endVertex();
        worldrenderer.pos(x + 18, y, this.zLevel).tex((float) (u + 18) * 0.0078125F, (float) (v) * 0.0078125F).endVertex();
        worldrenderer.pos(x, y, this.zLevel).tex((float) (u) * 0.0078125F, (float) (v) * 0.0078125F).endVertex();
        tessellator.draw();
    }

    abstract class Stats extends GuiSlot {
        protected int headerPressed = -1;
        protected List<StatCrafting> statsHolder;
        protected Comparator<StatCrafting> statSorter;
        protected int sortColumn = -1;
        protected int sortOrder;

        protected Stats(Minecraft mcIn) {
            super(mcIn, GuiStats.this.width, GuiStats.this.height, 32, GuiStats.this.height - 64, 20);
            this.setShowSelectionBox(false);
            this.setHasListHeader(true, 20);
        }

        protected void elementClicked(int slotIndex, boolean isDoubleClick, int mouseX, int mouseY) {
        }

        protected boolean isSelected(int slotIndex) {
            return false;
        }

        protected void drawBackground() {
            GuiStats.this.drawDefaultBackground();
        }

        protected void drawListHeader(int insideLeft, int insideTop, Tessellator tessellatorIn) {
            if (!Mouse.isButtonDown(0)) {
                this.headerPressed = -1;
            }

            if (this.headerPressed == 0) {
                GuiStats.this.drawSprite(insideLeft + 115 - 18, insideTop + 1, 0, 0);
            } else {
                GuiStats.this.drawSprite(insideLeft + 115 - 18, insideTop + 1, 0, 18);
            }

            if (this.headerPressed == 1) {
                GuiStats.this.drawSprite(insideLeft + 165 - 18, insideTop + 1, 0, 0);
            } else {
                GuiStats.this.drawSprite(insideLeft + 165 - 18, insideTop + 1, 0, 18);
            }

            if (this.headerPressed == 2) {
                GuiStats.this.drawSprite(insideLeft + 215 - 18, insideTop + 1, 0, 0);
            } else {
                GuiStats.this.drawSprite(insideLeft + 215 - 18, insideTop + 1, 0, 18);
            }

            if (this.sortColumn != -1) {
                int i = 79;
                int j = 18;

                if (this.sortColumn == 1) {
                    i = 129;
                } else if (this.sortColumn == 2) {
                    i = 179;
                }

                if (this.sortOrder == 1) {
                    j = 36;
                }

                GuiStats.this.drawSprite(insideLeft + i, insideTop + 1, j, 0);
            }
        }

        protected void clickedHeader(int mouseX, int mouseY) {
            this.headerPressed = -1;

            if (mouseX >= 79 && mouseX < 115) {
                this.headerPressed = 0;
            } else if (mouseX >= 129 && mouseX < 165) {
                this.headerPressed = 1;
            } else if (mouseX >= 179 && mouseX < 215) {
                this.headerPressed = 2;
            }

            if (this.headerPressed >= 0) {
                this.sortByColumn(this.headerPressed);
                this.mc.getSoundHandler().playSound(PositionedSoundRecord.create(new ResourceLocation("gui.button.press"), 1.0F));
            }
        }

        protected final int getSize() {
            return this.statsHolder.size();
        }

        protected final StatCrafting getSlotStat(int index) {
            return this.statsHolder.get(index);
        }

        protected abstract String getHeaderDescriptionId(int state);

        protected void renderStat(StatBase stat, int x, int y, boolean evenSlot) {
            if (stat != null) {
                String s = stat.format(GuiStats.this.stats.readStat(stat));
                GuiStats.this.drawString(GuiStats.this.minecraftFontRendererObj, s, x - GuiStats.this.minecraftFontRendererObj.getStringWidth(s), y + 5, evenSlot ? 16777215 : 9474192);
            } else {
                String s1 = "-";
                GuiStats.this.drawString(GuiStats.this.minecraftFontRendererObj, s1, x - GuiStats.this.minecraftFontRendererObj.getStringWidth(s1), y + 5, evenSlot ? 16777215 : 9474192);
            }
        }

        protected void renderDecorations(int mouseXIn, int mouseYIn) {
            if (mouseYIn >= this.top && mouseYIn <= this.bottom) {
                int i = this.getSlotIndexFromScreenCoords(mouseXIn, mouseYIn);
                int j = this.width / 2 - 92 - 16;

                if (i >= 0) {
                    if (mouseXIn < j + 40 || mouseXIn > j + 40 + 20) {
                        return;
                    }

                    StatCrafting statcrafting = this.getSlotStat(i);
                    this.renderMouseHoverToolTip(statcrafting, mouseXIn, mouseYIn);
                } else {
                    String s;

                    if (mouseXIn >= j + 115 - 18 && mouseXIn <= j + 115) {
                        s = this.getHeaderDescriptionId(0);
                    } else if (mouseXIn >= j + 165 - 18 && mouseXIn <= j + 165) {
                        s = this.getHeaderDescriptionId(1);
                    } else {
                        if (mouseXIn < j + 215 - 18 || mouseXIn > j + 215) {
                            return;
                        }

                        s = this.getHeaderDescriptionId(2);
                    }

                    s = (I18n.format(s)).trim();

                    if (!s.isEmpty()) {
                        int k = mouseXIn + 12;
                        int l = mouseYIn - 12;
                        int i1 = GuiStats.this.minecraftFontRendererObj.getStringWidth(s);
                        GuiStats.this.drawGradientRect(k - 3, l - 3, k + i1 + 3, l + 8 + 3, -1073741824, -1073741824);
                        GuiStats.this.minecraftFontRendererObj.drawStringWithShadow(s, (float) k, (float) l, -1);
                    }
                }
            }
        }

        protected void renderMouseHoverToolTip(StatCrafting statCraft, int mouseX, int mouseY) {
            if (statCraft != null) {
                Item item = statCraft.getItem();
                ItemStack itemstack = new ItemStack(item);
                String s = itemstack.getUnlocalizedName();
                String s1 = (I18n.format(s + ".name")).trim();

                if (!s1.isEmpty()) {
                    int i = mouseX + 12;
                    int j = mouseY - 12;
                    int k = GuiStats.this.minecraftFontRendererObj.getStringWidth(s1);
                    GuiStats.this.drawGradientRect(i - 3, j - 3, i + k + 3, j + 8 + 3, -1073741824, -1073741824);
                    GuiStats.this.minecraftFontRendererObj.drawStringWithShadow(s1, (float) i, (float) j, -1);
                }
            }
        }

        protected void sortByColumn(int headerIndex) {
            if (headerIndex != this.sortColumn) {
                this.sortColumn = headerIndex;
                this.sortOrder = -1;
            } else if (this.sortOrder == -1) {
                this.sortOrder = 1;
            } else {
                this.sortColumn = -1;
                this.sortOrder = 0;
            }

            this.statsHolder.sort(this.statSorter);
        }
    }

    class StatsBlock extends GuiStats.Stats {
        public StatsBlock(Minecraft mcIn) {
            super(mcIn);
            this.statsHolder = Lists.newArrayList();

            for (StatCrafting statcrafting : StatList.objectMineStats) {
                boolean flag = false;
                int i = Item.getIdFromItem(statcrafting.getItem());

                if (GuiStats.this.stats.readStat(statcrafting) > 0) {
                    flag = true;
                } else if (StatList.objectUseStats[i] != null && GuiStats.this.stats.readStat(StatList.objectUseStats[i]) > 0) {
                    flag = true;
                } else if (StatList.objectCraftStats[i] != null && GuiStats.this.stats.readStat(StatList.objectCraftStats[i]) > 0) {
                    flag = true;
                }

                if (flag) {
                    this.statsHolder.add(statcrafting);
                }
            }

            this.statSorter = (p_compare_1_, p_compare_2_) -> {
                int j = Item.getIdFromItem(p_compare_1_.getItem());
                int k = Item.getIdFromItem(p_compare_2_.getItem());
                StatBase statbase = null;
                StatBase statbase1 = null;

                if (StatsBlock.this.sortColumn == 2) {
                    statbase = StatList.mineBlockStatArray[j];
                    statbase1 = StatList.mineBlockStatArray[k];
                } else if (StatsBlock.this.sortColumn == 0) {
                    statbase = StatList.objectCraftStats[j];
                    statbase1 = StatList.objectCraftStats[k];
                } else if (StatsBlock.this.sortColumn == 1) {
                    statbase = StatList.objectUseStats[j];
                    statbase1 = StatList.objectUseStats[k];
                }

                if (statbase != null || statbase1 != null) {
                    if (statbase == null) {
                        return 1;
                    }

                    if (statbase1 == null) {
                        return -1;
                    }

                    int l = GuiStats.this.stats.readStat(statbase);
                    int i1 = GuiStats.this.stats.readStat(statbase1);

                    if (l != i1) {
                        return (l - i1) * StatsBlock.this.sortOrder;
                    }
                }

                return j - k;
            };
        }

        protected void drawListHeader(int insideLeft, int insideTop, Tessellator tessellatorIn) {
            super.drawListHeader(insideLeft, insideTop, tessellatorIn);

            if (this.headerPressed == 0) {
                GuiStats.this.drawSprite(insideLeft + 115 - 18 + 1, insideTop + 1 + 1, 18, 18);
            } else {
                GuiStats.this.drawSprite(insideLeft + 115 - 18, insideTop + 1, 18, 18);
            }

            if (this.headerPressed == 1) {
                GuiStats.this.drawSprite(insideLeft + 165 - 18 + 1, insideTop + 1 + 1, 36, 18);
            } else {
                GuiStats.this.drawSprite(insideLeft + 165 - 18, insideTop + 1, 36, 18);
            }

            if (this.headerPressed == 2) {
                GuiStats.this.drawSprite(insideLeft + 215 - 18 + 1, insideTop + 1 + 1, 54, 18);
            } else {
                GuiStats.this.drawSprite(insideLeft + 215 - 18, insideTop + 1, 54, 18);
            }
        }

        protected void drawSlot(int entryID, int insideLeft, int yPos, int insideSlotHeight, int mouseXIn, int mouseYIn) {
            StatCrafting statcrafting = this.getSlotStat(entryID);
            Item item = statcrafting.getItem();
            GuiStats.this.drawStatsScreen(insideLeft + 40, yPos, item);
            int i = Item.getIdFromItem(item);
            this.renderStat(StatList.objectCraftStats[i], insideLeft + 115, yPos, entryID % 2 == 0);
            this.renderStat(StatList.objectUseStats[i], insideLeft + 165, yPos, entryID % 2 == 0);
            this.renderStat(statcrafting, insideLeft + 215, yPos, entryID % 2 == 0);
        }

        protected String getHeaderDescriptionId(int state) {
            return state == 0 ? "stat.crafted" : (state == 1 ? "stat.used" : "stat.mined");
        }
    }

    class StatsGeneral extends GuiSlot {
        public StatsGeneral(Minecraft mcIn) {
            super(mcIn, GuiStats.this.width, GuiStats.this.height, 32, GuiStats.this.height - 64, 10);
            this.setShowSelectionBox(false);
        }

        protected int getSize() {
            return StatList.generalStats.size();
        }

        protected void elementClicked(int slotIndex, boolean isDoubleClick, int mouseX, int mouseY) {
        }

        protected boolean isSelected(int slotIndex) {
            return false;
        }

        protected int getContentHeight() {
            return this.getSize() * 10;
        }

        protected void drawBackground() {
            GuiStats.this.drawDefaultBackground();
        }

        protected void drawSlot(int entryID, int insideLeft, int yPos, int insideSlotHeight, int mouseXIn, int mouseYIn) {
            StatBase statbase = StatList.generalStats.get(entryID);
            GuiStats.this.drawString(GuiStats.this.minecraftFontRendererObj, statbase.getStatName().getUnformattedText(), insideLeft + 2, yPos + 1, entryID % 2 == 0 ? 16777215 : 9474192);
            String s = statbase.format(GuiStats.this.stats.readStat(statbase));
            GuiStats.this.drawString(GuiStats.this.minecraftFontRendererObj, s, insideLeft + 2 + 213 - GuiStats.this.minecraftFontRendererObj.getStringWidth(s), yPos + 1, entryID % 2 == 0 ? 16777215 : 9474192);
        }
    }

    class StatsItem extends GuiStats.Stats {
        public StatsItem(Minecraft mcIn) {
            super(mcIn);
            this.statsHolder = Lists.newArrayList();

            for (StatCrafting statcrafting : StatList.itemStats) {
                boolean flag = false;
                int i = Item.getIdFromItem(statcrafting.getItem());

                if (GuiStats.this.stats.readStat(statcrafting) > 0) {
                    flag = true;
                } else if (StatList.objectBreakStats[i] != null && GuiStats.this.stats.readStat(StatList.objectBreakStats[i]) > 0) {
                    flag = true;
                } else if (StatList.objectCraftStats[i] != null && GuiStats.this.stats.readStat(StatList.objectCraftStats[i]) > 0) {
                    flag = true;
                }

                if (flag) {
                    this.statsHolder.add(statcrafting);
                }
            }

            this.statSorter = (p_compare_1_, p_compare_2_) -> {
                int j = Item.getIdFromItem(p_compare_1_.getItem());
                int k = Item.getIdFromItem(p_compare_2_.getItem());
                StatBase statbase = null;
                StatBase statbase1 = null;

                if (StatsItem.this.sortColumn == 0) {
                    statbase = StatList.objectBreakStats[j];
                    statbase1 = StatList.objectBreakStats[k];
                } else if (StatsItem.this.sortColumn == 1) {
                    statbase = StatList.objectCraftStats[j];
                    statbase1 = StatList.objectCraftStats[k];
                } else if (StatsItem.this.sortColumn == 2) {
                    statbase = StatList.objectUseStats[j];
                    statbase1 = StatList.objectUseStats[k];
                }

                if (statbase != null || statbase1 != null) {
                    if (statbase == null) {
                        return 1;
                    }

                    if (statbase1 == null) {
                        return -1;
                    }

                    int l = GuiStats.this.stats.readStat(statbase);
                    int i1 = GuiStats.this.stats.readStat(statbase1);

                    if (l != i1) {
                        return (l - i1) * StatsItem.this.sortOrder;
                    }
                }

                return j - k;
            };
        }

        protected void drawListHeader(int insideLeft, int insideTop, Tessellator tessellatorIn) {
            super.drawListHeader(insideLeft, insideTop, tessellatorIn);

            if (this.headerPressed == 0) {
                GuiStats.this.drawSprite(insideLeft + 115 - 18 + 1, insideTop + 1 + 1, 72, 18);
            } else {
                GuiStats.this.drawSprite(insideLeft + 115 - 18, insideTop + 1, 72, 18);
            }

            if (this.headerPressed == 1) {
                GuiStats.this.drawSprite(insideLeft + 165 - 18 + 1, insideTop + 1 + 1, 18, 18);
            } else {
                GuiStats.this.drawSprite(insideLeft + 165 - 18, insideTop + 1, 18, 18);
            }

            if (this.headerPressed == 2) {
                GuiStats.this.drawSprite(insideLeft + 215 - 18 + 1, insideTop + 1 + 1, 36, 18);
            } else {
                GuiStats.this.drawSprite(insideLeft + 215 - 18, insideTop + 1, 36, 18);
            }
        }

        protected void drawSlot(int entryID, int insideLeft, int yPos, int insideSlotHeight, int mouseXIn, int mouseYIn) {
            StatCrafting statcrafting = this.getSlotStat(entryID);
            Item item = statcrafting.getItem();
            GuiStats.this.drawStatsScreen(insideLeft + 40, yPos, item);
            int i = Item.getIdFromItem(item);
            this.renderStat(StatList.objectBreakStats[i], insideLeft + 115, yPos, entryID % 2 == 0);
            this.renderStat(StatList.objectCraftStats[i], insideLeft + 165, yPos, entryID % 2 == 0);
            this.renderStat(statcrafting, insideLeft + 215, yPos, entryID % 2 == 0);
        }

        protected String getHeaderDescriptionId(int state) {
            return state == 1 ? "stat.crafted" : (state == 2 ? "stat.used" : "stat.depleted");
        }
    }

    class StatsMobsList extends GuiSlot {
        private final List<EntityList.EntityEggInfo> mobs = Lists.newArrayList();

        public StatsMobsList(Minecraft mcIn) {
            super(mcIn, GuiStats.this.width, GuiStats.this.height, 32, GuiStats.this.height - 64, GuiStats.this.minecraftFontRendererObj.FONT_HEIGHT * 4);
            this.setShowSelectionBox(false);

            for (EntityList.EntityEggInfo entitylist$entityegginfo : EntityList.entityEggs.values()) {
                if (GuiStats.this.stats.readStat(entitylist$entityegginfo.killEntityStat) > 0 || GuiStats.this.stats.readStat(entitylist$entityegginfo.entityKilledByStat) > 0) {
                    this.mobs.add(entitylist$entityegginfo);
                }
            }
        }

        protected int getSize() {
            return this.mobs.size();
        }

        protected void elementClicked(int slotIndex, boolean isDoubleClick, int mouseX, int mouseY) {
        }

        protected boolean isSelected(int slotIndex) {
            return false;
        }

        protected int getContentHeight() {
            return this.getSize() * GuiStats.this.minecraftFontRendererObj.FONT_HEIGHT * 4;
        }

        protected void drawBackground() {
            GuiStats.this.drawDefaultBackground();
        }

        protected void drawSlot(int entryID, int insideLeft, int yPos, int insideSlotHeight, int mouseXIn, int mouseYIn) {
            EntityList.EntityEggInfo entitylist$entityegginfo = this.mobs.get(entryID);
            String s = I18n.format("entity." + EntityList.getStringFromID(entitylist$entityegginfo.spawnedID) + ".name");
            int i = GuiStats.this.stats.readStat(entitylist$entityegginfo.killEntityStat);
            int j = GuiStats.this.stats.readStat(entitylist$entityegginfo.entityKilledByStat);
            String s1 = I18n.format("stat.entityKills", i, s);
            String s2 = I18n.format("stat.entityKilledBy", s, j);

            if (i == 0) {
                s1 = I18n.format("stat.entityKills.none", s);
            }

            if (j == 0) {
                s2 = I18n.format("stat.entityKilledBy.none", s);
            }

            GuiStats.this.drawString(GuiStats.this.minecraftFontRendererObj, s, insideLeft + 2 - 10, yPos + 1, 16777215);
            GuiStats.this.drawString(GuiStats.this.minecraftFontRendererObj, s1, insideLeft + 2, yPos + 1 + GuiStats.this.minecraftFontRendererObj.FONT_HEIGHT, i == 0 ? 6316128 : 9474192);
            GuiStats.this.drawString(GuiStats.this.minecraftFontRendererObj, s2, insideLeft + 2, yPos + 1 + GuiStats.this.minecraftFontRendererObj.FONT_HEIGHT * 2, j == 0 ? 6316128 : 9474192);
        }
    }
}
