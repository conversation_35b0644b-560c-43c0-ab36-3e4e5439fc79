package net.minecraft.command;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import net.minecraft.block.material.Material;
import net.minecraft.entity.Entity;
import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.scoreboard.Team;
import net.minecraft.server.MinecraftServer;
import net.minecraft.util.BlockPos;
import net.minecraft.util.ChatComponentTranslation;
import net.minecraft.util.MathHelper;
import net.minecraft.world.World;

import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.Set;

public class CommandSpreadPlayers extends CommandBase {
    public String getCommandName() {
        return "spreadplayers";
    }

    public int getRequiredPermissionLevel() {
        return 2;
    }

    public String getCommandUsage(ICommandSender sender) {
        return "commands.spreadplayers.usage";
    }

    public void processCommand(ICommandSender sender, String[] args) throws CommandException {
        if (args.length < 6) {
            throw new WrongUsageException("commands.spreadplayers.usage");
        } else {
            int i = 0;
            BlockPos blockpos = sender.getPosition();
            double d0 = parseDouble(blockpos.getX(), args[i++], true);
            double d1 = parseDouble(blockpos.getZ(), args[i++], true);
            double d2 = parseDouble(args[i++], 0.0D);
            double d3 = parseDouble(args[i++], d2 + 1.0D);
            boolean flag = parseBoolean(args[i++]);
            List<Entity> list = Lists.newArrayList();

            while (i < args.length) {
                String s = args[i++];

                if (PlayerSelector.hasArguments(s)) {
                    List<Entity> list1 = PlayerSelector.matchEntities(sender, s, Entity.class);

                    if (list1.isEmpty()) {
                        throw new EntityNotFoundException();
                    }

                    list.addAll(list1);
                } else {
                    EntityPlayer entityplayer = MinecraftServer.getServer().getConfigurationManager().getPlayerByUsername(s);

                    if (entityplayer == null) {
                        throw new PlayerNotFoundException();
                    }

                    list.add(entityplayer);
                }
            }

            sender.setCommandStat(CommandResultStats.Type.AFFECTED_ENTITIES, list.size());

            if (list.isEmpty()) {
                throw new EntityNotFoundException();
            } else {
                sender.addChatMessage(new ChatComponentTranslation("commands.spreadplayers.spreading." + (flag ? "teams" : "players"), list.size(), d3, d0, d1, d2));
                this.spread(sender, list, new CommandSpreadPlayers.Position(d0, d1), d2, d3, list.get(0).worldObj, flag);
            }
        }
    }

    private void spread(ICommandSender sender, List<Entity> entities, CommandSpreadPlayers.Position pos, double spreadDistance, double maxRange, World worldIn, boolean respectTeams) throws CommandException {
        Random random = new Random();
        double d0 = pos.x - maxRange;
        double d1 = pos.z - maxRange;
        double d2 = pos.x + maxRange;
        double d3 = pos.z + maxRange;
        CommandSpreadPlayers.Position[] acommandspreadplayers$position = this.createInitialPositions(random, respectTeams ? this.getNumberOfTeams(entities) : entities.size(), d0, d1, d2, d3);
        int i = this.spreadPositions(pos, spreadDistance, worldIn, random, d0, d1, d2, d3, acommandspreadplayers$position, respectTeams);
        double d4 = this.setPlayerPositions(entities, worldIn, acommandspreadplayers$position, respectTeams);
        notifyOperators(sender, this, "commands.spreadplayers.success." + (respectTeams ? "teams" : "players"), acommandspreadplayers$position.length, pos.x, pos.z);

        if (acommandspreadplayers$position.length > 1) {
            sender.addChatMessage(new ChatComponentTranslation("commands.spreadplayers.info." + (respectTeams ? "teams" : "players"), String.format("%.2f", d4), i));
        }
    }

    private int getNumberOfTeams(List<Entity> entities) {
        Set<Team> set = Sets.newHashSet();

        for (Entity entity : entities) {
            if (entity instanceof EntityPlayer) {
                set.add(((EntityPlayer) entity).getTeam());
            } else {
                set.add(null);
            }
        }

        return set.size();
    }

    private int spreadPositions(CommandSpreadPlayers.Position position, double distance, World worldIn, Random random, double minX, double minZ, double maxX, double maxZ, CommandSpreadPlayers.Position[] positions, boolean respectTeams) throws CommandException {
        boolean flag = true;
        double d0 = 3.4028234663852886E38D;
        int i;

        for (i = 0; i < 10000 && flag; ++i) {
            flag = false;
            d0 = 3.4028234663852886E38D;

            for (int j = 0; j < positions.length; ++j) {
                CommandSpreadPlayers.Position commandspreadplayers$position = positions[j];
                int k = 0;
                CommandSpreadPlayers.Position commandspreadplayers$position1 = new CommandSpreadPlayers.Position();

                for (int l = 0; l < positions.length; ++l) {
                    if (j != l) {
                        CommandSpreadPlayers.Position commandspreadplayers$position2 = positions[l];
                        double d1 = commandspreadplayers$position.dist(commandspreadplayers$position2);
                        d0 = Math.min(d1, d0);

                        if (d1 < distance) {
                            ++k;
                            commandspreadplayers$position1.x += commandspreadplayers$position2.x - commandspreadplayers$position.x;
                            commandspreadplayers$position1.z += commandspreadplayers$position2.z - commandspreadplayers$position.z;
                        }
                    }
                }

                if (k > 0) {
                    commandspreadplayers$position1.x /= k;
                    commandspreadplayers$position1.z /= k;
                    double d2 = commandspreadplayers$position1.getLength();

                    if (d2 > 0.0D) {
                        commandspreadplayers$position1.normalize();
                        commandspreadplayers$position.moveAway(commandspreadplayers$position1);
                    } else {
                        commandspreadplayers$position.randomize(random, minX, minZ, maxX, maxZ);
                    }

                    flag = true;
                }

                if (commandspreadplayers$position.clamp(minX, minZ, maxX, maxZ)) {
                    flag = true;
                }
            }

            if (!flag) {
                for (CommandSpreadPlayers.Position commandspreadplayers$position3 : positions) {
                    if (!commandspreadplayers$position3.isSafe(worldIn)) {
                        commandspreadplayers$position3.randomize(random, minX, minZ, maxX, maxZ);
                        flag = true;
                    }
                }
            }
        }

        if (i >= 10000) {
            throw new CommandException("commands.spreadplayers.failure." + (respectTeams ? "teams" : "players"), positions.length, position.x, position.z, String.format("%.2f", d0));
        } else {
            return i;
        }
    }

    private double setPlayerPositions(List<Entity> entities, World worldIn, CommandSpreadPlayers.Position[] positions, boolean respectTeams) {
        double d0 = 0.0D;
        int i = 0;
        Map<Team, CommandSpreadPlayers.Position> map = Maps.newHashMap();

        for (Entity entity : entities) {
            Position commandspreadplayers$position;

            if (respectTeams) {
                Team team = entity instanceof EntityPlayer ? ((EntityPlayer) entity).getTeam() : null;

                if (!map.containsKey(team)) {
                    map.put(team, positions[i++]);
                }

                commandspreadplayers$position = map.get(team);
            } else {
                commandspreadplayers$position = positions[i++];
            }

            entity.setPositionAndUpdate((float) MathHelper.floor_double(commandspreadplayers$position.x) + 0.5F, commandspreadplayers$position.getSpawnY(worldIn), (double) MathHelper.floor_double(commandspreadplayers$position.z) + 0.5D);
            double d2 = Double.MAX_VALUE;

            for (Position position : positions) {
                if (commandspreadplayers$position != position) {
                    double d1 = commandspreadplayers$position.dist(position);
                    d2 = Math.min(d1, d2);
                }
            }

            d0 += d2;
        }

        d0 = d0 / (double) entities.size();
        return d0;
    }

    private CommandSpreadPlayers.Position[] createInitialPositions(Random random, int teamsSize, double minX, double minZ, double maxX, double maxZ) {
        CommandSpreadPlayers.Position[] acommandspreadplayers$position = new CommandSpreadPlayers.Position[teamsSize];

        for (int i = 0; i < acommandspreadplayers$position.length; ++i) {
            CommandSpreadPlayers.Position commandspreadplayers$position = new CommandSpreadPlayers.Position();
            commandspreadplayers$position.randomize(random, minX, minZ, maxX, maxZ);
            acommandspreadplayers$position[i] = commandspreadplayers$position;
        }

        return acommandspreadplayers$position;
    }

    public List<String> addTabCompletionOptions(ICommandSender sender, String[] args, BlockPos pos) {
        return args.length >= 1 && args.length <= 2 ? getTabCompletionCoordinateXZ(args, 0, pos) : null;
    }

    static class Position {
        double x;
        double z;

        Position() {
        }

        Position(double xIn, double zIn) {
            this.x = xIn;
            this.z = zIn;
        }

        double dist(CommandSpreadPlayers.Position pos) {
            double d0 = this.x - pos.x;
            double d1 = this.z - pos.z;
            return Math.sqrt(d0 * d0 + d1 * d1);
        }

        void normalize() {
            double d0 = this.getLength();
            this.x /= d0;
            this.z /= d0;
        }

        float getLength() {
            return MathHelper.sqrt_double(this.x * this.x + this.z * this.z);
        }

        public void moveAway(CommandSpreadPlayers.Position pos) {
            this.x -= pos.x;
            this.z -= pos.z;
        }

        public boolean clamp(double minX, double minZ, double maxX, double maxZ) {
            boolean flag = false;

            if (this.x < minX) {
                this.x = minX;
                flag = true;
            } else if (this.x > maxX) {
                this.x = maxX;
                flag = true;
            }

            if (this.z < minZ) {
                this.z = minZ;
                flag = true;
            } else if (this.z > maxZ) {
                this.z = maxZ;
                flag = true;
            }

            return flag;
        }

        public int getSpawnY(World worldIn) {
            BlockPos blockpos = new BlockPos(this.x, 256.0D, this.z);

            while (blockpos.getY() > 0) {
                blockpos = blockpos.down();

                if (worldIn.getBlockState(blockpos).getBlock().getMaterial() != Material.air) {
                    return blockpos.getY() + 1;
                }
            }

            return 257;
        }

        public boolean isSafe(World worldIn) {
            BlockPos blockpos = new BlockPos(this.x, 256.0D, this.z);

            while (blockpos.getY() > 0) {
                blockpos = blockpos.down();
                Material material = worldIn.getBlockState(blockpos).getBlock().getMaterial();

                if (material != Material.air) {
                    return !material.isLiquid() && material != Material.fire;
                }
            }

            return false;
        }

        public void randomize(Random rand, double minX, double minZ, double maxX, double maxZ) {
            this.x = MathHelper.getRandomDoubleInRange(rand, minX, maxX);
            this.z = MathHelper.getRandomDoubleInRange(rand, minZ, maxZ);
        }
    }
}
