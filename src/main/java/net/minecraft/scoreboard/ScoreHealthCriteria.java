package net.minecraft.scoreboard;

import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.util.MathHelper;

import java.util.List;

public class ScoreHealthCriteria extends ScoreDummyCriteria {
    public ScoreHealthCriteria(String name) {
        super(name);
    }

    public int setScore(List<EntityPlayer> players) {
        float f = 0.0F;

        for (EntityPlayer entityplayer : players) {
            f += entityplayer.getHealth() + entityplayer.getAbsorptionAmount();
        }

        if (!players.isEmpty()) {
            f /= (float) players.size();
        }

        return MathHelper.ceiling_float_int(f);
    }

    public boolean isReadOnly() {
        return true;
    }

    public IScoreObjectiveCriteria.EnumRenderType getRenderType() {
        return IScoreObjectiveCriteria.EnumRenderType.HEARTS;
    }
}
