package net.minecraft.world.gen.layer;

import net.minecraft.world.WorldType;
import net.minecraft.world.biome.BiomeGenBase;
import net.minecraft.world.gen.ChunkProviderSettings;

public class GenLayerBiome extends GenLayer {
    private final BiomeGenBase[] mediumBiomes = new BiomeGenBase[]{BiomeGenBase.forest, BiomeGenBase.roofedForest, BiomeGenBase.extremeHills, BiomeGenBase.plains, BiomeGenBase.birchForest, BiomeGenBase.swampland};
    private final BiomeGenBase[] coldBiomes = new BiomeGenBase[]{BiomeGenBase.forest, BiomeGenBase.extremeHills, BiomeGenBase.taiga, BiomeGenBase.plains};
    private final BiomeGenBase[] iceBiomes = new BiomeGenBase[]{BiomeGenBase.icePlains, BiomeGenBase.icePlains, BiomeGenBase.icePlains, BiomeGenBase.coldTaiga};
    private final ChunkProviderSettings settings;
    private BiomeGenBase[] warmBiomes = new BiomeGenBase[]{BiomeGenBase.desert, BiomeGenBase.desert, BiomeGenBase.desert, BiomeGenBase.savanna, BiomeGenBase.savanna, BiomeGenBase.plains};

    public GenLayerBiome(long seed, GenLayer parent, WorldType type, String jsonObj) {
        super(seed);
        this.parent = parent;

        if (type == WorldType.DEFAULT_1_1) {
            this.warmBiomes = new BiomeGenBase[]{BiomeGenBase.desert, BiomeGenBase.forest, BiomeGenBase.extremeHills, BiomeGenBase.swampland, BiomeGenBase.plains, BiomeGenBase.taiga};
            this.settings = null;
        } else if (type == WorldType.CUSTOMIZED) {
            this.settings = ChunkProviderSettings.Factory.jsonToFactory(jsonObj).build();
        } else {
            this.settings = null;
        }
    }

    public int[] getInts(int areaX, int areaY, int areaWidth, int areaHeight) {
        int[] aint = this.parent.getInts(areaX, areaY, areaWidth, areaHeight);
        int[] aint1 = IntCache.getIntCache(areaWidth * areaHeight);

        for (int i = 0; i < areaHeight; ++i) {
            for (int j = 0; j < areaWidth; ++j) {
                this.initChunkSeed(j + areaX, i + areaY);
                int k = aint[j + i * areaWidth];
                int l = (k & 3840) >> 8;
                k = k & -3841;

                if (this.settings != null && this.settings.fixedBiome >= 0) {
                    aint1[j + i * areaWidth] = this.settings.fixedBiome;
                } else if (isBiomeOceanic(k)) {
                    aint1[j + i * areaWidth] = k;
                } else if (k == BiomeGenBase.mushroomIsland.biomeID) {
                    aint1[j + i * areaWidth] = k;
                } else if (k == 1) {
                    if (l > 0) {
                        if (this.nextInt(3) == 0) {
                            aint1[j + i * areaWidth] = BiomeGenBase.mesaPlateau.biomeID;
                        } else {
                            aint1[j + i * areaWidth] = BiomeGenBase.mesaPlateau_F.biomeID;
                        }
                    } else {
                        aint1[j + i * areaWidth] = this.warmBiomes[this.nextInt(this.warmBiomes.length)].biomeID;
                    }
                } else if (k == 2) {
                    if (l > 0) {
                        aint1[j + i * areaWidth] = BiomeGenBase.jungle.biomeID;
                    } else {
                        aint1[j + i * areaWidth] = this.mediumBiomes[this.nextInt(this.mediumBiomes.length)].biomeID;
                    }
                } else if (k == 3) {
                    if (l > 0) {
                        aint1[j + i * areaWidth] = BiomeGenBase.megaTaiga.biomeID;
                    } else {
                        aint1[j + i * areaWidth] = this.coldBiomes[this.nextInt(this.coldBiomes.length)].biomeID;
                    }
                } else if (k == 4) {
                    aint1[j + i * areaWidth] = this.iceBiomes[this.nextInt(this.iceBiomes.length)].biomeID;
                } else {
                    aint1[j + i * areaWidth] = BiomeGenBase.mushroomIsland.biomeID;
                }
            }
        }

        return aint1;
    }
}
