package net.minecraft.world.gen;

import net.minecraft.block.Block;
import net.minecraft.block.state.IBlockState;
import net.minecraft.util.ResourceLocation;

public class FlatLayerInfo {
    private final int version;
    private final int layerCount;
    private IBlockState layerMaterial;
    private int layerMinimumY;

    public FlatLayerInfo(int layerCount, Block layerMaterialIn) {
        this(3, layerCount, layerMaterialIn);
    }

    public FlatLayerInfo(int version, int height, Block layerMaterialIn) {
        this.version = version;
        this.layerCount = height;
        this.layerMaterial = layerMaterialIn.getDefaultState();
    }

    public FlatLayerInfo(int version, int height, Block layerMaterialIn, int meta) {
        this(version, height, layerMaterialIn);
        this.layerMaterial = layerMaterialIn.getStateFromMeta(meta);
    }

    public int getLayerCount() {
        return this.layerCount;
    }

    public IBlockState getLayerMaterial() {
        return this.layerMaterial;
    }

    private Block getLayerMaterialBlock() {
        return this.layerMaterial.getBlock();
    }

    private int getFillBlockMeta() {
        return this.layerMaterial.getBlock().getMetaFromState(this.layerMaterial);
    }

    public int getMinY() {
        return this.layerMinimumY;
    }

    public void setMinY(int minY) {
        this.layerMinimumY = minY;
    }

    public String toString() {
        String s;

        if (this.version >= 3) {
            ResourceLocation resourcelocation = Block.blockRegistry.getNameForObject(this.getLayerMaterialBlock());
            s = resourcelocation == null ? "null" : resourcelocation.toString();

            if (this.layerCount > 1) {
                s = this.layerCount + "*" + s;
            }
        } else {
            s = Integer.toString(Block.getIdFromBlock(this.getLayerMaterialBlock()));

            if (this.layerCount > 1) {
                s = this.layerCount + "x" + s;
            }
        }

        int i = this.getFillBlockMeta();

        if (i > 0) {
            s = s + ":" + i;
        }

        return s;
    }
}
