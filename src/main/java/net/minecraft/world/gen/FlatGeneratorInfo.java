package net.minecraft.world.gen;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import net.minecraft.block.Block;
import net.minecraft.init.Blocks;
import net.minecraft.util.MathHelper;
import net.minecraft.world.biome.BiomeGenBase;

import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

public class FlatGeneratorInfo {
    private final List<FlatLayerInfo> flatLayers = Lists.newArrayList();
    private final Map<String, Map<String, String>> worldFeatures = Maps.newHashMap();
    private int biomeToUse;

    private static FlatLayerInfo getLayerFromString(int version, String layerText, int y) {
        String[] astring = version >= 3 ? layerText.split("\\*", 2) : layerText.split("x", 2);
        int i = 1;
        int j = 0;

        if (astring.length == 2) {
            try {
                i = Integer.parseInt(astring[0]);

                if (y + i >= 256) {
                    i = 256 - y;
                }

                if (i < 0) {
                    i = 0;
                }
            } catch (Throwable var8) {
                return null;
            }
        }

        Block block;

        try {
            String s = astring[astring.length - 1];

            if (version < 3) {
                astring = s.split(":", 2);

                if (astring.length > 1) {
                    j = Integer.parseInt(astring[1]);
                }

                block = Block.getBlockById(Integer.parseInt(astring[0]));
            } else {
                astring = s.split(":", 3);
                block = astring.length > 1 ? Block.getBlockFromName(astring[0] + ":" + astring[1]) : null;

                if (block != null) {
                    j = astring.length > 2 ? Integer.parseInt(astring[2]) : 0;
                } else {
                    block = Block.getBlockFromName(astring[0]);

                    if (block != null) {
                        j = astring.length > 1 ? Integer.parseInt(astring[1]) : 0;
                    }
                }

                if (block == null) {
                    return null;
                }
            }

            if (block == Blocks.air) {
                j = 0;
            }

            if (j < 0 || j > 15) {
                j = 0;
            }
        } catch (Throwable var9) {
            return null;
        }

        FlatLayerInfo flatlayerinfo = new FlatLayerInfo(version, i, block, j);
        flatlayerinfo.setMinY(y);
        return flatlayerinfo;
    }

    private static List<FlatLayerInfo> getLayersFromString(int version, String layerText) {
        if (layerText != null && !layerText.isEmpty()) {
            List<FlatLayerInfo> list = Lists.newArrayList();
            String[] astring = layerText.split(",");
            int i = 0;

            for (String s : astring) {
                FlatLayerInfo flatlayerinfo = getLayerFromString(version, s, i);

                if (flatlayerinfo == null) {
                    return null;
                }

                list.add(flatlayerinfo);
                i += flatlayerinfo.getLayerCount();
            }

            return list;
        } else {
            return null;
        }
    }

    public static FlatGeneratorInfo createFlatGeneratorFromString(String flatGeneratorSettings) {
        if (flatGeneratorSettings == null) {
            return getDefaultFlatGenerator();
        } else {
            String[] astring = flatGeneratorSettings.split(";", -1);
            int i = astring.length == 1 ? 0 : MathHelper.parseIntWithDefault(astring[0], 0);

            if (i >= 0 && i <= 3) {
                FlatGeneratorInfo flatgeneratorinfo = new FlatGeneratorInfo();
                int j = astring.length == 1 ? 0 : 1;
                List<FlatLayerInfo> list = getLayersFromString(i, astring[j++]);

                if (list != null && !list.isEmpty()) {
                    flatgeneratorinfo.getFlatLayers().addAll(list);
                    flatgeneratorinfo.updateLayers();
                    int k = BiomeGenBase.plains.biomeID;

                    if (i > 0 && astring.length > j) {
                        k = MathHelper.parseIntWithDefault(astring[j++], k);
                    }

                    flatgeneratorinfo.setBiome(k);

                    if (i > 0 && astring.length > j) {
                        String[] astring1 = astring[j + 1].toLowerCase().split(",");

                        for (String s : astring1) {
                            String[] astring2 = s.split("\\(", 2);
                            Map<String, String> map = Maps.newHashMap();

                            if (!astring2[0].isEmpty()) {
                                flatgeneratorinfo.getWorldFeatures().put(astring2[0], map);

                                if (astring2.length > 1 && astring2[1].endsWith(")") && astring2[1].length() > 1) {
                                    String[] astring3 = astring2[1].substring(0, astring2[1].length() - 1).split(" ");

                                    for (String string : astring3) {
                                        String[] astring4 = string.split("=", 2);

                                        if (astring4.length == 2) {
                                            map.put(astring4[0], astring4[1]);
                                        }
                                    }
                                }
                            }
                        }
                    } else {
                        flatgeneratorinfo.getWorldFeatures().put("village", Maps.newHashMap());
                    }

                    return flatgeneratorinfo;
                } else {
                    return getDefaultFlatGenerator();
                }
            } else {
                return getDefaultFlatGenerator();
            }
        }
    }

    public static FlatGeneratorInfo getDefaultFlatGenerator() {
        FlatGeneratorInfo flatgeneratorinfo = new FlatGeneratorInfo();
        flatgeneratorinfo.setBiome(BiomeGenBase.plains.biomeID);
        flatgeneratorinfo.getFlatLayers().add(new FlatLayerInfo(1, Blocks.bedrock));
        flatgeneratorinfo.getFlatLayers().add(new FlatLayerInfo(2, Blocks.dirt));
        flatgeneratorinfo.getFlatLayers().add(new FlatLayerInfo(1, Blocks.grass));
        flatgeneratorinfo.updateLayers();
        flatgeneratorinfo.getWorldFeatures().put("village", Maps.newHashMap());
        return flatgeneratorinfo;
    }

    public int getBiome() {
        return this.biomeToUse;
    }

    public void setBiome(int biome) {
        this.biomeToUse = biome;
    }

    public Map<String, Map<String, String>> getWorldFeatures() {
        return this.worldFeatures;
    }

    public List<FlatLayerInfo> getFlatLayers() {
        return this.flatLayers;
    }

    public void updateLayers() {
        int i = 0;

        for (FlatLayerInfo flatlayerinfo : this.flatLayers) {
            flatlayerinfo.setMinY(i);
            i += flatlayerinfo.getLayerCount();
        }
    }

    public String toString() {
        StringBuilder stringbuilder = new StringBuilder();
        stringbuilder.append(3);
        stringbuilder.append(";");

        for (int i = 0; i < this.flatLayers.size(); ++i) {
            if (i > 0) {
                stringbuilder.append(",");
            }

            stringbuilder.append(this.flatLayers.get(i).toString());
        }

        stringbuilder.append(";");
        stringbuilder.append(this.biomeToUse);

        if (!this.worldFeatures.isEmpty()) {
            stringbuilder.append(";");
            int k = 0;

            for (Entry<String, Map<String, String>> entry : this.worldFeatures.entrySet()) {
                if (k++ > 0) {
                    stringbuilder.append(",");
                }

                stringbuilder.append(entry.getKey().toLowerCase());
                Map<String, String> map = entry.getValue();

                if (!map.isEmpty()) {
                    stringbuilder.append("(");
                    int j = 0;

                    for (Entry<String, String> entry1 : map.entrySet()) {
                        if (j++ > 0) {
                            stringbuilder.append(" ");
                        }

                        stringbuilder.append(entry1.getKey());
                        stringbuilder.append("=");
                        stringbuilder.append(entry1.getValue());
                    }

                    stringbuilder.append(")");
                }
            }
        } else {
            stringbuilder.append(";");
        }

        return stringbuilder.toString();
    }
}
