package net.minecraft.world.storage;

import net.minecraft.nbt.NBTTagCompound;
import net.minecraft.util.BlockPos;
import net.minecraft.world.EnumDifficulty;
import net.minecraft.world.GameRules;
import net.minecraft.world.WorldSettings;
import net.minecraft.world.WorldType;

public class DerivedWorldInfo extends WorldInfo {
    private final WorldInfo theWorldInfo;

    public DerivedWorldInfo(WorldInfo worldInfoIn) {
        this.theWorldInfo = worldInfoIn;
    }

    public NBTTagCompound getNBTTagCompound() {
        return this.theWorldInfo.getNBTTagCompound();
    }

    public NBTTagCompound cloneNBTCompound(NBTTagCompound nbt) {
        return this.theWorldInfo.cloneNBTCompound(nbt);
    }

    public long getSeed() {
        return this.theWorldInfo.getSeed();
    }

    public int getSpawnX() {
        return this.theWorldInfo.getSpawnX();
    }

    public void setSpawnX(int x) {
    }

    public int getSpawnY() {
        return this.theWorldInfo.getSpawnY();
    }

    public void setSpawnY(int y) {
    }

    public int getSpawnZ() {
        return this.theWorldInfo.getSpawnZ();
    }

    public void setSpawnZ(int z) {
    }

    public long getWorldTotalTime() {
        return this.theWorldInfo.getWorldTotalTime();
    }

    public void setWorldTotalTime(long time) {
    }

    public long getWorldTime() {
        return this.theWorldInfo.getWorldTime();
    }

    public void setWorldTime(long time) {
    }

    public long getSizeOnDisk() {
        return this.theWorldInfo.getSizeOnDisk();
    }

    public NBTTagCompound getPlayerNBTTagCompound() {
        return this.theWorldInfo.getPlayerNBTTagCompound();
    }

    public String getWorldName() {
        return this.theWorldInfo.getWorldName();
    }

    public void setWorldName(String worldName) {
    }

    public int getSaveVersion() {
        return this.theWorldInfo.getSaveVersion();
    }

    public void setSaveVersion(int version) {
    }

    public long getLastTimePlayed() {
        return this.theWorldInfo.getLastTimePlayed();
    }

    public boolean isThundering() {
        return this.theWorldInfo.isThundering();
    }

    public void setThundering(boolean thunderingIn) {
    }

    public int getThunderTime() {
        return this.theWorldInfo.getThunderTime();
    }

    public void setThunderTime(int time) {
    }

    public boolean isRaining() {
        return this.theWorldInfo.isRaining();
    }

    public void setRaining(boolean isRaining) {
    }

    public int getRainTime() {
        return this.theWorldInfo.getRainTime();
    }

    public void setRainTime(int time) {
    }

    public WorldSettings.GameType getGameType() {
        return this.theWorldInfo.getGameType();
    }

    public void setSpawn(BlockPos spawnPoint) {
    }

    public boolean isMapFeaturesEnabled() {
        return this.theWorldInfo.isMapFeaturesEnabled();
    }

    public boolean isHardcoreModeEnabled() {
        return this.theWorldInfo.isHardcoreModeEnabled();
    }

    public WorldType getTerrainType() {
        return this.theWorldInfo.getTerrainType();
    }

    public void setTerrainType(WorldType type) {
    }

    public boolean areCommandsAllowed() {
        return this.theWorldInfo.areCommandsAllowed();
    }

    public void setAllowCommands(boolean allow) {
    }

    public boolean isInitialized() {
        return this.theWorldInfo.isInitialized();
    }

    public void setServerInitialized(boolean initializedIn) {
    }

    public GameRules getGameRulesInstance() {
        return this.theWorldInfo.getGameRulesInstance();
    }

    public EnumDifficulty getDifficulty() {
        return this.theWorldInfo.getDifficulty();
    }

    public void setDifficulty(EnumDifficulty newDifficulty) {
    }

    public boolean isDifficultyLocked() {
        return this.theWorldInfo.isDifficultyLocked();
    }

    public void setDifficultyLocked(boolean locked) {
    }
}
