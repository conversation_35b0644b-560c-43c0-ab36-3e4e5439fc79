package net.minecraft.network.play.server;

import net.minecraft.network.Packet;
import net.minecraft.network.PacketBuffer;
import net.minecraft.network.play.INetHandlerPlayClient;
import net.minecraft.scoreboard.IScoreObjectiveCriteria;
import net.minecraft.scoreboard.ScoreObjective;

public class S3BPacketScoreboardObjective implements Packet<INetHandlerPlayClient> {
    private String objectiveName;
    private String objectiveValue;
    private IScoreObjectiveCriteria.EnumRenderType type;
    private int action;

    public S3BPacketScoreboardObjective() {
    }

    public S3BPacketScoreboardObjective(ScoreObjective objective, int action) {
        this.objectiveName = objective.getName();
        this.objectiveValue = objective.getDisplayName();
        this.type = objective.getCriteria().getRenderType();
        this.action = action;
    }

    public void readPacketData(PacketBuffer buf) {
        this.objectiveName = buf.readStringFromBuffer(16);
        this.action = buf.readByte();

        if (this.action == 0 || this.action == 2) {
            this.objectiveValue = buf.readStringFromBuffer(32);
            this.type = IScoreObjectiveCriteria.EnumRenderType.getByName(buf.readStringFromBuffer(16));
        }
    }

    public void writePacketData(PacketBuffer buf) {
        buf.writeString(this.objectiveName);
        buf.writeByte(this.action);

        if (this.action == 0 || this.action == 2) {
            buf.writeString(this.objectiveValue);
            buf.writeString(this.type.getRenderType());
        }
    }

    public void processPacket(INetHandlerPlayClient handler) {
        handler.handleScoreboardObjective(this);
    }

    public String getObjectiveName() {
        return this.objectiveName;
    }

    public String getObjectiveValue() {
        return this.objectiveValue;
    }

    public int getAction() {
        return this.action;
    }

    public IScoreObjectiveCriteria.EnumRenderType getRenderType() {
        return this.type;
    }
}
