package net.minecraft.enchantment;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import net.minecraft.entity.Entity;
import net.minecraft.entity.EntityLivingBase;
import net.minecraft.entity.EnumCreatureAttribute;
import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.init.Items;
import net.minecraft.item.Item;
import net.minecraft.item.ItemStack;
import net.minecraft.nbt.NBTTagCompound;
import net.minecraft.nbt.NBTTagList;
import net.minecraft.util.DamageSource;
import net.minecraft.util.WeightedRandom;

import java.util.*;

public class EnchantmentHelper {
    private static final Random enchantmentRand = new Random();
    private static final EnchantmentHelper.ModifierDamage enchantmentModifierDamage = new EnchantmentHelper.ModifierDamage();
    private static final EnchantmentHelper.ModifierLiving enchantmentModifierLiving = new EnchantmentHelper.ModifierLiving();
    private static final EnchantmentHelper.HurtIterator ENCHANTMENT_ITERATOR_HURT = new EnchantmentHelper.HurtIterator();
    private static final EnchantmentHelper.DamageIterator ENCHANTMENT_ITERATOR_DAMAGE = new EnchantmentHelper.DamageIterator();

    public static int getEnchantmentLevel(int enchID, ItemStack stack) {
        if (stack != null) {
            NBTTagList nbttaglist = stack.getEnchantmentTagList();

            if (nbttaglist != null) {
                for (int i = 0; i < nbttaglist.tagCount(); ++i) {
                    int j = nbttaglist.getCompoundTagAt(i).getShort("id");
                    int k = nbttaglist.getCompoundTagAt(i).getShort("lvl");

                    if (j == enchID) {
                        return k;
                    }
                }

            }
        }
        return 0;
    }

    public static Map<Integer, Integer> getEnchantments(ItemStack stack) {
        Map<Integer, Integer> map = Maps.newLinkedHashMap();
        NBTTagList nbttaglist = stack.getItem() == Items.enchanted_book ? Items.enchanted_book.getEnchantments(stack) : stack.getEnchantmentTagList();

        if (nbttaglist != null) {
            for (int i = 0; i < nbttaglist.tagCount(); ++i) {
                int j = nbttaglist.getCompoundTagAt(i).getShort("id");
                int k = nbttaglist.getCompoundTagAt(i).getShort("lvl");
                map.put(j, k);
            }
        }

        return map;
    }

    public static void setEnchantments(Map<Integer, Integer> enchMap, ItemStack stack) {
        NBTTagList nbttaglist = new NBTTagList();

        for (int i : enchMap.keySet()) {
            Enchantment enchantment = Enchantments.getEnchantmentById(i);

            if (enchantment != null) {
                NBTTagCompound nbttagcompound = new NBTTagCompound();
                nbttagcompound.setShort("id", (short) i);
                nbttagcompound.setShort("lvl", (short) enchMap.get(i).intValue());
                nbttaglist.appendTag(nbttagcompound);

                if (stack.getItem() == Items.enchanted_book) {
                    Items.enchanted_book.addEnchantment(stack, new EnchantmentData(enchantment, enchMap.get(i)));
                }
            }
        }

        if (nbttaglist.tagCount() > 0) {
            if (stack.getItem() != Items.enchanted_book) {
                stack.setTagInfo("ench", nbttaglist);
            }
        } else if (stack.hasTagCompound()) {
            stack.getTagCompound().removeTag("ench");
        }
    }

    public static int getMaxEnchantmentLevel(int enchID, ItemStack[] stacks) {
        if (stacks == null) {
            return 0;
        } else {
            int i = 0;

            for (ItemStack itemstack : stacks) {
                int j = getEnchantmentLevel(enchID, itemstack);

                if (j > i) {
                    i = j;
                }
            }

            return i;
        }
    }

    private static void applyEnchantmentModifier(EnchantmentHelper.IModifier modifier, ItemStack stack) {
        if (stack != null) {
            NBTTagList nbttaglist = stack.getEnchantmentTagList();

            if (nbttaglist != null) {
                for (int i = 0; i < nbttaglist.tagCount(); ++i) {
                    int j = nbttaglist.getCompoundTagAt(i).getShort("id");
                    int k = nbttaglist.getCompoundTagAt(i).getShort("lvl");

                    if (Enchantments.getEnchantmentById(j) != null) {
                        modifier.calculateModifier(Enchantments.getEnchantmentById(j), k);
                    }
                }
            }
        }
    }

    private static void applyEnchantmentModifierArray(EnchantmentHelper.IModifier modifier, ItemStack[] stacks) {
        for (ItemStack itemstack : stacks) {
            applyEnchantmentModifier(modifier, itemstack);
        }
    }

    public static int getEnchantmentModifierDamage(ItemStack[] stacks, DamageSource source) {
        enchantmentModifierDamage.damageModifier = 0;
        enchantmentModifierDamage.source = source;
        applyEnchantmentModifierArray(enchantmentModifierDamage, stacks);

        if (enchantmentModifierDamage.damageModifier > 25) {
            enchantmentModifierDamage.damageModifier = 25;
        } else if (enchantmentModifierDamage.damageModifier < 0) {
            enchantmentModifierDamage.damageModifier = 0;
        }

        return (enchantmentModifierDamage.damageModifier + 1 >> 1) + enchantmentRand.nextInt((enchantmentModifierDamage.damageModifier >> 1) + 1);
    }

    public static float getModifierForCreature(ItemStack stack, EnumCreatureAttribute creatureAttribute) {
        enchantmentModifierLiving.livingModifier = 0.0F;
        enchantmentModifierLiving.entityLiving = creatureAttribute;
        applyEnchantmentModifier(enchantmentModifierLiving, stack);
        return enchantmentModifierLiving.livingModifier;
    }

    public static void applyThornEnchantments(EntityLivingBase user, Entity attacker) {
        ENCHANTMENT_ITERATOR_HURT.attacker = attacker;
        ENCHANTMENT_ITERATOR_HURT.user = user;

        if (user != null) {
            applyEnchantmentModifierArray(ENCHANTMENT_ITERATOR_HURT, user.getInventory());
        }

        if (attacker instanceof EntityPlayer) {
            applyEnchantmentModifier(ENCHANTMENT_ITERATOR_HURT, Objects.requireNonNull(user).getHeldItem());
        }
    }

    public static void applyArthropodEnchantments(EntityLivingBase user, Entity target) {
        ENCHANTMENT_ITERATOR_DAMAGE.user = user;
        ENCHANTMENT_ITERATOR_DAMAGE.target = target;

        if (user != null) {
            applyEnchantmentModifierArray(ENCHANTMENT_ITERATOR_DAMAGE, user.getInventory());
        }

        if (user instanceof EntityPlayer) {
            applyEnchantmentModifier(ENCHANTMENT_ITERATOR_DAMAGE, user.getHeldItem());
        }
    }

    public static int getKnockbackModifier(EntityLivingBase player) {
        return getEnchantmentLevel(Enchantments.knockback.effectId, player.getHeldItem());
    }

    public static int getFireAspectModifier(EntityLivingBase player) {
        return getEnchantmentLevel(Enchantments.fireAspect.effectId, player.getHeldItem());
    }

    public static int getRespiration(Entity player) {
        return getMaxEnchantmentLevel(Enchantments.respiration.effectId, player.getInventory());
    }

    public static int getDepthStriderModifier(Entity player) {
        return getMaxEnchantmentLevel(Enchantments.depthStrider.effectId, player.getInventory());
    }

    public static int getEfficiencyModifier(EntityLivingBase player) {
        return getEnchantmentLevel(Enchantments.efficiency.effectId, player.getHeldItem());
    }

    public static boolean getSilkTouchModifier(EntityLivingBase player) {
        return getEnchantmentLevel(Enchantments.silkTouch.effectId, player.getHeldItem()) > 0;
    }

    public static int getFortuneModifier(EntityLivingBase player) {
        return getEnchantmentLevel(Enchantments.fortune.effectId, player.getHeldItem());
    }

    public static int getLuckOfSeaModifier(EntityLivingBase player) {
        return getEnchantmentLevel(Enchantments.luckOfTheSea.effectId, player.getHeldItem());
    }

    public static int getLureModifier(EntityLivingBase player) {
        return getEnchantmentLevel(Enchantments.lure.effectId, player.getHeldItem());
    }

    public static int getLootingModifier(EntityLivingBase player) {
        return getEnchantmentLevel(Enchantments.looting.effectId, player.getHeldItem());
    }

    public static boolean getAquaAffinityModifier(EntityLivingBase player) {
        return getMaxEnchantmentLevel(Enchantments.aquaAffinity.effectId, player.getInventory()) > 0;
    }

    public static ItemStack getEnchantedItem(Enchantment enchantmentIn, EntityLivingBase entityIn) {
        for (ItemStack itemstack : entityIn.getInventory()) {
            if (itemstack != null && getEnchantmentLevel(enchantmentIn.effectId, itemstack) > 0) {
                return itemstack;
            }
        }

        return null;
    }

    public static int calcItemStackEnchantability(Random rand, int enchantNum, int power, ItemStack stack) {
        Item item = stack.getItem();
        int i = item.getItemEnchantability();

        if (i <= 0) {
            return 0;
        } else {
            if (power > 15) {
                power = 15;
            }

            int j = rand.nextInt(8) + 1 + (power >> 1) + rand.nextInt(power + 1);
            return enchantNum == 0 ? Math.max(j / 3, 1) : (enchantNum == 1 ? j * 2 / 3 + 1 : Math.max(j, power * 2));
        }
    }

    public static void addRandomEnchantment(Random random, ItemStack stack, int level) {
        List<EnchantmentData> list = buildEnchantmentList(random, stack, level);
        boolean flag = stack.getItem() == Items.book;

        if (flag) {
            stack.setItem(Items.enchanted_book);
        }

        if (list != null) {
            for (EnchantmentData enchantmentdata : list) {
                if (flag) {
                    Items.enchanted_book.addEnchantment(stack, enchantmentdata);
                } else {
                    stack.addEnchantment(enchantmentdata.enchantmentobj, enchantmentdata.enchantmentLevel);
                }
            }
        }

    }

    public static List<EnchantmentData> buildEnchantmentList(Random randomIn, ItemStack itemStackIn, int level) {
        Item item = itemStackIn.getItem();
        int i = item.getItemEnchantability();

        if (i <= 0) {
            return null;
        } else {
            i = i / 2;
            i = 1 + randomIn.nextInt((i >> 1) + 1) + randomIn.nextInt((i >> 1) + 1);
            int j = i + level;
            float f = (randomIn.nextFloat() + randomIn.nextFloat() - 1.0F) * 0.15F;
            int k = (int) ((float) j * (1.0F + f) + 0.5F);

            if (k < 1) {
                k = 1;
            }

            List<EnchantmentData> list = null;
            Map<Integer, EnchantmentData> map = mapEnchantmentData(k, itemStackIn);

            if (map != null && !map.isEmpty()) {
                EnchantmentData enchantmentdata = WeightedRandom.getRandomItem(randomIn, map.values());

                if (enchantmentdata != null) {
                    list = Lists.newArrayList();
                    list.add(enchantmentdata);

                    for (int l = k; randomIn.nextInt(50) <= l; l >>= 1) {
                        Iterator<Integer> iterator = map.keySet().iterator();

                        while (iterator.hasNext()) {
                            Integer integer = iterator.next();
                            boolean flag = true;

                            for (EnchantmentData enchantmentdata1 : list) {
                                if (!enchantmentdata1.enchantmentobj.canApplyTogether(Enchantments.getEnchantmentById(integer))) {
                                    flag = false;
                                    break;
                                }
                            }

                            if (!flag) {
                                iterator.remove();
                            }
                        }

                        if (!map.isEmpty()) {
                            EnchantmentData enchantmentdata2 = WeightedRandom.getRandomItem(randomIn, map.values());
                            list.add(enchantmentdata2);
                        }
                    }
                }
            }

            return list;
        }
    }

    public static Map<Integer, EnchantmentData> mapEnchantmentData(int enchantability, ItemStack itemStack) {
        Item item = itemStack.getItem();
        Map<Integer, EnchantmentData> map = null;
        boolean flag = itemStack.getItem() == Items.book;

        for (Enchantment enchantment : Enchantments.enchantmentsBookList) {
            if (enchantment != null && (enchantment.type.canEnchantItem(item) || flag)) {
                for (int i = enchantment.getMinLevel(); i <= enchantment.getMaxLevel(); ++i) {
                    if (enchantability >= enchantment.getMinEnchantability(i) && enchantability <= enchantment.getMaxEnchantability(i)) {
                        if (map == null) {
                            map = Maps.newHashMap();
                        }

                        map.put(enchantment.effectId, new EnchantmentData(enchantment, i));
                    }
                }
            }
        }

        return map;
    }

    interface IModifier {
        void calculateModifier(Enchantment enchantmentIn, int enchantmentLevel);
    }

    static final class DamageIterator implements EnchantmentHelper.IModifier {
        public EntityLivingBase user;
        public Entity target;

        private DamageIterator() {
        }

        public void calculateModifier(Enchantment enchantmentIn, int enchantmentLevel) {
            enchantmentIn.onEntityDamaged(this.user, this.target, enchantmentLevel);
        }
    }

    static final class HurtIterator implements EnchantmentHelper.IModifier {
        public EntityLivingBase user;
        public Entity attacker;

        private HurtIterator() {
        }

        public void calculateModifier(Enchantment enchantmentIn, int enchantmentLevel) {
            enchantmentIn.onUserHurt(this.user, this.attacker, enchantmentLevel);
        }
    }

    static final class ModifierDamage implements EnchantmentHelper.IModifier {
        public int damageModifier;
        public DamageSource source;

        private ModifierDamage() {
        }

        public void calculateModifier(Enchantment enchantmentIn, int enchantmentLevel) {
            this.damageModifier += enchantmentIn.calcModifierDamage(enchantmentLevel, this.source);
        }
    }

    static final class ModifierLiving implements EnchantmentHelper.IModifier {
        public float livingModifier;
        public EnumCreatureAttribute entityLiving;

        private ModifierLiving() {
        }

        public void calculateModifier(Enchantment enchantmentIn, int enchantmentLevel) {
            this.livingModifier += enchantmentIn.calcDamageByCreature(enchantmentLevel, this.entityLiving);
        }
    }
}
