# General
of.general.ambiguous=ambiguo
of.general.compact=Compatto
of.general.custom=Personalizza
of.general.from=Da
of.general.id=ID
of.general.max=Massimo
of.general.restart=Riavvia
of.general.smart=Intelligente

# Keys
of.key.zoom=Zoom

# Message
of.message.aa.shaders1=L'Antialiasing non è compatibile con le Shaders.
of.message.aa.shaders2=Disattivale per usare questa funzione.

of.message.af.shaders1=Il Filtro Anisotropico non è compatibile con le Shaders
of.message.af.shaders2=Disattivale per usare questa funzione.

of.message.fr.shaders1=Il Rendering Veloce non è compatibile con le Shaders.
of.message.fr.shaders2=Disattivale per usare questa funzione.

of.message.an.shaders1=Il 3D anaglifico non è compatibile con le Shaders.
of.message.an.shaders2=Disattivale per usare questa funzione.

of.message.shaders.aa1=Le Shaders non sono compatibili con l'Antialiasing.
of.message.shaders.aa2=Per disattivarlo, fai clic su Qualità -> Antialiasing.

of.message.shaders.af1=Le Shaders non sono compatibili con il Filtro Anisotropico.
of.message.shaders.af2=Per disattivarlo, fai clic su Qualità -> Filtro Anisotropico.

of.message.shaders.fr1=Le Shaders non sono compatibili con il Rendering Veloce.
of.message.shaders.fr2=Per disattivarlo, fai clic su Prestazione -> Rendering Veloce.

of.message.shaders.an1=Le Shaders non sono compatibili col 3D Anaglifico.
of.message.shaders.an2=Per favore vai su Varie... -> 3D Anaglifico -> No.

of.message.shaders.nv1=Questo pacchetto di shader richiede una versione più recente della OptiFine: %s
of.message.shaders.nv2=Desideri continuare?

of.message.newVersion=C'è una nuova versione di §eOptiFine§f disponibile: §e%s§f.
of.message.java64Bit=Installa §eJava a 64-bit§f per migliorare il Rendering.
of.message.openglError=§eErrore di OpenGL§f: %s (%s)

of.message.shaders.loading=Caricando le Shaders: %s

of.message.other.reset=Ristabilire tutte le impostazioni grafiche?

of.message.loadingVisibleChunks=Caricando chunk visibili

# Personalizzazione skin

of.options.skinCustomisation.ofCape=Mantello OptiFine...

of.options.capeOF.title=Mantello OptiFine
of.options.capeOF.openEditor=Apri Editor Mantello
of.options.capeOF.reloadCape=Ricarica Mantello
of.options.capeOF.copyEditorLink=Copia Link negli Appunti

of.message.capeOF.openEditor=L'Editor del Mantello OptiFine dovrebbe aprirsi nel browser.
of.message.capeOF.openEditorError=Si è verificato un errore cercando di aprire il link nel browser.
of.message.capeOF.reloadCape=Il mantello sarà ricaricato in 15 secondi.

of.message.capeOF.error1=Autenticazione Mojang fallita.
of.message.capeOF.error2=Errore: %s

# Impostazioni Video

options.graphics.tooltip.1=Grafica
options.graphics.tooltip.2=  Rapida  - qualità peggiore, prestazioni migliori
options.graphics.tooltip.3=  Dettagliata - qualità migliore, prestazioni peggiori
options.graphics.tooltip.4=Cambiamenti visibili nelle nuvole, foglie, acqua
options.graphics.tooltip.5=ombre e lati dei blocchi d'erba.

of.options.renderDistance.tiny=Minima
of.options.renderDistance.short=Corta
of.options.renderDistance.normal=Normale
of.options.renderDistance.far=Lontana
of.options.renderDistance.extreme=Estrema
of.options.renderDistance.insane=Folle
of.options.renderDistance.ludicrous=Ridicola

options.renderDistance.tooltip.1=Distanza visibile
options.renderDistance.tooltip.2=  2 Minima - 32m (più veloce)
options.renderDistance.tooltip.3=  8 Normale - 128m (normale)
options.renderDistance.tooltip.4=  16 Lontana - 256m (lenta)
options.renderDistance.tooltip.5=  32 Estrema - 512m (molto lenta!) richiede molte risorse
options.renderDistance.tooltip.6=  48 Folle - 768m, richiede 2GB di RAM allocati
options.renderDistance.tooltip.7=  64 Ridicola - 1024m, richiede 3GB di RAM allocati
options.renderDistance.tooltip.8=Valori sopra 16 sono funzionanti solo in mondi locali.

options.ao.tooltip.1=Luci soffuse
options.ao.tooltip.2=  No - senza luci soffuse (veloce)
options.ao.tooltip.3=  Minimo - luci soffuse semplici (lento)
options.ao.tooltip.4=  Massimo - luci soffuse avanzate (più lento)

options.framerateLimit.tooltip.1=Limite di FPS
options.framerateLimit.tooltip.2=  VSync - limitazione FPS dello schermo (60, 30, 20)
options.framerateLimit.tooltip.3=  5-255 - limite di FPS personalizzato
options.framerateLimit.tooltip.4=  Senza limite - tutti gli FPS possibili (veloce)
options.framerateLimit.tooltip.5=Il limite di FPS riduce gli FPS anche se
options.framerateLimit.tooltip.6=il valore limite non viene raggiunto.
of.options.framerateLimit.vsync=VSync

of.options.AO_LEVEL=Livello di morbidezza
of.options.AO_LEVEL.tooltip.1=livello di illuminazione morbida
of.options.AO_LEVEL.tooltip.2=  No - senza ombre
of.options.AO_LEVEL.tooltip.3=  50%% - ombre chiare
of.options.AO_LEVEL.tooltip.4=  100%% - ombre scure

options.viewBobbing.tooltip.1=Visualizza un movimento del giocatore più realistico.
options.viewBobbing.tooltip.2=Disattivare il mipmap per ottenere un miglior risultato.

options.guiScale.tooltip.1=Scala dell'Interfaccia (GUI)
options.guiScale.tooltip.2=  Auto - dimensione ottimale
options.guiScale.tooltip.3=  Piccola, Normale, Grande - da 1x a 3x
options.guiScale.tooltip.4=  4x verso 10x - disponibile su schermi 4K
options.guiScale.tooltip.5=Valori dispari (1x, 3x, 5x ...) non compatibili con Unicode.
options.guiScale.tooltip.6=Una GUI più piccola potrebbe migliorare le prestazioni.

options.vbo=VBO
options.vbo.tooltip.1=Vertex Buffer Objects (abbreviato in VBO)
options.vbo.tooltip.2=In alternativa è il rendering che di solito è più
options.vbo.tooltip.3=veloce (5-10%%) alla forma default.

options.gamma.tooltip.1=Modifica la luminosità
options.gamma.tooltip.2=  Scura - Luminosità di default
options.gamma.tooltip.3=  1-99%% - Luminosità personalizzata
options.gamma.tooltip.4=  Luminoso - Luminosità al massimo
options.gamma.tooltip.5=Questa opzione non cambia particolarmente
options.gamma.tooltip.6=la luminosità degli oggetti più scuri.

options.anaglyph.tooltip.1=Visione in 3D
options.anaglyph.tooltip.2=Attivare un effetto stereoscopico utilizzando un colore
options.anaglyph.tooltip.3=differente per ogni occhio.
options.anaglyph.tooltip.4=NOTA: Richiede occhiali anaglifici (rossi e blu).

options.attackIndicator.tooltip.1=Configura posizione indicatore d'attacco
options.attackIndicator.tooltip.2=  Mirino - sotto il mirino
options.attackIndicator.tooltip.3=  Hotbar - accanto alla hotbar
options.attackIndicator.tooltip.4=  OFF - nessun indicatore d'attacco
options.attackIndicator.tooltip.5=L'indicatore d'attacco mostra la potenza
options.attackIndicator.tooltip.6=dell'attacco dello strumento equipaggiato.

of.options.ALTERNATE_BLOCKS=Blocchi alternati
of.options.ALTERNATE_BLOCKS.tooltip.1=Blocchi con texture alternate
of.options.ALTERNATE_BLOCKS.tooltip.2=Utilizzare texture alternative per lo stesso blocco.
of.options.ALTERNATE_BLOCKS.tooltip.3=Dipende dal pacchetto di risorse usato.

of.options.FOG_FANCY=Nebbia
of.options.FOG_FANCY.tooltip.1=Tipo di nebbia
of.options.FOG_FANCY.tooltip.2=  Veloce - nebbia più veloce, sembra squallido
of.options.FOG_FANCY.tooltip.3=  Dettagliata - nebbia più lenta, sembra migliore
of.options.FOG_FANCY.tooltip.4=  No - senza foschia, super veloce
of.options.FOG_FANCY.tooltip.5=La nebbia dettagliata è disponibile solo se
of.options.FOG_FANCY.tooltip.6=la tua scheda grafica te lo permette.

of.options.FOG_START=Distanza della nebbia
of.options.FOG_START.tooltip.1=Distanza in cui la nebbia comincia
of.options.FOG_START.tooltip.2=  0.2 - la nebbia inizia vicina al giocatore
of.options.FOG_START.tooltip.3=  0.8 - la nebbia inizia lontana dal giocatore
of.options.FOG_START.tooltip.4=Normalmente questa opzione non affligge le prestazioni.

of.options.CHUNK_LOADING=Caricamento dei Chunk
of.options.CHUNK_LOADING.tooltip.1=Velocità del caricamento dei Chunk
of.options.CHUNK_LOADING.tooltip.2=  Default - FPS instabili al caricamento dei Chunk
of.options.CHUNK_LOADING.tooltip.3=  Morbido - FPS stabili
of.options.CHUNK_LOADING.tooltip.4=  Multinucleo - FPS stabili e una velocità tre volte superiore
of.options.CHUNK_LOADING.tooltip.5=Le opzioni "Morbido" e "Multinucleo" eliminano gli intoppi nella
of.options.CHUNK_LOADING.tooltip.6=lentezza causata nel caricamento dei Chunk.
of.options.CHUNK_LOADING.tooltip.7=Il Multinucleo può accelerare il carico fino a tre volte e migliorare
of.options.CHUNK_LOADING.tooltip.8=gli FPS e usare un secondo nucleo della CPU.
of.options.chunkLoading.smooth=Morbido
of.options.chunkLoading.multiCore=Multinucleo

of.options.shaders.SHADER_PACK=Pacchetto shaders

of.options.shaders=Shaders...
of.options.shadersTitle=Shaders

of.options.shaders.packNone=(NO SHADERS)
of.options.shaders.packDefault=(SHADERS INTERNE)

of.options.shaders.shadersFolder=Cartella shaders
of.options.shaders.shaderOptions=Opzioni shaders

of.options.shaderOptionsTitle=Opzioni shaders

of.options.quality=Qualità...
of.options.qualityTitle=Opzioni grafiche

of.options.details=Dettagli...
of.options.detailsTitle=Opzioni grafiche

of.options.performance=Prestazioni...
of.options.performanceTitle=Prestazioni

of.options.animations=Animazioni...
of.options.animationsTitle=Opzioni animazioni

of.options.other=Varie...
of.options.otherTitle=Opzioni varie

of.options.other.reset=Ripristino impostazioni...

of.shaders.profile=Profilo

# Qualità

of.options.mipmap.bilinear=Bilineare
of.options.mipmap.linear=Lineare
of.options.mipmap.nearest=Vicino
of.options.mipmap.trilinear=Trilinere

options.mipmapLevels.tooltip.1=Effetto visivo che consente di visualizzare oggetti distanti
options.mipmapLevels.tooltip.2=migliori lisciando i dettagli delle texture
options.mipmapLevels.tooltip.3=  No - senza morbidezza
options.mipmapLevels.tooltip.4=  1 - morbidezza minima
options.mipmapLevels.tooltip.5=  4 - morbidezza massima
options.mipmapLevels.tooltip.6=Normalmente questa opzione non influisce sulle prestazioni.

of.options.MIPMAP_TYPE=Tipo di mipmap
of.options.MIPMAP_TYPE.tooltip.1=Effetto visivo che consente di visualizzare oggetti distanti
of.options.MIPMAP_TYPE.tooltip.2=migliori lisciando i dettagli delle texture
of.options.MIPMAP_TYPE.tooltip.3=  Vicino - morbidezza scarsa (più veloce)
of.options.MIPMAP_TYPE.tooltip.4=  Lineare - morbidezza normale
of.options.MIPMAP_TYPE.tooltip.5=  Bilineare - morbidezza sottile
of.options.MIPMAP_TYPE.tooltip.6=  Trilineare - morbidezza molto sottile (più lenta)

of.options.ANTIALIASING=Antialiasing
of.options.ANTIALIASING.tooltip.1=Antialiasing attenua linee e i bordi dei blocchi
of.options.ANTIALIASING.tooltip.2=  No - (per default) senza antialiasing (più veloce)
of.options.ANTIALIASING.tooltip.3=  FXAA 2x, 4x - livello di antialiasing morbido (più lento)
of.options.ANTIALIASING.tooltip.4=Ammorbidisce i denti di sega e migliora la nitidezza.
of.options.ANTIALIASING.tooltip.5=Gli FPS può diminuire considerevolmente quando attivato.
of.options.ANTIALIASING.tooltip.6=Alcune schede grafiche non supportano tutti i livelli.
of.options.ANTIALIASING.tooltip.7=La modifica verrà applicata al RIAVVIO del gioco!

of.options.shaders.NORMAL_MAP=Mappatura normale
of.options.shaders.NORMAL_MAP.tooltip.1=Mappatura normale
of.options.shaders.NORMAL_MAP.tooltip.2=  Sì - (default) abilita mappatura normale
of.options.shaders.NORMAL_MAP.tooltip.3=  No - disabilita mappatura normale
of.options.shaders.NORMAL_MAP.tooltip.4=Le mappature normali possono essere usate dai pacchetti shader
of.options.shaders.NORMAL_MAP.tooltip.5=per simulare geometria 3D su superfici piatte.
of.options.shaders.NORMAL_MAP.tooltip.6=Le texture mappature normali sono supportate
of.options.shaders.NORMAL_MAP.tooltip.7=da questo pacchetto di risorse.

of.options.shaders.SPECULAR_MAP=Mappatura speculare
of.options.shaders.SPECULAR_MAP.tooltip.1=Mappatura speculare
of.options.shaders.SPECULAR_MAP.tooltip.2=  Sì - (default) abilita mappature speculari
of.options.shaders.SPECULAR_MAP.tooltip.3=  No - disabilita mappature speculari
of.options.shaders.SPECULAR_MAP.tooltip.4=Le mappature speculari possono essere usate dai pacchetti shader
of.options.shaders.SPECULAR_MAP.tooltip.5=per simulare speciali effetti di riflessione.
of.options.shaders.SPECULAR_MAP.tooltip.6=Le texture mappature speculari sono supportate
of.options.shaders.SPECULAR_MAP.tooltip.7=da questo pacchetto di risorse.

of.options.shaders.RENDER_RES_MUL=Qualità rendering
of.options.shaders.RENDER_RES_MUL.tooltip.1=Qualità rendering
of.options.shaders.RENDER_RES_MUL.tooltip.2=  0.5x - bassa (più veloce)
of.options.shaders.RENDER_RES_MUL.tooltip.3=  1x - normale (default)
of.options.shaders.RENDER_RES_MUL.tooltip.4=  2x - alta (più lenta)
of.options.shaders.RENDER_RES_MUL.tooltip.5=Qualità rendering controlla la grandezza delle texture
of.options.shaders.RENDER_RES_MUL.tooltip.6=che il pacchetto shaders renderizza.
of.options.shaders.RENDER_RES_MUL.tooltip.7=Valori più bassi sono buoni per schermi 4K.
of.options.shaders.RENDER_RES_MUL.tooltip.8=Valori più alti funzionano come un filtro antialiasing.

of.options.shaders.SHADOW_RES_MUL=Qualità ombre
of.options.shaders.SHADOW_RES_MUL.tooltip.1=Qualità ombre
of.options.shaders.SHADOW_RES_MUL.tooltip.2=  0.5x - bassa (più veloce)
of.options.shaders.SHADOW_RES_MUL.tooltip.3=  1x - normale (default)
of.options.shaders.SHADOW_RES_MUL.tooltip.4=  2x - alta (più lenta)
of.options.shaders.SHADOW_RES_MUL.tooltip.5=Qualità ombre controlla la grandezza della mappatura ombre
of.options.shaders.SHADOW_RES_MUL.tooltip.6=utilizzata dal pacchetto di shaders.
of.options.shaders.SHADOW_RES_MUL.tooltip.7=Valori più bassi =inesatta, ombre grossolane.
of.options.shaders.SHADOW_RES_MUL.tooltip.8=Valori più alti =dettagliata, ombre di qualità.

of.options.shaders.HAND_DEPTH_MUL=Profondità campo
of.options.shaders.HAND_DEPTH_MUL.tooltip.1=Profondità campo
of.options.shaders.HAND_DEPTH_MUL.tooltip.2=  0.5x - mano vicina alla telecamera
of.options.shaders.HAND_DEPTH_MUL.tooltip.3=  1x - (default)
of.options.shaders.HAND_DEPTH_MUL.tooltip.4=  2x - mano lontana dalla telecamera
of.options.shaders.HAND_DEPTH_MUL.tooltip.5=Profondità campo controlla quanto lontano gli oggetti
of.options.shaders.HAND_DEPTH_MUL.tooltip.6=tenuti in mano sono dalla telecamera.
of.options.shaders.HAND_DEPTH_MUL.tooltip.7=Per i pacchetti di shader che usano un effetto blur questa
of.options.shaders.HAND_DEPTH_MUL.tooltip.8=opzione dovrebbe cambiare l'effetto degli oggetti tenuti.

of.options.shaders.OLD_HAND_LIGHT=Luce mano secondaria
of.options.shaders.OLD_HAND_LIGHT.tooltip.1=Luce mano secondaria
of.options.shaders.OLD_HAND_LIGHT.tooltip.2=  Default - controllata dal pacchetto shader
of.options.shaders.OLD_HAND_LIGHT.tooltip.3=  Sì - usa luce mano secondaria
of.options.shaders.OLD_HAND_LIGHT.tooltip.4=  No - usa normale luce
of.options.shaders.OLD_HAND_LIGHT.tooltip.5=Luce mano secondaria permette ai pacchetti shader
of.options.shaders.OLD_HAND_LIGHT.tooltip.6=che riconoscono la luce di oggetti tenuti solo
of.options.shaders.OLD_HAND_LIGHT.tooltip.7=nella mano primaria di funzionare anche con la mano secondaria.

of.options.shaders.OLD_LIGHTING=Luce classica
of.options.shaders.OLD_LIGHTING.tooltip.1=Luce classica
of.options.shaders.OLD_LIGHTING.tooltip.2=  Default - controllata dal pacchetto shader
of.options.shaders.OLD_LIGHTING.tooltip.3=  Sì - usa luce classica
of.options.shaders.OLD_LIGHTING.tooltip.4=  No - non usare luce classica
of.options.shaders.OLD_LIGHTING.tooltip.5=Luce classica controlla la luce applicata 
of.options.shaders.OLD_LIGHTING.tooltip.6=al lato dei blocchi vanilla.
of.options.shaders.OLD_LIGHTING.tooltip.7=I pacchetti shader che usano ombre di solito forniscono
of.options.shaders.OLD_LIGHTING.tooltip.8=una migliore luce in base alla posizione del Sole.

of.options.shaders.DOWNLOAD=Scarica shaders
of.options.shaders.DOWNLOAD.tooltip.1=Scarica shaders
of.options.shaders.DOWNLOAD.tooltip.2=
of.options.shaders.DOWNLOAD.tooltip.3=Apre la pagina del pacchetto di shaders nel browser.
of.options.shaders.DOWNLOAD.tooltip.4=Metti il pacchetto di shaders scaricato nella "Cartella Shaders"
of.options.shaders.DOWNLOAD.tooltip.5=e appariranno nella lista di shaders installate.

of.options.AF_LEVEL=Filtro Anisotropico
of.options.AF_LEVEL.tooltip.1=Filtro Anisotropico
of.options.AF_LEVEL.tooltip.2=  No - (Default) dettaglio texture Normale (veloce)
of.options.AF_LEVEL.tooltip.3=  2-16 - raffinazione texture tramite mipmap (più lento)
of.options.AF_LEVEL.tooltip.4=Questo filtro consente di recuperare i dettagli utilizzando mipmap
of.options.AF_LEVEL.tooltip.5=Gli FPS possono diminuire considerevolmente quando attivato.

of.options.CLEAR_WATER=Acqua cristallina
of.options.CLEAR_WATER.tooltip.1=Acqua cristallina
of.options.CLEAR_WATER.tooltip.2=  Sì - acqua limpida e trasparente
of.options.CLEAR_WATER.tooltip.3=  No - acqua default

of.options.RANDOM_ENTITIES=Creature casuali
of.options.RANDOM_ENTITIES.tooltip.1=Randomizza texture delle creature
of.options.RANDOM_ENTITIES.tooltip.2=  No - non casuale, più veloce
of.options.RANDOM_ENTITIES.tooltip.3=  Sì - con randomizzazione, più lento
of.options.RANDOM_ENTITIES.tooltip.4=Permette alle entità di avere più texture.
of.options.RANDOM_ENTITIES.tooltip.5=Richiede un pacchetto di risorse con più texture per entità.

of.options.BETTER_GRASS=Prato migliorato
of.options.BETTER_GRASS.tooltip.1=Texture dei lati del blocco di terra migliorati aggiungendo prato
of.options.BETTER_GRASS.tooltip.2=  No - prato normale (per default), più veloce
of.options.BETTER_GRASS.tooltip.3=  Rapido - unica texture d'erba applicata
of.options.BETTER_GRASS.tooltip.4=  Dettagliata - texture dinamica, molto lento

of.options.BETTER_SNOW=Neve migliorata
of.options.BETTER_SNOW.tooltip.1=Neve migliorata
of.options.BETTER_SNOW.tooltip.2=  No - neve default, più veloce
of.options.BETTER_SNOW.tooltip.3=  Sì - neve dettagliata, più lento
of.options.BETTER_SNOW.tooltip.4=Mostra la neve sotto i blocchi trasparenti
of.options.BETTER_SNOW.tooltip.5=(Recinzioni, erba) quando li si confina con la neve.

of.options.CUSTOM_FONTS=Font personalizzati
of.options.CUSTOM_FONTS.tooltip.1=Font personalizzati
of.options.CUSTOM_FONTS.tooltip.2=  Sì - font personalizzati (per default), più lento
of.options.CUSTOM_FONTS.tooltip.3=  No - font di default, più veloce
of.options.CUSTOM_FONTS.tooltip.4=Alcuni pacchetti di risorse contengono font
of.options.CUSTOM_FONTS.tooltip.5=personalizzati.

of.options.CUSTOM_COLORS=Colori personalizzati
of.options.CUSTOM_COLORS.tooltip.1=Colori personalizzati
of.options.CUSTOM_COLORS.tooltip.2=  Sì - colori personalizzati (per default), più lenti
of.options.CUSTOM_COLORS.tooltip.3=  No - colori default, più veloci
of.options.CUSTOM_COLORS.tooltip.4=Alcuni pacchetti di risorse contengono colori
of.options.CUSTOM_COLORS.tooltip.5=personalizzati.

of.options.SWAMP_COLORS=Colore delle paludi
of.options.SWAMP_COLORS.tooltip.1=Colore delle paludi
of.options.SWAMP_COLORS.tooltip.2=  Sì - colore delle paludi (per default), più lento
of.options.SWAMP_COLORS.tooltip.3=  No - senza colori nelle paludi, più veloce
of.options.SWAMP_COLORS.tooltip.4=Comprende: erba, acqua, foglie e rampicanti.

of.options.SMOOTH_BIOMES=Morbidezza dei biomi
of.options.SMOOTH_BIOMES.tooltip.1=Morbidezza dei confini dei biomi
of.options.SMOOTH_BIOMES.tooltip.2=  Sì - bordi lisci bioma (default), più lento
of.options.SMOOTH_BIOMES.tooltip.3=  No - senza bordi lisci dei biomi, più veloce
of.options.SMOOTH_BIOMES.tooltip.4=Il livellamento viene eseguito
of.options.SMOOTH_BIOMES.tooltip.5=dai blocchi di colore gradiente vicino ai confini
of.options.SMOOTH_BIOMES.tooltip.6=Comprende: erba, acqua, foglie e rampicanti.

of.options.CONNECTED_TEXTURES=Blocchi uniti
of.options.CONNECTED_TEXTURES.tooltip.1=Le texture uniscono blocchi determinati
of.options.CONNECTED_TEXTURES.tooltip.2=  No - non unirli, più veloce
of.options.CONNECTED_TEXTURES.tooltip.3=  Veloce - unirli in forma più semplice
of.options.CONNECTED_TEXTURES.tooltip.4=  Dettagliate - unirli in forma migliorata
of.options.CONNECTED_TEXTURES.tooltip.5=Unisce le texture di vetro, pietra arenaria
of.options.CONNECTED_TEXTURES.tooltip.6=e biblioteche per ottenere i blocchi.
of.options.CONNECTED_TEXTURES.tooltip.7=Non tutti i pacchetti di risorse supportano questa opzione.

of.options.NATURAL_TEXTURES=Texture naturali
of.options.NATURAL_TEXTURES.tooltip.1=Texture naturali
of.options.NATURAL_TEXTURES.tooltip.2=  No - senza texture naturali (per default)
of.options.NATURAL_TEXTURES.tooltip.3=  Sì - con texture naturali
of.options.NATURAL_TEXTURES.tooltip.4=Questa opzione rimuove i blocchi del modello
of.options.NATURAL_TEXTURES.tooltip.5=che viene creato mettendo blocchi dello stesso tipo.
of.options.NATURAL_TEXTURES.tooltip.6=Si usano varianti ruotate della texture
of.options.NATURAL_TEXTURES.tooltip.7=base del blocco.
of.options.NATURAL_TEXTURES.tooltip.8=Non tutti i pacchetti di risorse supportano questa opzione.

of.options.EMISSIVE_TEXTURES=Texture emissive
of.options.EMISSIVE_TEXTURES.tooltip.1=Texture emissive
of.options.EMISSIVE_TEXTURES.tooltip.2=  Sì - no texture emissive (default)
of.options.EMISSIVE_TEXTURES.tooltip.3=  No - utilizza texture emissive
of.options.EMISSIVE_TEXTURES.tooltip.4=Le texture emissive sono renderizzate come copertura
of.options.EMISSIVE_TEXTURES.tooltip.5=con massima luminosità. Possono essere usate come
of.options.EMISSIVE_TEXTURES.tooltip.6=parti di luce della texture base.
of.options.EMISSIVE_TEXTURES.tooltip.7=Le texture emissive sono supportate dal
of.options.EMISSIVE_TEXTURES.tooltip.8=pacchetto di risorse attuale.

of.options.CUSTOM_SKY=Cielo personalizzato
of.options.CUSTOM_SKY.tooltip.1=Texture personalizzata
of.options.CUSTOM_SKY.tooltip.2=  Sì - cielo personalizzato, più lento
of.options.CUSTOM_SKY.tooltip.3=  No - cielo di default, più veloce
of.options.CUSTOM_SKY.tooltip.4=Non tutti i pacchetti di risorse supportano questa opzione.

of.options.CUSTOM_ITEMS=Oggetti personalizzati
of.options.CUSTOM_ITEMS.tooltip.1=Texture personalizzata di oggetti (item)
of.options.CUSTOM_ITEMS.tooltip.2=  Sì - oggetti personalizzati (per default), più lento
of.options.CUSTOM_ITEMS.tooltip.3=  No - oggetti default, più veloce
of.options.CUSTOM_ITEMS.tooltip.4=Non tutti i pacchetti di risorse supportano questa opzione.

of.options.CUSTOM_ENTITY_MODELS=Entità personalizzate
of.options.CUSTOM_ENTITY_MODELS.tooltip.1=Texture personalizzate delle entità
of.options.CUSTOM_ENTITY_MODELS.tooltip.2=  Sì - vari modelli entità (default), più lento
of.options.CUSTOM_ENTITY_MODELS.tooltip.3=  No - modelli di default, più veloce
of.options.CUSTOM_ENTITY_MODELS.tooltip.4=Non tutti i pacchetti di risorse supportano questa opzione.

of.options.CUSTOM_GUIS=GUI personalizzate
of.options.CUSTOM_GUIS.tooltip.1=GUI personalizzate
of.options.CUSTOM_GUIS.tooltip.2=  Sì - GUI personalizzate (default), più lento
of.options.CUSTOM_GUIS.tooltip.3=  No - GUI di default, più veloce
of.options.CUSTOM_GUIS.tooltip.4=Le GUI personalizzate sono supportate dall'attuale pacchetto di risorse.

# Details

of.options.CLOUDS=Nuvole
of.options.CLOUDS.tooltip.1=Qualità delle nuvole
of.options.CLOUDS.tooltip.2=  Predefinita - dipende dalle impostazioni della qualità grafica
of.options.CLOUDS.tooltip.3=  Veloce - peggiore qualità, migliori prestazioni
of.options.CLOUDS.tooltip.4=  Dettagliata - maggiore qualità, le prestazioni peggiori
of.options.CLOUDS.tooltip.5=  No - senza nuvole, prestazioni super-veloci
of.options.CLOUDS.tooltip.6=Le nubi veloci si renderizzano in 2D
of.options.CLOUDS.tooltip.7=Le nubi dettagliate si renderizzano in 3D

of.options.CLOUD_HEIGHT=Altezza nuvole
of.options.CLOUD_HEIGHT.tooltip.1=Aumento dell'altezza dello strato di nubi
of.options.CLOUD_HEIGHT.tooltip.2=  No - Altezza default
of.options.CLOUD_HEIGHT.tooltip.3=Verso il limite del mondo

of.options.TREES=Alberi
of.options.TREES.tooltip.1=Qualità delle foglie
of.options.TREES.tooltip.2=  Predefinita - dipende dalle impostazioni della qualità grafica
of.options.TREES.tooltip.3=  Veloce - bassa qualità, migliori prestazioni
of.options.TREES.tooltip.4=  Intelligente - più qualità, rendimento medio
of.options.TREES.tooltip.5=  Dettagliata - alta qualità, prestazioni peggiori
of.options.TREES.tooltip.6=La qualità veloce lascia le foglie senza trasparenza.
of.options.TREES.tooltip.7="Dettagliata" e "Intelligente" fanno mantenere la trasparenza.

of.options.RAIN=Pioggia/Neve
of.options.RAIN.tooltip.1=Pioggia/Neve
of.options.RAIN.tooltip.2=  Predefinita - dipende dalle impostazioni della qualità grafica
of.options.RAIN.tooltip.3=  Rapida - pioggia / neve leggera, più veloce
of.options.RAIN.tooltip.4=  Dettagliata - pioggia / neve pesante, lento
of.options.RAIN.tooltip.5=  No - senza neve e pioggia, velocissimo
of.options.RAIN.tooltip.6=Anche se la pioggia è disattivata, i suoi suoni si
of.options.RAIN.tooltip.7=possono comunque sentire.

of.options.SKY=Cielo
of.options.SKY.tooltip.1=Cielo
of.options.SKY.tooltip.2=  Sì - cielo visibile, più lento
of.options.SKY.tooltip.3=  No - cielo non visibile, più veloce
of.options.SKY.tooltip.4=Anche se disabilitato, la Luna e il Sole si possono
of.options.SKY.tooltip.5=sempre vedere.

of.options.STARS=Cielo stellato
of.options.STARS.tooltip.1=Visibilità di stelle nel cielo notturno
of.options.STARS.tooltip.2=  Sì - stellato, più lento
of.options.STARS.tooltip.3=  No - senza stelle, più veloce

of.options.SUN_MOON=Sole/Luna
of.options.SUN_MOON.tooltip.1=Visibilità del Sole e della Luna
of.options.SUN_MOON.tooltip.2=  Sì - Il Sole e la Luna sono visibili (lento)
of.options.SUN_MOON.tooltip.3=  No - Il Sole e la Luna non sono visibili (veloce)

of.options.SHOW_CAPES=Mantelli
of.options.SHOW_CAPES.tooltip.1=Mantelli dei giocatori
of.options.SHOW_CAPES.tooltip.2=  Sì - mostra i mantelli dei giocatori
of.options.SHOW_CAPES.tooltip.3=  No - nascondi i mantelli dei giocatori

of.options.TRANSLUCENT_BLOCKS=Blocchi traslucidi
of.options.TRANSLUCENT_BLOCKS.tooltip.1=Qualità dei blocchi traslucidi
of.options.TRANSLUCENT_BLOCKS.tooltip.2=  Dettagliata - perfetta miscela di colori (default)
of.options.TRANSLUCENT_BLOCKS.tooltip.3=  Rapida - miscela di colori approssimativa (più veloce)
of.options.TRANSLUCENT_BLOCKS.tooltip.4=Questa opzione riguarda i blocchi traslucidi che hanno
of.options.TRANSLUCENT_BLOCKS.tooltip.5=colore, ma solo quando posti uno dietro
of.options.TRANSLUCENT_BLOCKS.tooltip.6=l'altro ad un blocco vuoto.
of.options.TRANSLUCENT_BLOCKS.tooltip.7=Modifica: vetrate, acqua, ghiaccio ...

of.options.HELD_ITEM_TOOLTIPS=Nome oggetto tenuto
of.options.HELD_ITEM_TOOLTIPS.tooltip.1=Mostra il nome dell'oggetto tenuto in mano
of.options.HELD_ITEM_TOOLTIPS.tooltip.2=  Sì - mostra il nome (com'è tutt'ora)
of.options.HELD_ITEM_TOOLTIPS.tooltip.3=  No - non mostrare il nome (come in passato)

of.options.DROPPED_ITEMS=Oggetti a terra
of.options.DROPPED_ITEMS.tooltip.1=Qualità degli oggetti a terra non raccolti
of.options.DROPPED_ITEMS.tooltip.2=  Predefinito - dipende dalle impostazioni grafiche
of.options.DROPPED_ITEMS.tooltip.3=  Veloce - oggetti in 2D, più veloce ma più brutto
of.options.DROPPED_ITEMS.tooltip.4=  Dettagliata - oggetti in 3D, più lento ma più bello

options.entityShadows.tooltip.1=Ombre delle entità
options.entityShadows.tooltip.2=  Sì - mostrare ombre alle entità
options.entityShadows.tooltip.3=  No - non mostrare ombre alle entità

of.options.VIGNETTE=Bordi
of.options.VIGNETTE.tooltip.1=Scurisce gli angoli dello schermo
of.options.VIGNETTE.tooltip.2=  Predefinito - dipende dalle impostazioni della qualità grafica
of.options.VIGNETTE.tooltip.3=  Bordi disattivati (più veloce) - Veloce
of.options.VIGNETTE.tooltip.4=  Dettagliato - effetto vignetta (più lento)
of.options.VIGNETTE.tooltip.5=Gli FPS possono diminuire drasticamente all'attivarsi, 
of.options.VIGNETTE.tooltip.6=soprattutto a schermo intero.
of.options.VIGNETTE.tooltip.7=I bordi possono essere disattivati senza problemi.

of.options.DYNAMIC_FOV=Visione campo dinamico
of.options.DYNAMIC_FOV.tooltip.1=Visione campo dinamico
of.options.DYNAMIC_FOV.tooltip.2=  Sì - attivarlo (per default)
of.options.DYNAMIC_FOV.tooltip.3=  No - disattivarlo
of.options.DYNAMIC_FOV.tooltip.4=Cambiare la visione del campo dinamico (FOV)
of.options.DYNAMIC_FOV.tooltip.5=quando si corre, cammina e si usa un arco.

of.options.DYNAMIC_LIGHTS=Illum. dinamica
of.options.DYNAMIC_LIGHTS.tooltip.1=Illuminazione dinamica
of.options.DYNAMIC_LIGHTS.tooltip.2=  No - no illum. dinamica (per default)
of.options.DYNAMIC_LIGHTS.tooltip.3=  Rapida - illum. dinamica veloce (aggiornam. 500ms)
of.options.DYNAMIC_LIGHTS.tooltip.4=  Dettagliata - illum. dinamica live (aggiornam. live)
of.options.DYNAMIC_LIGHTS.tooltip.5=
of.options.DYNAMIC_LIGHTS.tooltip.6=Permette a certi oggetti di emettere luce anche se non piazzati.
of.options.DYNAMIC_LIGHTS.tooltip.7=Quando tieni in mano uno di questi oggetti o li getti a terra
of.options.DYNAMIC_LIGHTS.tooltip.8=l'ambiente si illumina (comprende: torce, luminite...).

options.biomeBlendRadius.tooltip.1=Ammorbidisce la transizione di colori tra biomi
options.biomeBlendRadius.tooltip.2=  No - nessuna modifica (più veloce)
options.biomeBlendRadius.tooltip.3=  5x5 - unione normale (per default)
options.biomeBlendRadius.tooltip.4=  15x15 - unione massima (più lenta)
options.biomeBlendRadius.tooltip.5=Valori più alti potrebbero causare picchi
options.biomeBlendRadius.tooltip.6=di lag al caricamento dei chunk.

# Performance

of.options.SMOOTH_FPS=Stabilizzazione FPS
of.options.SMOOTH_FPS.tooltip.1=Stabilizza l'FPS livellando il buffer driver grafico
of.options.SMOOTH_FPS.tooltip.2=  No - senza stabilizzazione
of.options.SMOOTH_FPS.tooltip.3=  Sì - con stabilizzazione
of.options.SMOOTH_FPS.tooltip.4=Questa opzione dipende dalla scheda grafica
of.options.SMOOTH_FPS.tooltip.5=e il suo effetto non sempre è evidente.

of.options.SMOOTH_WORLD=Stabilizzazione mondo
of.options.SMOOTH_WORLD.tooltip.1=Elimina i disturbi causati da lag server interno
of.options.SMOOTH_WORLD.tooltip.2=  No - senza stabilizzazione
of.options.SMOOTH_WORLD.tooltip.3=  Sì - con stabilizzazione
of.options.SMOOTH_WORLD.tooltip.4=Questa opzione stabilizza FPS e distribuisce
of.options.SMOOTH_WORLD.tooltip.5=il carico del server interno.
of.options.SMOOTH_WORLD.tooltip.6=Non funziona in multigiocatore.

of.options.FAST_RENDER=Rendering veloce
of.options.FAST_RENDER.tooltip.1=Rendering veloce
of.options.FAST_RENDER.tooltip.2=  No - rendering standard (lento)
of.options.FAST_RENDER.tooltip.3=  Sì - rendering ottimizzato (veloce)
of.options.FAST_RENDER.tooltip.4=Usa un algoritmo di rendering migliorato diminuendo il
of.options.FAST_RENDER.tooltip.5=consumo di GPU e aumenta gli FPS.

of.options.FAST_MATH=Calcoli ottimizzati
of.options.FAST_MATH.tooltip.1=Calcoli ottimizzati
of.options.FAST_MATH.tooltip.2=  No - standard (per default)
of.options.FAST_MATH.tooltip.3=  Sì - Calcoli ottimizzati
of.options.FAST_MATH.tooltip.4=Utilizzare le funzioni sin() e cos() di Java in forma
of.options.FAST_MATH.tooltip.5=ottimizzata per migliorare l'uso della cache e della CPU
of.options.FAST_MATH.tooltip.6=per migliorare gli FPS.

of.options.CHUNK_UPDATES=Aggiornamento dei Chunk
of.options.CHUNK_UPDATES.tooltip.1=Aggiornamento dei Chunk
of.options.CHUNK_UPDATES.tooltip.2=  1 - caricamento lento, FPS (default)
of.options.CHUNK_UPDATES.tooltip.3=  3 - caricamento più veloce, FPS bassi
of.options.CHUNK_UPDATES.tooltip.4=  5 - caricamento super-veloce, FPS molto bassi
of.options.CHUNK_UPDATES.tooltip.5=Numero di renderizzazioni chunk per frame.
of.options.CHUNK_UPDATES.tooltip.6=Valori più alti possono destabilizzare gli FPS.

of.options.CHUNK_UPDATES_DYNAMIC=Aggiornamento dinamico
of.options.CHUNK_UPDATES_DYNAMIC.tooltip.1=Aggiornamento dei Chunk in modo dinamico
of.options.CHUNK_UPDATES_DYNAMIC.tooltip.2=  No - (default) fotogramma di carico normale
of.options.CHUNK_UPDATES_DYNAMIC.tooltip.3=  Sì - più aggiornamenti mentre si è fermi
of.options.CHUNK_UPDATES_DYNAMIC.tooltip.4=Questa opzione forza l'aggiornamento dei Chunk mentre
of.options.CHUNK_UPDATES_DYNAMIC.tooltip.5=si sta fermi per caricare il mondo più velocemente.

of.options.LAZY_CHUNK_LOADING=Caricamento casuale
of.options.LAZY_CHUNK_LOADING.tooltip.1=Caricamento dei Chunk casuale
of.options.LAZY_CHUNK_LOADING.tooltip.2=  No - caricamente dei Chunk in stile default
of.options.LAZY_CHUNK_LOADING.tooltip.3=  Sì - caricamento casuale Chunk (stabilizza il caricamento)
of.options.LAZY_CHUNK_LOADING.tooltip.4=Ammorbidisce il caricamento dei Chunk distribuendolo durante
of.options.LAZY_CHUNK_LOADING.tooltip.5=vari ticks. Disattiva se alcune parti del mondo
of.options.LAZY_CHUNK_LOADING.tooltip.6=non si caricano correttamente.
of.options.LAZY_CHUNK_LOADING.tooltip.7=Funziona solo su mondi locali con un core CPU.

of.options.RENDER_REGIONS=Renderizza aree
of.options.RENDER_REGIONS.tooltip.1=Renderizza aree
of.options.RENDER_REGIONS.tooltip.2=  Sì - (default) non usare renderizzazione aree
of.options.RENDER_REGIONS.tooltip.3=  No - usa renderizzazione aree
of.options.RENDER_REGIONS.tooltip.4=La renderizzazione aree permette un rendering migliore
of.options.RENDER_REGIONS.tooltip.5=del terreno a grandi distanze. Meglio se il VBO è attivo.
of.options.RENDER_REGIONS.tooltip.6=Non raccomandata per schede grafiche integrate.

of.options.SMART_ANIMATIONS=Animazioni smart
of.options.SMART_ANIMATIONS.tooltip.1=Animazioni smart
of.options.SMART_ANIMATIONS.tooltip.2=  No - (default) non usare le animazioni smart
of.options.SMART_ANIMATIONS.tooltip.3=  Sì - usa le animazioni smart
of.options.SMART_ANIMATIONS.tooltip.4=Con le animazioni smart attive solo le
of.options.SMART_ANIMATIONS.tooltip.5=texture visibili al giocatore saranno animate.
of.options.SMART_ANIMATIONS.tooltip.6=Vengono ridotti picchi di lag e aumentati gli FPS.
of.options.SMART_ANIMATIONS.tooltip.7=Particolarmente utile per pacchetti di mod grandi e texture in HD.

# Animations

of.options.animation.allOn=TUTTI Sì
of.options.animation.allOff=TUTTI No
of.options.animation.dynamic=Dinamica

of.options.ANIMATED_WATER=Acqua
of.options.ANIMATED_LAVA=Lava
of.options.ANIMATED_FIRE=Fuoco
of.options.ANIMATED_PORTAL=Portali
of.options.ANIMATED_REDSTONE=Redstone
of.options.ANIMATED_EXPLOSION=Esplosioni
of.options.ANIMATED_FLAME=Fuochi d'artificio
of.options.ANIMATED_SMOKE=Fumo
of.options.VOID_PARTICLES=Particelle del vuoto
of.options.WATER_PARTICLES=Particelle d'acqua
of.options.RAIN_SPLASH=Pioggia
of.options.PORTAL_PARTICLES=Particelle dei portali
of.options.POTION_PARTICLES=Particelle delle pozioni
of.options.DRIPPING_WATER_LAVA=Gocce d'acqua/lava
of.options.ANIMATED_TERRAIN=Terreno animato
of.options.ANIMATED_TEXTURES=Texture animate
of.options.FIREWORK_PARTICLES=Particelle fuoco d'artificio

# Other

of.options.LAGOMETER=Contatore lag
of.options.LAGOMETER.tooltip.1=Mostra il contatore di lag premendo F3
of.options.LAGOMETER.tooltip.2=* Arancione - Cestino memoria
of.options.LAGOMETER.tooltip.3=* Ciano - Ticks
of.options.LAGOMETER.tooltip.4=* Azzurro - Eseguibili programmati
of.options.LAGOMETER.tooltip.5=* Porpora - Caricamento dei Chunk
of.options.LAGOMETER.tooltip.6=* Rosso - Aggiornamento dei Chunk
of.options.LAGOMETER.tooltip.7=* Giallo - Controllo visibilità
of.options.LAGOMETER.tooltip.8=* Verde - Terreno renderizzato

of.options.PROFILER=Profilo di debug
of.options.PROFILER.tooltip.1=Debug
of.options.PROFILER.tooltip.2=  Sì - il profilo di debug attivato, più lento
of.options.PROFILER.tooltip.3=  No - il profilo di debug disattivato, più veloce
of.options.PROFILER.tooltip.4=Il profilo di debug raccoglie e visualizza maggiori
of.options.PROFILER.tooltip.5=informazioni rispetto alla funzione F3.

of.options.WEATHER=Condizioni meteo
of.options.WEATHER.tooltip.1=Meteo
of.options.WEATHER.tooltip.2=  Sì - meteo attivato, più lento
of.options.WEATHER.tooltip.3=  No - meteo disattivato, più veloce
of.options.WEATHER.tooltip.4=Il meteo controlla pioggia, neve e tempeste.
of.options.WEATHER.tooltip.5=Questa opzione non funziona in multigiocatore.

of.options.time.dayOnly=Sempre giorno
of.options.time.nightOnly=Sempre notte

of.options.TIME=Tempo
of.options.TIME.tooltip.1=Tempo (orario, giorno o notte)
of.options.TIME.tooltip.2=  Predefinito - cicli normali giorno / notte
of.options.TIME.tooltip.3=  Sempre giorno - sempre è solo giorno
of.options.TIME.tooltip.4=  Sempre notte - sempre e solo notte
of.options.TIME.tooltip.5=Questa opzione funziona solo in creativa e solamente
of.options.TIME.tooltip.6=in mondi locali (NON in multigiocatore).

options.fullscreen.tooltip.1=Schermo intero
options.fullscreen.tooltip.2=  Sì - giocare a schermo intero
options.fullscreen.tooltip.3=  No - giocare a schermo normale
options.fullscreen.tooltip.4=Giocare a schermo intero può essere
options.fullscreen.tooltip.5=migliore o peggiore, dipende dalla scheda grafica.

options.fullscreen.resolution=Modalità schermo intero
options.fullscreen.resolution.tooltip.1=Risoluzione dello schermo intero
options.fullscreen.resolution.tooltip.2=  Predefinita - risoluzione del monitor, più lento
options.fullscreen.resolution.tooltip.3=  Wxh - diversa risoluzione, più efficiente
options.fullscreen.resolution.tooltip.4=La risoluzione è usata in modalità schermo intero (F11).
options.fullscreen.resolution.tooltip.5=Risoluzioni inferiori di solito sono più veloci.

of.options.SHOW_FPS=Mostra FPS
of.options.SHOW_FPS.tooltip.1=Mostra FPS e i dati di rendering
of.options.SHOW_FPS.tooltip.2=  Fps - medi/minimi
of.options.SHOW_FPS.tooltip.3=  C: - rendering dei Chunk
of.options.SHOW_FPS.tooltip.4=  E: - entità rese + blocchi-entità resi
of.options.SHOW_FPS.tooltip.5=  U: - aggiornamento dei Chunk
of.options.SHOW_FPS.tooltip.6=Questi dati ridotti vengono visualizzati
of.options.SHOW_FPS.tooltip.7=solo quando non è attiva la funzione F3.

of.options.save.45s=45s
of.options.save.90s=90s
of.options.save.3min=3min
of.options.save.6min=6min
of.options.save.12min=12min
of.options.save.24min=24min

of.options.AUTOSAVE_TICKS=Autosalvataggio
of.options.AUTOSAVE_TICKS.tooltip.1=Intervallo dell'autosalvataggio
of.options.AUTOSAVE_TICKS.tooltip.2=  45s - default
of.options.AUTOSAVE_TICKS.tooltip.3=Un breve intervallo può influire negativamente sulle prestazioni.
of.options.AUTOSAVE_TICKS.tooltip.4=Il mondo viene salvato anche all'apertura del menù di gioco.

options.anaglyph.tooltip.1=Visione in 3D

of.options.ADVANCED_TOOLTIPS=Descrizione oggetti
of.options.ADVANCED_TOOLTIPS.tooltip.1=Descrizione degli oggetti nell'inventario
of.options.ADVANCED_TOOLTIPS.tooltip.2=  Sì - mostra le descrizioni
of.options.ADVANCED_TOOLTIPS.tooltip.3=  No - non mostrare descrizioni (per default)
of.options.ADVANCED_TOOLTIPS.tooltip.4=Consente di visualizzare informazioni più dettagliate
of.options.ADVANCED_TOOLTIPS.tooltip.5=su oggetti (ID, durata) e le
of.options.ADVANCED_TOOLTIPS.tooltip.6=Shaders (ID, origine, di default).

of.options.SCREENSHOT_SIZE=Grandezza screenshot
of.options.SCREENSHOT_SIZE.tooltip.1=Dimensione screenshot
of.options.SCREENSHOT_SIZE.tooltip.2=  Default - grandezza di default
of.options.SCREENSHOT_SIZE.tooltip.3=  2x-4x - grandezza personalizzata
of.options.SCREENSHOT_SIZE.tooltip.4=Fare screenshot più grandi richiede più memoria.
of.options.SCREENSHOT_SIZE.tooltip.5=Non compatibile con Render Veloce e Antialiasing.
of.options.SCREENSHOT_SIZE.tooltip.6=Richiede supporto framebuffer della GPU.

of.options.SHOW_GL_ERRORS=Errori Open GL
of.options.SHOW_GL_ERRORS.tooltip.1=Mostra Errori Open GL
of.options.SHOW_GL_ERRORS.tooltip.2=Quando abilitata mostra errori in chat.
of.options.SHOW_GL_ERRORS.tooltip.3=Disabilita solo se ci sono conflitti o per
of.options.SHOW_GL_ERRORS.tooltip.4=errori che non possono essere risolti.
of.options.SHOW_GL_ERRORS.tooltip.5=Quando disabilitato gli errori sono sempre salvati
of.options.SHOW_GL_ERRORS.tooltip.6=nel log degli errori e possono causare cali di FPS.

# Chat Settings

of.options.CHAT_BACKGROUND=Sfondo Chat
of.options.CHAT_BACKGROUND.tooltip.1=Sfondo Chat
of.options.CHAT_BACKGROUND.tooltip.2=  Default - larghezza automatica
of.options.CHAT_BACKGROUND.tooltip.3=  Compatta - adattata alla larghezza della riga
of.options.CHAT_BACKGROUND.tooltip.4=  No - nascosta

of.options.CHAT_SHADOW=Ombra Chat
of.options.CHAT_SHADOW.tooltip.1=Ombra Chat
of.options.CHAT_SHADOW.tooltip.2=  Sì - sfondo scuro della chat
of.options.CHAT_SHADOW.tooltip.3=  No - nessuno sfondo scuro
