
language.name=English
language.region=US
language.code=en_US

gui.done=Done
gui.cancel=Cancel
gui.back=Back
gui.toTitle=Back to title screen
gui.toMenu=Back to server list
gui.up=Up
gui.down=Down
gui.yes=Yes
gui.no=No
gui.none=None
gui.all=All

translation.test.none=Hello, world!
translation.test.complex=Prefix, %s%2$s again %s and %1$s lastly %s and also %1$s again!
translation.test.escape=%%s %%%s %%%%s %%%%%s
translation.test.invalid=hi %
translation.test.invalid2=hi %  s
translation.test.args=%s %s
translation.test.world=world

menu.game=Game menu
menu.singleplayer=Singleplayer
menu.multiplayer=Multiplayer
menu.options=Options...
menu.quit=Quit Game
menu.returnToMenu=Save and Quit to Title
menu.disconnect=Disconnect
menu.returnToGame=Back to Game
menu.switchingLevel=Switching worlds
menu.generatingLevel=Generating world
menu.loadingLevel=Loading world
menu.generatingTerrain=Building terrain
menu.convertingLevel=Converting world
menu.simulating=Simulating the world for a bit
menu.respawning=Respawning
menu.shareToLan=Open to LAN

selectWorld.title=Select World
selectWorld.empty=empty
selectWorld.world=World
selectWorld.select=Play Selected World
selectWorld.create=Create New World
selectWorld.recreate=Re-Create
selectWorld.delete=Delete
selectWorld.rename=Rename
selectWorld.deleteQuestion=Are you sure you want to delete this world?
selectWorld.deleteWarning=will be lost forever! (A long time!)
selectWorld.deleteButton=Delete
selectWorld.renameButton=Rename
selectWorld.renameTitle=Rename World
selectWorld.conversion=Must be converted!
selectWorld.newWorld=New World
selectWorld.newWorld.copyOf=Copy of %s
selectWorld.enterName=World Name
selectWorld.resultFolder=Will be saved in:
selectWorld.enterSeed=Seed for the World Generator
selectWorld.seedInfo=Leave blank for a random seed
selectWorld.cheats=Cheats
selectWorld.customizeType=Customize

createWorld.customize.presets=Presets
createWorld.customize.presets.title=Select a Preset
createWorld.customize.presets.select=Use Preset
createWorld.customize.presets.share=Want to share your preset with someone? Use the below box!
createWorld.customize.presets.list=Alternatively, here's some we made earlier!
createWorld.customize.flat.title=Superflat Customization
createWorld.customize.flat.tile=Layer Material
createWorld.customize.flat.height=Height
createWorld.customize.flat.addLayer=Add Layer
createWorld.customize.flat.editLayer=Edit Layer
createWorld.customize.flat.removeLayer=Remove Layer
createWorld.customize.flat.layer.top=Top - %d
createWorld.customize.flat.layer=%d
createWorld.customize.flat.layer.bottom=Bottom - %d

createWorld.customize.custom.page0=Basic Settings
createWorld.customize.custom.page1=Ore Settings
createWorld.customize.custom.page2=Advanced Settings (Expert Users Only!)
createWorld.customize.custom.page3=Extra Advanced Settings (Expert Users Only!)
createWorld.customize.custom.randomize=Randomize
createWorld.customize.custom.prev=Previous Page
createWorld.customize.custom.next=Next Page
createWorld.customize.custom.defaults=Defaults
createWorld.customize.custom.confirm1=This will overwrite your current
createWorld.customize.custom.confirm2=settings and cannot be undone.
createWorld.customize.custom.confirmTitle=Warning!
createWorld.customize.custom.mainNoiseScaleX=Main Noise Scale X
createWorld.customize.custom.mainNoiseScaleY=Main Noise Scale Y
createWorld.customize.custom.mainNoiseScaleZ=Main Noise Scale Z
createWorld.customize.custom.depthNoiseScaleX=Depth Noise Scale X
createWorld.customize.custom.depthNoiseScaleZ=Depth Noise Scale Z
createWorld.customize.custom.depthNoiseScaleExponent=Depth Noise Exponent
createWorld.customize.custom.baseSize=Depth Base Size
createWorld.customize.custom.coordinateScale=Coordinate Scale
createWorld.customize.custom.heightScale=Height Scale
createWorld.customize.custom.stretchY=Height Stretch
createWorld.customize.custom.upperLimitScale=Upper Limit Scale
createWorld.customize.custom.lowerLimitScale=Lower Limit Scale
createWorld.customize.custom.biomeDepthWeight=Biome Depth Weight
createWorld.customize.custom.biomeDepthOffset=Biome Depth Offset
createWorld.customize.custom.biomeScaleWeight=Biome Scale Weight
createWorld.customize.custom.biomeScaleOffset=Biome Scale Offset
createWorld.customize.custom.seaLevel=Sea Level
createWorld.customize.custom.useCaves=Caves
createWorld.customize.custom.useStrongholds=Strongholds
createWorld.customize.custom.useVillages=Villages
createWorld.customize.custom.useMineShafts=Mineshafts
createWorld.customize.custom.useTemples=Temples
createWorld.customize.custom.useMonuments=Ocean Monuments
createWorld.customize.custom.useRavines=Ravines
createWorld.customize.custom.useDungeons=Dungeons
createWorld.customize.custom.dungeonChance=Dungeon Count
createWorld.customize.custom.useWaterLakes=Water Lakes
createWorld.customize.custom.waterLakeChance=Water Lake Rarity
createWorld.customize.custom.useLavaLakes=Lava Lakes
createWorld.customize.custom.lavaLakeChance=Lava Lake Rarity
createWorld.customize.custom.useLavaOceans=Lava Oceans
createWorld.customize.custom.fixedBiome=Biome
createWorld.customize.custom.biomeSize=Biome Size
createWorld.customize.custom.riverSize=River Size

createWorld.customize.custom.size= Spawn Size
createWorld.customize.custom.count= Spawn Tries
createWorld.customize.custom.minHeight= Min. Height
createWorld.customize.custom.maxHeight= Max. Height
createWorld.customize.custom.center= Center Height
createWorld.customize.custom.spread= Spread Height

createWorld.customize.custom.presets.title=Customize World Presets
createWorld.customize.custom.presets=Presets
createWorld.customize.custom.preset.waterWorld=Water World
createWorld.customize.custom.preset.isleLand=Isle Land
createWorld.customize.custom.preset.caveDelight=Caver's Delight
createWorld.customize.custom.preset.mountains=Mountain Madness
createWorld.customize.custom.preset.drought=Drought
createWorld.customize.custom.preset.caveChaos=Caves of Chaos
createWorld.customize.custom.preset.goodLuck=Good Luck

gameMode.survival=Survival Mode
gameMode.creative=Creative Mode
gameMode.adventure=Adventure Mode
gameMode.spectator=Spectator Mode
gameMode.hardcore=Hardcore Mode!
gameMode.changed=Your game mode has been updated

selectWorld.gameMode=Game Mode
selectWorld.gameMode.survival=Survival
selectWorld.gameMode.survival.line1=Search for resources, crafting, gain
selectWorld.gameMode.survival.line2=levels, health and hunger
selectWorld.gameMode.creative=Creative
selectWorld.gameMode.creative.line1=Unlimited resources, free flying and
selectWorld.gameMode.creative.line2=destroy blocks instantly
selectWorld.gameMode.spectator=Spectator
selectWorld.gameMode.spectator.line1=You can look but don't touch
selectWorld.gameMode.spectator.line2=
selectWorld.gameMode.hardcore=Hardcore
selectWorld.gameMode.hardcore.line1=Same as survival mode, locked at hardest
selectWorld.gameMode.hardcore.line2=difficulty, and one life only
selectWorld.gameMode.adventure=Adventure
selectWorld.gameMode.adventure.line1=Same as survival mode, but blocks can't
selectWorld.gameMode.adventure.line2=be added or removed
selectWorld.moreWorldOptions=More World Options...
selectWorld.mapFeatures=Generate Structures:
selectWorld.mapFeatures.info=Villages, dungeons etc
selectWorld.mapType=World Type:
selectWorld.mapType.normal=Normal
selectWorld.allowCommands=Allow Cheats:
selectWorld.allowCommands.info=Commands like /gamemode, /xp
selectWorld.hardcoreMode=Hardcore:
selectWorld.hardcoreMode.info=World is deleted upon death
selectWorld.bonusItems=Bonus Chest:

generator.default=Default
generator.flat=Superflat
generator.largeBiomes=Large Biomes
generator.amplified=AMPLIFIED
generator.customized=Customized
generator.debug_all_block_states=Debug Mode

generator.amplified.info=Notice: Just for fun, requires beefy computer

selectServer.title=Select Server
selectServer.empty=empty
selectServer.select=Join Server
selectServer.direct=Direct Connect
selectServer.edit=Edit
selectServer.delete=Delete
selectServer.add=Add server
selectServer.defaultName=Minecraft Server
selectServer.deleteQuestion=Are you sure you want to remove this server?
selectServer.deleteWarning=will be lost forever! (A long time!)
selectServer.deleteButton=Delete
selectServer.refresh=Refresh
selectServer.hiddenAddress=(Hidden)
addServer.title=Edit Server Info
addServer.enterName=Server Name
addServer.enterIp=Server Address
addServer.add=Done
addServer.hideAddress=Hide Address
addServer.resourcePack=Server Resource Packs
addServer.resourcePack.enabled=Enabled
addServer.resourcePack.disabled=Disabled
addServer.resourcePack.prompt=Prompt
lanServer.title=LAN World
lanServer.scanning=Scanning for games on your local network
lanServer.start=Start LAN World
lanServer.otherPlayers=Settings for Other Players
mcoServer.title=Minecraft Online World

multiplayer.title=Play Multiplayer
multiplayer.connect=Connect
multiplayer.info1=Minecraft Multiplayer is currently not finished, but there
multiplayer.info2=is some buggy early testing going on.
multiplayer.ipinfo=Enter the IP of a server to connect to it:
multiplayer.texturePrompt.line1=This server recommends the use of a custom resource pack.
multiplayer.texturePrompt.line2=Would you like to download and install it automagically?
multiplayer.downloadingTerrain=Downloading terrain
multiplayer.downloadingStats=Downloading statistics & achievements...
multiplayer.stopSleeping=Leave Bed
multiplayer.player.joined=%s joined the game
multiplayer.player.joined.renamed=%s (formerly known as %s) joined the game
multiplayer.player.left=%s left the game

chat.cannotSend=Cannot send chat message
chat.type.text=<%s> %s
chat.type.emote=* %s %s
chat.type.announcement=[%s] %s
chat.type.admin=[%s: %s]
chat.type.achievement=%s has just earned the achievement %s
chat.type.achievement.taken=%s has lost the achievement %s
chat.link.confirm=Are you sure you want to open the following website?
chat.link.warning=Never open links from people that you don't trust!
chat.copy=Copy to Clipboard
chat.link.confirmTrusted=Do you want to open this link or copy it to your clipboard?
chat.link.open=Open in browser

connect.connecting=Connecting to the server...
connect.authorizing=Logging in...
connect.failed=Failed to connect to the server

disconnect.genericReason=%s
disconnect.disconnected=Disconnected by Server
disconnect.lost=Connection Lost
disconnect.kicked=Was kicked from the game
disconnect.timeout=Timed out
disconnect.closed=Connection closed
disconnect.loginFailed=Failed to login
disconnect.loginFailedInfo=Failed to login: %s
disconnect.loginFailedInfo.serversUnavailable=The authentication servers are currently down for maintenance.
disconnect.loginFailedInfo.invalidSession=Invalid session (Try restarting your game)
disconnect.quitting=Quitting
disconnect.overflow=Buffer overflow
disconnect.spam=Kicked for spamming

soundCategory.master=Master Volume
soundCategory.music=Music
soundCategory.record=Jukebox/Noteblocks
soundCategory.weather=Weather
soundCategory.hostile=Hostile Creatures
soundCategory.neutral=Friendly Creatures
soundCategory.player=Players
soundCategory.block=Blocks
soundCategory.ambient=Ambient/Environment

record.nowPlaying=Now playing: %s

options.off=OFF
options.on=ON
options.visible=Shown
options.hidden=Hidden
options.title=Options
options.controls=Controls...
options.video=Video Settings...
options.language=Language...
options.sounds=Music & Sounds...
options.sounds.title=Music & Sound Options
options.languageWarning=Language translations may not be 100%% accurate
options.videoTitle=Video Settings
options.customizeTitle=Customize World Settings
options.music=Music
options.sound=Sound
options.invertMouse=Invert Mouse
options.fov=FOV
options.fov.min=Normal
options.fov.max=Quake Pro
options.saturation=Saturation
options.gamma=Brightness
options.gamma.min=Moody
options.gamma.max=Bright
options.sensitivity=Sensitivity
options.sensitivity.min=*yawn*
options.sensitivity.max=HYPERSPEED!!!
options.renderDistance=Render Distance
options.renderDistance.tiny=Tiny
options.renderDistance.short=Short
options.renderDistance.normal=Normal
options.renderDistance.far=Far
options.viewBobbing=View Bobbing
options.ao=Smooth Lighting
options.ao.off=OFF
options.ao.min=Minimum
options.ao.max=Maximum
options.anaglyph=3D Anaglyph
options.framerateLimit=Max Framerate
options.framerateLimit.max=Unlimited
options.difficulty=Difficulty
options.difficulty.peaceful=Peaceful
options.difficulty.easy=Easy
options.difficulty.normal=Normal
options.difficulty.hard=Hard
options.difficulty.hardcore=Hardcore
options.graphics=Graphics
options.graphics.fancy=Fancy
options.graphics.fast=Fast
options.guiScale=GUI Scale
options.guiScale.auto=Auto
options.guiScale.small=Small
options.guiScale.normal=Normal
options.guiScale.large=Large
options.advancedOpengl=Advanced OpenGL
options.fboEnable=Enable FBOs
options.postProcessEnable=Enable Post-Processing
options.renderClouds=Clouds
options.qualityButton=Video Quality Settings...
options.qualityVideoTitle=Video Quality Settings
options.performanceButton=Video Performance Settings...
options.performanceVideoTitle=Video Performance Settings
options.advancedButton=Advanced Video Settings...
options.advancedVideoTitle=Advanced Video Settings
options.postButton=Post-Processing Settings...
options.postVideoTitle=Post-Processing Settings
options.farWarning1=A 64 bit Java installation is recommended
options.farWarning2=for 'Far' render distance (you have 32 bit)
options.particles=Particles
options.particles.all=All
options.particles.decreased=Decreased
options.particles.minimal=Minimal
options.multiplayer.title=Multiplayer Settings...
options.chat.title=Chat Settings...
options.chat.visibility=Chat
options.chat.visibility.full=Shown
options.chat.visibility.system=Commands Only
options.chat.visibility.hidden=Hidden
options.chat.color=Colors
options.chat.opacity=Opacity
options.chat.links=Web Links
options.chat.links.prompt=Prompt on Links
options.chat.scale=Scale
options.chat.width=Width
options.chat.height.focused=Focused Height
options.chat.height.unfocused=Unfocused Height
options.skinCustomisation=Skin Customization...
options.skinCustomisation.title=Skin Customization
options.modelPart.cape=Cape
options.modelPart.hat=Hat
options.modelPart.jacket=Jacket
options.modelPart.left_sleeve=Left Sleeve
options.modelPart.right_sleeve=Right Sleeve
options.modelPart.left_pants_leg=Left Pants Leg
options.modelPart.right_pants_leg=Right Pants Leg
options.resourcepack=Resource Packs...
options.fullscreen=Fullscreen
options.vsync=Use VSync
options.vbo=Use VBOs
options.touchscreen=Touchscreen Mode
options.blockAlternatives=Alternate Blocks
options.reducedDebugInfo=Reduced Debug Info
options.entityShadows=Entity Shadows

options.mipmapLevels=Mipmap Levels
options.forceUnicodeFont=Force Unicode Font

difficulty.lock.title=Lock World Difficulty
difficulty.lock.question=Are you sure you want to lock the difficulty of this world? This will set this world to always be %1$s, and you will never be able to change that again.

title.oldgl1=Old graphics card detected; this may prevent you from
title.oldgl2=playing in the future as OpenGL 2.0 will be required.

controls.title=Controls
controls.reset=Reset
controls.resetAll=Reset Keys

key.sprint=Sprint
key.forward=Walk Forwards
key.left=Strafe Left
key.back=Walk Backwards
key.right=Strafe Right
key.jump=Jump
key.inventory=Inventory
key.drop=Drop Item
key.chat=Open Chat
key.sneak=Sneak
key.playerlist=List Players
key.attack=Attack/Destroy
key.use=Use Item/Place Block
key.pickItem=Pick Block
key.mouseButton=Button %1$s
key.command=Open Command
key.screenshot=Take Screenshot
key.togglePerspective=Toggle Perspective
key.smoothCamera=Toggle Cinematic Camera
key.fullscreen=Toggle Fullscreen
key.spectatorOutlines=Highlight Players (Spectators)
key.hotbar.1=Hotbar Slot 1
key.hotbar.2=Hotbar Slot 2
key.hotbar.3=Hotbar Slot 3
key.hotbar.4=Hotbar Slot 4
key.hotbar.5=Hotbar Slot 5
key.hotbar.6=Hotbar Slot 6
key.hotbar.7=Hotbar Slot 7
key.hotbar.8=Hotbar Slot 8
key.hotbar.9=Hotbar Slot 9

key.categories.movement=Movement
key.categories.misc=Miscellaneous
key.categories.multiplayer=Multiplayer
key.categories.gameplay=Gameplay
key.categories.ui=Game Interface
key.categories.inventory=Inventory

resourcePack.openFolder=Open resource pack folder
resourcePack.title=Select Resource Packs
resourcePack.available.title=Available Resource Packs
resourcePack.selected.title=Selected Resource Packs
resourcePack.folderInfo=(Place resource pack files here)
resourcePack.incompatible=Incompatible
resourcePack.incompatible.old=(Made for an older version of Minecraft)
resourcePack.incompatible.new=(Made for a newer version of Minecraft)
resourcePack.incompatible.confirm.title=Are you sure you want to load this resource pack?
resourcePack.incompatible.confirm.old=This resource pack was made for an older version of Minecraft and may no longer work correctly.
resourcePack.incompatible.confirm.new=This resource pack was made for a newer version of Minecraft and may no longer work correctly.

sign.edit=Edit sign message

book.pageIndicator=Page %1$s of %2$s
book.byAuthor=by %1$s
book.signButton=Sign
book.editTitle=Enter Book Title:
book.finalizeButton=Sign and Close
book.finalizeWarning=Note! When you sign the book, it will no longer be editable.
book.generation.0=Original
book.generation.1=Copy of original
book.generation.2=Copy of a copy
book.generation.3=Tattered

merchant.deprecated=Trade something else to unlock!

tile.barrier.name=Barrier
tile.stone.stone.name=Stone
tile.stone.granite.name=Granite
tile.stone.graniteSmooth.name=Polished Granite
tile.stone.diorite.name=Diorite
tile.stone.dioriteSmooth.name=Polished Diorite
tile.stone.andesite.name=Andesite
tile.stone.andesiteSmooth.name=Polished Andesite
tile.hayBlock.name=Hay Bale
tile.grass.name=Grass Block
tile.dirt.name=Dirt
tile.dirt.default.name=Dirt
tile.dirt.coarse.name=Coarse Dirt
tile.dirt.podzol.name=Podzol
tile.stonebrick.name=Cobblestone
tile.wood.name=Wooden Planks
tile.wood.oak.name=Oak Wood Planks
tile.wood.spruce.name=Spruce Wood Planks
tile.wood.birch.name=Birch Wood Planks
tile.wood.jungle.name=Jungle Wood Planks
tile.wood.acacia.name=Acacia Wood Planks
tile.wood.big_oak.name=Dark Oak Wood Planks
tile.sapling.oak.name=Oak Sapling
tile.sapling.spruce.name=Spruce Sapling
tile.sapling.birch.name=Birch Sapling
tile.sapling.jungle.name=Jungle Sapling
tile.sapling.acacia.name=Acacia Sapling
tile.sapling.big_oak.name=Dark Oak Sapling
tile.deadbush.name=Dead Bush
tile.bedrock.name=Bedrock
tile.water.name=Water
tile.lava.name=Lava
tile.sand.name=Sand
tile.sand.default.name=Sand
tile.sand.red.name=Red Sand
tile.sandStone.name=Sandstone
tile.sandStone.default.name=Sandstone
tile.sandStone.chiseled.name=Chiseled Sandstone
tile.sandStone.smooth.name=Smooth Sandstone
tile.redSandStone.name=Red Sandstone
tile.redSandStone.default.name=Red Sandstone
tile.redSandStone.chiseled.name=Chiseled Red Sandstone
tile.redSandStone.smooth.name=Smooth Red Sandstone
tile.gravel.name=Gravel
tile.oreGold.name=Gold Ore
tile.oreIron.name=Iron Ore
tile.oreCoal.name=Coal Ore
tile.log.name=Wood
tile.log.oak.name=Oak Wood
tile.log.spruce.name=Spruce Wood
tile.log.birch.name=Birch Wood
tile.log.jungle.name=Jungle Wood
tile.log.acacia.name=Acacia Wood
tile.log.big_oak.name=Dark Oak Wood
tile.leaves.name=Leaves
tile.leaves.oak.name=Oak Leaves
tile.leaves.spruce.name=Spruce Leaves
tile.leaves.birch.name=Birch Leaves
tile.leaves.jungle.name=Jungle Leaves
tile.leaves.acacia.name=Acacia Leaves
tile.leaves.big_oak.name=Dark Oak Leaves
tile.tallgrass.name=Grass
tile.tallgrass.shrub.name=Shrub
tile.tallgrass.grass.name=Grass
tile.tallgrass.fern.name=Fern
tile.sponge.dry.name=Sponge
tile.sponge.wet.name=Wet Sponge
tile.glass.name=Glass
tile.stainedGlass.name=Stained Glass
tile.stainedGlass.black.name=Black Stained Glass
tile.stainedGlass.red.name=Red Stained Glass
tile.stainedGlass.green.name=Green Stained Glass
tile.stainedGlass.brown.name=Brown Stained Glass
tile.stainedGlass.blue.name=Blue Stained Glass
tile.stainedGlass.purple.name=Purple Stained Glass
tile.stainedGlass.cyan.name=Cyan Stained Glass
tile.stainedGlass.silver.name=Light Gray Stained Glass
tile.stainedGlass.gray.name=Gray Stained Glass
tile.stainedGlass.pink.name=Pink Stained Glass
tile.stainedGlass.lime.name=Lime Stained Glass
tile.stainedGlass.yellow.name=Yellow Stained Glass
tile.stainedGlass.lightBlue.name=Light Blue Stained Glass
tile.stainedGlass.magenta.name=Magenta Stained Glass
tile.stainedGlass.orange.name=Orange Stained Glass
tile.stainedGlass.white.name=White Stained Glass
tile.thinStainedGlass.name=Stained Glass Pane
tile.thinStainedGlass.black.name=Black Stained Glass Pane
tile.thinStainedGlass.red.name=Red Stained Glass Pane
tile.thinStainedGlass.green.name=Green Stained Glass Pane
tile.thinStainedGlass.brown.name=Brown Stained Glass Pane
tile.thinStainedGlass.blue.name=Blue Stained Glass Pane
tile.thinStainedGlass.purple.name=Purple Stained Glass Pane
tile.thinStainedGlass.cyan.name=Cyan Stained Glass Pane
tile.thinStainedGlass.silver.name=Light Gray Stained Glass Pane
tile.thinStainedGlass.gray.name=Gray Stained Glass Pane
tile.thinStainedGlass.pink.name=Pink Stained Glass Pane
tile.thinStainedGlass.lime.name=Lime Stained Glass Pane
tile.thinStainedGlass.yellow.name=Yellow Stained Glass Pane
tile.thinStainedGlass.lightBlue.name=Light Blue Stained Glass Pane
tile.thinStainedGlass.magenta.name=Magenta Stained Glass Pane
tile.thinStainedGlass.orange.name=Orange Stained Glass Pane
tile.thinStainedGlass.white.name=White Stained Glass Pane
tile.thinGlass.name=Glass Pane
tile.cloth.name=Wool
tile.flower1.name=Flower
tile.flower1.dandelion.name=Dandelion
tile.flower2.name=Flower
tile.flower2.poppy.name=Poppy
tile.flower2.blueOrchid.name=Blue Orchid
tile.flower2.allium.name=Allium
tile.flower2.houstonia.name=Azure Bluet
tile.flower2.tulipRed.name=Red Tulip
tile.flower2.tulipOrange.name=Orange Tulip
tile.flower2.tulipWhite.name=White Tulip
tile.flower2.tulipPink.name=Pink Tulip
tile.flower2.oxeyeDaisy.name=Oxeye Daisy
tile.doublePlant.name=Plant
tile.doublePlant.sunflower.name=Sunflower
tile.doublePlant.syringa.name=Lilac
tile.doublePlant.grass.name=Double Tallgrass
tile.doublePlant.fern.name=Large Fern
tile.doublePlant.rose.name=Rose Bush
tile.doublePlant.paeonia.name=Peony
tile.mushroom.name=Mushroom
tile.blockGold.name=Block of Gold
tile.blockIron.name=Block of Iron
tile.stoneSlab.name=Stone Slab
tile.stoneSlab.stone.name=Stone Slab
tile.stoneSlab.sand.name=Sandstone Slab
tile.stoneSlab.wood.name=Wooden Slab
tile.stoneSlab.cobble.name=Cobblestone Slab
tile.stoneSlab.brick.name=Bricks Slab
tile.stoneSlab.smoothStoneBrick.name=Stone Bricks Slab
tile.stoneSlab.netherBrick.name=Nether Brick Slab
tile.stoneSlab.quartz.name=Quartz Slab
tile.stoneSlab2.red_sandstone.name=Red Sandstone Slab
tile.woodSlab.name=Wood Slab
tile.woodSlab.oak.name=Oak Wood Slab
tile.woodSlab.spruce.name=Spruce Wood Slab
tile.woodSlab.birch.name=Birch Wood Slab
tile.woodSlab.jungle.name=Jungle Wood Slab
tile.woodSlab.acacia.name=Acacia Wood Slab
tile.woodSlab.big_oak.name=Dark Oak Wood Slab
tile.brick.name=Bricks
tile.tnt.name=TNT
tile.bookshelf.name=Bookshelf
tile.stoneMoss.name=Moss Stone
tile.obsidian.name=Obsidian
tile.torch.name=Torch
tile.fire.name=Fire
tile.mobSpawner.name=Monster Spawner
tile.stairsWood.name=Oak Wood Stairs
tile.stairsWoodSpruce.name=Spruce Wood Stairs
tile.stairsWoodBirch.name=Birch Wood Stairs
tile.stairsWoodJungle.name=Jungle Wood Stairs
tile.stairsWoodAcacia.name=Acacia Wood Stairs
tile.stairsWoodDarkOak.name=Dark Oak Wood Stairs
tile.chest.name=Chest
tile.chestTrap.name=Trapped Chest
tile.redstoneDust.name=Redstone Dust
tile.oreDiamond.name=Diamond Ore
tile.blockCoal.name=Block of Coal
tile.blockDiamond.name=Block of Diamond
tile.workbench.name=Crafting Table
tile.crops.name=Crops
tile.farmland.name=Farmland
tile.furnace.name=Furnace
tile.sign.name=Sign
tile.doorWood.name=Wooden Door
tile.ladder.name=Ladder
tile.rail.name=Rail
tile.goldenRail.name=Powered Rail
tile.activatorRail.name=Activator Rail
tile.detectorRail.name=Detector Rail
tile.stairsStone.name=Cobblestone Stairs
tile.stairsSandStone.name=Sandstone Stairs
tile.stairsRedSandStone.name=Red Sandstone Stairs
tile.lever.name=Lever
tile.pressurePlateStone.name=Stone Pressure Plate
tile.pressurePlateWood.name=Wooden Pressure Plate
tile.weightedPlate_light.name=Weighted Pressure Plate (Light)
tile.weightedPlate_heavy.name=Weighted Pressure Plate (Heavy)
tile.doorIron.name=Iron Door
tile.oreRedstone.name=Redstone Ore
tile.notGate.name=Redstone Torch
tile.button.name=Button
tile.snow.name=Snow
tile.woolCarpet.name=Carpet
tile.woolCarpet.black.name=Black Carpet
tile.woolCarpet.red.name=Red Carpet
tile.woolCarpet.green.name=Green Carpet
tile.woolCarpet.brown.name=Brown Carpet
tile.woolCarpet.blue.name=Blue Carpet
tile.woolCarpet.purple.name=Purple Carpet
tile.woolCarpet.cyan.name=Cyan Carpet
tile.woolCarpet.silver.name=Light Gray Carpet
tile.woolCarpet.gray.name=Gray Carpet
tile.woolCarpet.pink.name=Pink Carpet
tile.woolCarpet.lime.name=Lime Carpet
tile.woolCarpet.yellow.name=Yellow Carpet
tile.woolCarpet.lightBlue.name=Light Blue Carpet
tile.woolCarpet.magenta.name=Magenta Carpet
tile.woolCarpet.orange.name=Orange Carpet
tile.woolCarpet.white.name=Carpet
tile.ice.name=Ice
tile.icePacked.name=Packed Ice
tile.cactus.name=Cactus
tile.clay.name=Clay
tile.clayHardenedStained.name=Stained Clay
tile.clayHardenedStained.black.name=Black Stained Clay
tile.clayHardenedStained.red.name=Red Stained Clay
tile.clayHardenedStained.green.name=Green Stained Clay
tile.clayHardenedStained.brown.name=Brown Stained Clay
tile.clayHardenedStained.blue.name=Blue Stained Clay
tile.clayHardenedStained.purple.name=Purple Stained Clay
tile.clayHardenedStained.cyan.name=Cyan Stained Clay
tile.clayHardenedStained.silver.name=Light Gray Stained Clay
tile.clayHardenedStained.gray.name=Gray Stained Clay
tile.clayHardenedStained.pink.name=Pink Stained Clay
tile.clayHardenedStained.lime.name=Lime Stained Clay
tile.clayHardenedStained.yellow.name=Yellow Stained Clay
tile.clayHardenedStained.lightBlue.name=Light Blue Stained Clay
tile.clayHardenedStained.magenta.name=Magenta Stained Clay
tile.clayHardenedStained.orange.name=Orange Stained Clay
tile.clayHardenedStained.white.name=White Stained Clay
tile.clayHardened.name=Hardened Clay
tile.reeds.name=Sugar cane
tile.jukebox.name=Jukebox
tile.fence.name=Oak Fence
tile.spruceFence.name=Spruce Fence
tile.birchFence.name=Birch Fence
tile.jungleFence.name=Jungle Fence
tile.darkOakFence.name=Dark Oak Fence
tile.acaciaFence.name=Acacia Fence
tile.fenceGate.name=Oak Fence Gate
tile.spruceFenceGate.name=Spruce Fence Gate
tile.birchFenceGate.name=Birch Fence Gate
tile.jungleFenceGate.name=Jungle Fence Gate
tile.darkOakFenceGate.name=Dark Oak Fence Gate
tile.acaciaFenceGate.name=Acacia Fence Gate
tile.pumpkinStem.name=Pumpkin Stem
tile.pumpkin.name=Pumpkin
tile.litpumpkin.name=Jack o'Lantern
tile.hellrock.name=Netherrack
tile.hellsand.name=Soul Sand
tile.lightgem.name=Glowstone
tile.portal.name=Portal
tile.cloth.black.name=Black Wool
tile.cloth.red.name=Red Wool
tile.cloth.green.name=Green Wool
tile.cloth.brown.name=Brown Wool
tile.cloth.blue.name=Blue Wool
tile.cloth.purple.name=Purple Wool
tile.cloth.cyan.name=Cyan Wool
tile.cloth.silver.name=Light Gray Wool
tile.cloth.gray.name=Gray Wool
tile.cloth.pink.name=Pink Wool
tile.cloth.lime.name=Lime Wool
tile.cloth.yellow.name=Yellow Wool
tile.cloth.lightBlue.name=Light Blue Wool
tile.cloth.magenta.name=Magenta Wool
tile.cloth.orange.name=Orange Wool
tile.cloth.white.name=Wool
tile.oreLapis.name=Lapis Lazuli Ore
tile.blockLapis.name=Lapis Lazuli Block
tile.dispenser.name=Dispenser
tile.dropper.name=Dropper
tile.musicBlock.name=Note Block
tile.cake.name=Cake
tile.bed.name=Bed
tile.bed.occupied=This bed is occupied
tile.bed.noSleep=You can only sleep at night
tile.bed.notSafe=You may not rest now, there are monsters nearby
tile.bed.notValid=Your home bed was missing or obstructed
tile.lockedchest.name=Locked chest
tile.trapdoor.name=Wooden Trapdoor
tile.ironTrapdoor.name=Iron Trapdoor
tile.web.name=Cobweb
tile.stonebricksmooth.name=Stone Bricks
tile.stonebricksmooth.default.name=Stone Bricks
tile.stonebricksmooth.mossy.name=Mossy Stone Bricks
tile.stonebricksmooth.cracked.name=Cracked Stone Bricks
tile.stonebricksmooth.chiseled.name=Chiseled Stone Bricks
tile.monsterStoneEgg.name=Stone Monster Egg
tile.monsterStoneEgg.stone.name=Stone Monster Egg
tile.monsterStoneEgg.cobble.name=Cobblestone Monster Egg
tile.monsterStoneEgg.brick.name=Stone Brick Monster Egg
tile.monsterStoneEgg.mossybrick.name=Mossy Stone Brick Monster Egg
tile.monsterStoneEgg.crackedbrick.name=Cracked Stone Brick Monster Egg
tile.monsterStoneEgg.chiseledbrick.name=Chiseled Stone Brick Monster Egg
tile.pistonBase.name=Piston
tile.pistonStickyBase.name=Sticky Piston
tile.fenceIron.name=Iron Bars
tile.melon.name=Melon
tile.stairsBrick.name=Brick Stairs
tile.stairsStoneBrickSmooth.name=Stone Brick Stairs
tile.vine.name=Vines
tile.netherBrick.name=Nether Brick
tile.netherFence.name=Nether Brick Fence
tile.stairsNetherBrick.name=Nether Brick Stairs
tile.netherStalk.name=Nether Wart
tile.cauldron.name=Cauldron
tile.enchantmentTable.name=Enchantment Table
tile.anvil.name=Anvil
tile.anvil.intact.name=Anvil
tile.anvil.slightlyDamaged.name=Slightly Damaged Anvil
tile.anvil.veryDamaged.name=Very Damaged Anvil
tile.whiteStone.name=End Stone
tile.endPortalFrame.name=End Portal
tile.mycel.name=Mycelium
tile.waterlily.name=Lily Pad
tile.dragonEgg.name=Dragon Egg
tile.redstoneLight.name=Redstone Lamp
tile.cocoa.name=Cocoa
tile.enderChest.name=Ender Chest
tile.oreRuby.name=Ruby Ore
tile.oreEmerald.name=Emerald Ore
tile.blockEmerald.name=Block of Emerald
tile.blockRedstone.name=Block of Redstone
tile.tripWire.name=Tripwire
tile.tripWireSource.name=Tripwire Hook
tile.commandBlock.name=Command Block
tile.beacon.name=Beacon
tile.beacon.primary=Primary Power
tile.beacon.secondary=Secondary Power
tile.cobbleWall.normal.name=Cobblestone Wall
tile.cobbleWall.mossy.name=Mossy Cobblestone Wall
tile.carrots.name=Carrots
tile.potatoes.name=Potatoes
tile.daylightDetector.name=Daylight Sensor
tile.netherquartz.name=Nether Quartz Ore
tile.hopper.name=Hopper
tile.quartzBlock.name=Block of Quartz
tile.quartzBlock.default.name=Block of Quartz
tile.quartzBlock.chiseled.name=Chiseled Quartz Block
tile.quartzBlock.lines.name=Pillar Quartz Block
tile.stairsQuartz.name=Quartz Stairs
tile.slime.name=Slime Block
tile.prismarine.rough.name=Prismarine
tile.prismarine.bricks.name=Prismarine Bricks
tile.prismarine.dark.name=Dark Prismarine
tile.seaLantern.name=Sea Lantern

item.nameTag.name=Name Tag
item.leash.name=Lead
item.shovelIron.name=Iron Shovel
item.pickaxeIron.name=Iron Pickaxe
item.hatchetIron.name=Iron Axe
item.flintAndSteel.name=Flint and Steel
item.apple.name=Apple
item.cookie.name=Cookie
item.bow.name=Bow
item.arrow.name=Arrow
item.coal.name=Coal
item.charcoal.name=Charcoal
item.diamond.name=Diamond
item.emerald.name=Emerald
item.ingotIron.name=Iron Ingot
item.ingotGold.name=Gold Ingot
item.swordIron.name=Iron Sword
item.swordWood.name=Wooden Sword
item.shovelWood.name=Wooden Shovel
item.pickaxeWood.name=Wooden Pickaxe
item.hatchetWood.name=Wooden Axe
item.swordStone.name=Stone Sword
item.shovelStone.name=Stone Shovel
item.pickaxeStone.name=Stone Pickaxe
item.hatchetStone.name=Stone Axe
item.swordDiamond.name=Diamond Sword
item.shovelDiamond.name=Diamond Shovel
item.pickaxeDiamond.name=Diamond Pickaxe
item.hatchetDiamond.name=Diamond Axe
item.stick.name=Stick
item.bowl.name=Bowl
item.mushroomStew.name=Mushroom Stew
item.swordGold.name=Golden Sword
item.shovelGold.name=Golden Shovel
item.pickaxeGold.name=Golden Pickaxe
item.hatchetGold.name=Golden Axe
item.string.name=String
item.feather.name=Feather
item.sulphur.name=Gunpowder
item.hoeWood.name=Wooden Hoe
item.hoeStone.name=Stone Hoe
item.hoeIron.name=Iron Hoe
item.hoeDiamond.name=Diamond Hoe
item.hoeGold.name=Golden Hoe
item.seeds.name=Seeds
item.seeds_pumpkin.name=Pumpkin Seeds
item.seeds_melon.name=Melon Seeds
item.melon.name=Melon
item.wheat.name=Wheat
item.bread.name=Bread
item.helmetCloth.name=Leather Cap
item.chestplateCloth.name=Leather Tunic
item.leggingsCloth.name=Leather Pants
item.bootsCloth.name=Leather Boots
item.helmetChain.name=Chain Helmet
item.chestplateChain.name=Chain Chestplate
item.leggingsChain.name=Chain Leggings
item.bootsChain.name=Chain Boots
item.helmetIron.name=Iron Helmet
item.chestplateIron.name=Iron Chestplate
item.leggingsIron.name=Iron Leggings
item.bootsIron.name=Iron Boots
item.helmetDiamond.name=Diamond Helmet
item.chestplateDiamond.name=Diamond Chestplate
item.leggingsDiamond.name=Diamond Leggings
item.bootsDiamond.name=Diamond Boots
item.helmetGold.name=Golden Helmet
item.chestplateGold.name=Golden Chestplate
item.leggingsGold.name=Golden Leggings
item.bootsGold.name=Golden Boots
item.flint.name=Flint
item.porkchopRaw.name=Raw Porkchop
item.porkchopCooked.name=Cooked Porkchop
item.chickenRaw.name=Raw Chicken
item.chickenCooked.name=Cooked Chicken
item.muttonRaw.name=Raw Mutton
item.muttonCooked.name=Cooked Mutton
item.rabbitRaw.name=Raw Rabbit
item.rabbitCooked.name=Cooked Rabbit
item.rabbitStew.name=Rabbit Stew
item.rabbitFoot.name=Rabbit's Foot
item.rabbitHide.name=Rabbit Hide
item.beefRaw.name=Raw Beef
item.beefCooked.name=Steak
item.painting.name=Painting
item.frame.name=Item Frame
item.appleGold.name=Golden Apple
item.sign.name=Sign
item.doorOak.name=Oak Door
item.doorSpruce.name=Spruce Door
item.doorBirch.name=Birch Door
item.doorJungle.name=Jungle Door
item.doorAcacia.name=Acacia Door
item.doorDarkOak.name=Dark Oak Door
item.bucket.name=Bucket
item.bucketWater.name=Water Bucket
item.bucketLava.name=Lava Bucket
item.minecart.name=Minecart
item.saddle.name=Saddle
item.doorIron.name=Iron Door
item.redstone.name=Redstone
item.snowball.name=Snowball
item.boat.name=Boat
item.leather.name=Leather
item.milk.name=Milk
item.brick.name=Brick
item.clay.name=Clay
item.reeds.name=Sugar Canes
item.paper.name=Paper
item.book.name=Book
item.slimeball.name=Slimeball
item.minecartChest.name=Minecart with Chest
item.minecartFurnace.name=Minecart with Furnace
item.minecartTnt.name=Minecart with TNT
item.minecartHopper.name=Minecart with Hopper
item.minecartCommandBlock.name=Minecart with Command Block
item.egg.name=Egg
item.compass.name=Compass
item.fishingRod.name=Fishing Rod
item.clock.name=Clock
item.yellowDust.name=Glowstone Dust
item.fish.cod.raw.name=Raw Fish
item.fish.salmon.raw.name=Raw Salmon
item.fish.pufferfish.raw.name=Pufferfish
item.fish.clownfish.raw.name=Clownfish
item.fish.cod.cooked.name=Cooked Fish
item.fish.salmon.cooked.name=Cooked Salmon
item.record.name=Music Disc
item.record.13.desc=C418 - 13
item.record.cat.desc=C418 - cat
item.record.blocks.desc=C418 - blocks
item.record.chirp.desc=C418 - chirp
item.record.far.desc=C418 - far
item.record.mall.desc=C418 - mall
item.record.mellohi.desc=C418 - mellohi
item.record.stal.desc=C418 - stal
item.record.strad.desc=C418 - strad
item.record.ward.desc=C418 - ward
item.record.11.desc=C418 - 11
item.record.wait.desc=C418 - wait
item.bone.name=Bone
item.dyePowder.black.name=Ink Sac
item.dyePowder.red.name=Rose Red
item.dyePowder.green.name=Cactus Green
item.dyePowder.brown.name=Cocoa Beans
item.dyePowder.blue.name=Lapis Lazuli
item.dyePowder.purple.name=Purple Dye
item.dyePowder.cyan.name=Cyan Dye
item.dyePowder.silver.name=Light Gray Dye
item.dyePowder.gray.name=Gray Dye
item.dyePowder.pink.name=Pink Dye
item.dyePowder.lime.name=Lime Dye
item.dyePowder.yellow.name=Dandelion Yellow
item.dyePowder.lightBlue.name=Light Blue Dye
item.dyePowder.magenta.name=Magenta Dye
item.dyePowder.orange.name=Orange Dye
item.dyePowder.white.name=Bone Meal
item.sugar.name=Sugar
item.cake.name=Cake
item.bed.name=Bed
item.diode.name=Redstone Repeater
item.comparator.name=Redstone Comparator
item.map.name=Map
item.leaves.name=Leaves
item.shears.name=Shears
item.rottenFlesh.name=Rotten Flesh
item.enderPearl.name=Ender Pearl
item.blazeRod.name=Blaze Rod
item.ghastTear.name=Ghast Tear
item.netherStalkSeeds.name=Nether Wart
item.potion.name=Potion
item.emptyPotion.name=Water Bottle
item.goldNugget.name=Gold Nugget
item.glassBottle.name=Glass Bottle
item.spiderEye.name=Spider Eye
item.fermentedSpiderEye.name=Fermented Spider Eye
item.blazePowder.name=Blaze Powder
item.magmaCream.name=Magma Cream
item.cauldron.name=Cauldron
item.brewingStand.name=Brewing Stand
item.eyeOfEnder.name=Eye of Ender
item.speckledMelon.name=Glistering Melon
item.monsterPlacer.name=Spawn
item.expBottle.name=Bottle o' Enchanting
item.fireball.name=Fire Charge
item.writingBook.name=Book and Quill
item.writtenBook.name=Written Book
item.ruby.name=Ruby
item.flowerPot.name=Flower Pot
item.emptyMap.name=Empty Map
item.carrots.name=Carrot
item.carrotGolden.name=Golden Carrot
item.potato.name=Potato
item.potatoBaked.name=Baked Potato
item.potatoPoisonous.name=Poisonous Potato
item.skull.skeleton.name=Skeleton Skull
item.skull.wither.name=Wither Skeleton Skull
item.skull.zombie.name=Zombie Head
item.skull.char.name=Head
item.skull.player.name=%s's Head
item.skull.creeper.name=Creeper Head
item.carrotOnAStick.name=Carrot on a Stick
item.netherStar.name=Nether Star
item.pumpkinPie.name=Pumpkin Pie
item.enchantedBook.name=Enchanted Book
item.fireworks.name=Firework Rocket
item.fireworks.flight=Flight Duration:
item.fireworksCharge.name=Firework Star
item.fireworksCharge.black=Black
item.fireworksCharge.red=Red
item.fireworksCharge.green=Green
item.fireworksCharge.brown=Brown
item.fireworksCharge.blue=Blue
item.fireworksCharge.purple=Purple
item.fireworksCharge.cyan=Cyan
item.fireworksCharge.silver=Light Gray
item.fireworksCharge.gray=Gray
item.fireworksCharge.pink=Pink
item.fireworksCharge.lime=Lime
item.fireworksCharge.yellow=Yellow
item.fireworksCharge.lightBlue=Light Blue
item.fireworksCharge.magenta=Magenta
item.fireworksCharge.orange=Orange
item.fireworksCharge.white=White
item.fireworksCharge.customColor=Custom
item.fireworksCharge.fadeTo=Fade to
item.fireworksCharge.flicker=Twinkle
item.fireworksCharge.trail=Trail
item.fireworksCharge.type.0=Small Ball
item.fireworksCharge.type.1=Large Ball
item.fireworksCharge.type.2=Star-shaped
item.fireworksCharge.type.3=Creeper-shaped
item.fireworksCharge.type.4=Burst
item.fireworksCharge.type=Unknown Shape
item.netherbrick.name=Nether Brick
item.netherquartz.name=Nether Quartz
item.armorStand.name=Armor Stand
item.horsearmormetal.name=Iron Horse Armor
item.horsearmorgold.name=Gold Horse Armor
item.horsearmordiamond.name=Diamond Horse Armor
item.prismarineShard.name=Prismarine Shard
item.prismarineCrystals.name=Prismarine Crystals

container.inventory=Inventory
container.hopper=Item Hopper
container.crafting=Crafting
container.dispenser=Dispenser
container.dropper=Dropper
container.furnace=Furnace
container.enchant=Enchant
container.enchant.lapis.one=1 Lapis Lazuli
container.enchant.lapis.many=%d Lapis Lazuli
container.enchant.level.one=1 Enchantment Level
container.enchant.level.many=%d Enchantment Levels
container.enchant.clue=%s . . . ?
container.repair=Repair & Name
container.repair.cost=Enchantment Cost: %1$d
container.repair.expensive=Too Expensive!
container.creative=Item Selection
container.brewing=Brewing Stand
container.chest=Chest
container.chestDouble=Large Chest
container.minecart=Minecart
container.enderchest=Ender Chest
container.beacon=Beacon

container.isLocked=%s is locked!

item.dyed=Dyed
item.unbreakable=Unbreakable
item.canBreak=Can break:
item.canPlace=Can be placed on:

entity.Item.name=Item
entity.XPOrb.name=Experience Orb
entity.SmallFireball.name=Small Fireball
entity.Fireball.name=Fireball
entity.ThrownPotion.name=Potion

entity.Arrow.name=Arrow
entity.Snowball.name=Snowball
entity.Painting.name=Painting
entity.ArmorStand.name=Armor Stand

entity.Mob.name=Mob
entity.Monster.name=Monster

entity.Creeper.name=Creeper
entity.Skeleton.name=Skeleton
entity.Spider.name=Spider
entity.Giant.name=Giant
entity.Zombie.name=Zombie
entity.Slime.name=Slime
entity.Ghast.name=Ghast
entity.PigZombie.name=Zombie Pigman
entity.Enderman.name=Enderman
entity.Endermite.name=Endermite
entity.Silverfish.name=Silverfish
entity.CaveSpider.name=Cave Spider
entity.Blaze.name=Blaze
entity.LavaSlime.name=Magma Cube
entity.MushroomCow.name=Mooshroom
entity.Villager.name=Villager
entity.VillagerGolem.name=Iron Golem
entity.SnowMan.name=Snow Golem
entity.EnderDragon.name=Ender Dragon
entity.WitherBoss.name=Wither
entity.Witch.name=Witch
entity.Guardian.name=Guardian

entity.Villager.farmer=Farmer
entity.Villager.fisherman=Fisherman
entity.Villager.shepherd=Shepherd
entity.Villager.fletcher=Fletcher
entity.Villager.librarian=Librarian
entity.Villager.cleric=Cleric
entity.Villager.armor=Armorer
entity.Villager.weapon=Weapon Smith
entity.Villager.tool=Tool Smith
entity.Villager.butcher=Butcher
entity.Villager.leather=Leatherworker

entity.Pig.name=Pig
entity.Sheep.name=Sheep
entity.Cow.name=Cow
entity.Chicken.name=Chicken
entity.Squid.name=Squid
entity.Wolf.name=Wolf
entity.Ozelot.name=Ocelot
entity.Cat.name=Cat
entity.Bat.name=Bat
entity.EntityHorse.name=Horse
entity.horse.name=Horse
entity.donkey.name=Donkey
entity.mule.name=Mule
entity.skeletonhorse.name=Skeleton Horse
entity.zombiehorse.name=Zombie Horse
entity.Rabbit.name=Rabbit
entity.KillerBunny.name=The Killer Bunny

entity.PrimedTnt.name=Block of TNT
entity.FallingSand.name=Falling Block

entity.Minecart.name=Minecart
entity.Boat.name=Boat

entity.generic.name=unknown

death.fell.accident.ladder=%1$s fell off a ladder
death.fell.accident.vines=%1$s fell off some vines
death.fell.accident.water=%1$s fell out of the water
death.fell.accident.generic=%1$s fell from a high place
death.fell.killer=%1$s was doomed to fall
death.fell.assist=%1$s was doomed to fall by %2$s
death.fell.assist.item=%1$s was doomed to fall by %2$s using %3$s
death.fell.finish=%1$s fell too far and was finished by %2$s
death.fell.finish.item=%1$s fell too far and was finished by %2$s using %3$s

death.attack.lightningBolt=%1$s was struck by lightning
death.attack.inFire=%1$s went up in flames
death.attack.inFire.player=%1$s walked into fire whilst fighting %2$s
death.attack.onFire=%1$s burned to death
death.attack.onFire.player=%1$s was burnt to a crisp whilst fighting %2$s
death.attack.lava=%1$s tried to swim in lava
death.attack.lava.player=%1$s tried to swim in lava to escape %2$s
death.attack.inWall=%1$s suffocated in a wall
death.attack.drown=%1$s drowned
death.attack.drown.player=%1$s drowned whilst trying to escape %2$s
death.attack.starve=%1$s starved to death
death.attack.cactus=%1$s was pricked to death
death.attack.cactus.player=%1$s walked into a cactus whilst trying to escape %2$s
death.attack.generic=%1$s died
death.attack.explosion=%1$s blew up
death.attack.explosion.player=%1$s was blown up by %2$s
death.attack.magic=%1$s was killed by magic
death.attack.wither=%1$s withered away
death.attack.anvil=%1$s was squashed by a falling anvil
death.attack.fallingBlock=%1$s was squashed by a falling block
death.attack.mob=%1$s was slain by %2$s
death.attack.player=%1$s was slain by %2$s
death.attack.player.item=%1$s was slain by %2$s using %3$s
death.attack.arrow=%1$s was shot by %2$s
death.attack.arrow.item=%1$s was shot by %2$s using %3$s
death.attack.fireball=%1$s was fireballed by %2$s
death.attack.fireball.item=%1$s was fireballed by %2$s using %3$s
death.attack.thrown=%1$s was pummeled by %2$s
death.attack.thrown.item=%1$s was pummeled by %2$s using %3$s
death.attack.indirectMagic=%1$s was killed by %2$s using magic
death.attack.indirectMagic.item=%1$s was killed by %2$s using %3$s
death.attack.thorns=%1$s was killed trying to hurt %2$s
death.attack.fall=%1$s hit the ground too hard
death.attack.outOfWorld=%1$s fell out of the world

deathScreen.respawn=Respawn
deathScreen.deleteWorld=Delete world
deathScreen.titleScreen=Title screen
deathScreen.score=Score
deathScreen.title.hardcore=Game over!
deathScreen.hardcoreInfo=You cannot respawn in hardcore mode!
deathScreen.title=You died!
deathScreen.leaveServer=Leave server
deathScreen.quit.confirm=Are you sure you want to quit?

potion.effects.whenDrank=When Applied:
potion.empty=No Effects
potion.moveSpeed=Speed
potion.moveSlowdown=Slowness
potion.digSpeed=Haste
potion.digSlowDown=Mining Fatigue
potion.damageBoost=Strength
potion.heal=Instant Health
potion.harm=Instant Damage
potion.jump=Jump Boost
potion.confusion=Nausea
potion.regeneration=Regeneration
potion.resistance=Resistance
potion.fireResistance=Fire Resistance
potion.waterBreathing=Water Breathing
potion.invisibility=Invisibility
potion.blindness=Blindness
potion.nightVision=Night Vision
potion.hunger=Hunger
potion.weakness=Weakness
potion.poison=Poison
potion.wither=Wither
potion.healthBoost=Health Boost
potion.absorption=Absorption
potion.saturation=Saturation

potion.moveSpeed.postfix=Potion of Swiftness
potion.moveSlowdown.postfix=Potion of Slowness
potion.digSpeed.postfix=Potion of Haste
potion.digSlowDown.postfix=Potion of Dullness
potion.damageBoost.postfix=Potion of Strength
potion.weakness.postfix=Potion of Weakness
potion.heal.postfix=Potion of Healing
potion.harm.postfix=Potion of Harming
potion.jump.postfix=Potion of Leaping
potion.confusion.postfix=Potion of Nausea
potion.regeneration.postfix=Potion of Regeneration
potion.resistance.postfix=Potion of Resistance
potion.fireResistance.postfix=Potion of Fire Resistance
potion.waterBreathing.postfix=Potion of Water Breathing
potion.invisibility.postfix=Potion of Invisibility
potion.blindness.postfix=Potion of Blindness
potion.nightVision.postfix=Potion of Night Vision
potion.hunger.postfix=Potion of Hunger
potion.poison.postfix=Potion of Poison
potion.wither.postfix=Potion of Decay
potion.healthBoost.postfix=Potion of Health Boost
potion.absorption.postfix=Potion of Absorption
potion.saturation.postfix=Potion of Saturation

potion.potency.0=
potion.potency.1=II
potion.potency.2=III
potion.potency.3=IV

potion.prefix.grenade=Splash
potion.prefix.mundane=Mundane
potion.prefix.uninteresting=Uninteresting
potion.prefix.bland=Bland
potion.prefix.clear=Clear
potion.prefix.milky=Milky
potion.prefix.diffuse=Diffuse
potion.prefix.artless=Artless
potion.prefix.thin=Thin
potion.prefix.awkward=Awkward
potion.prefix.flat=Flat
potion.prefix.bulky=Bulky
potion.prefix.bungling=Bungling
potion.prefix.buttered=Buttered
potion.prefix.smooth=Smooth
potion.prefix.suave=Suave
potion.prefix.debonair=Debonair
potion.prefix.thick=Thick
potion.prefix.elegant=Elegant
potion.prefix.fancy=Fancy
potion.prefix.charming=Charming
potion.prefix.dashing=Dashing
potion.prefix.refined=Refined
potion.prefix.cordial=Cordial
potion.prefix.sparkling=Sparkling
potion.prefix.potent=Potent
potion.prefix.foul=Foul
potion.prefix.odorless=Odorless
potion.prefix.rank=Rank
potion.prefix.harsh=Harsh
potion.prefix.acrid=Acrid
potion.prefix.gross=Gross
potion.prefix.stinky=Stinky

enchantment.damage.all=Sharpness
enchantment.damage.undead=Smite
enchantment.damage.arthropods=Bane of Arthropods
enchantment.knockback=Knockback
enchantment.fire=Fire Aspect
enchantment.protect.all=Protection
enchantment.protect.fire=Fire Protection
enchantment.protect.fall=Feather Falling
enchantment.protect.explosion=Blast Protection
enchantment.protect.projectile=Projectile Protection
enchantment.oxygen=Respiration
enchantment.waterWorker=Aqua Affinity
enchantment.waterWalker=Depth Strider
enchantment.digging=Efficiency
enchantment.untouching=Silk Touch
enchantment.durability=Unbreaking
enchantment.lootBonus=Looting
enchantment.lootBonusDigger=Fortune
enchantment.lootBonusFishing=Luck of the Sea
enchantment.fishingSpeed=Lure
enchantment.arrowDamage=Power
enchantment.arrowFire=Flame
enchantment.arrowKnockback=Punch
enchantment.arrowInfinite=Infinity
enchantment.thorns=Thorns

enchantment.level.1=I
enchantment.level.2=II
enchantment.level.3=III
enchantment.level.4=IV
enchantment.level.5=V
enchantment.level.6=VI
enchantment.level.7=VII
enchantment.level.8=VIII
enchantment.level.9=IX
enchantment.level.10=X

gui.achievements=Achievements
gui.stats=Statistics

stats.tooltip.type.achievement=Achievement
stats.tooltip.type.statistic=Statistic
stat.generalButton=General
stat.blocksButton=Blocks
stat.itemsButton=Items
stat.mobsButton=Mobs

stat.used=Times Used
stat.mined=Times Mined
stat.depleted=Times Depleted
stat.crafted=Times Crafted
stat.entityKills=You killed %d %s
stat.entityKilledBy=%s killed you %d time(s)
stat.entityKills.none=You have never killed %s
stat.entityKilledBy.none=You have never been killed by %s

stat.startGame=Times played
stat.createWorld=Worlds created
stat.loadWorld=Saves loaded
stat.joinMultiplayer=Multiplayer joins
stat.leaveGame=Games quit

stat.playOneMinute=Minutes Played
stat.timeSinceDeath=Since Last Death

stat.walkOneCm=Distance Walked
stat.crouchOneCm=Distance Crouched
stat.sprintOneCm=Distance Sprinted
stat.fallOneCm=Distance Fallen
stat.swimOneCm=Distance Swum
stat.flyOneCm=Distance Flown
stat.climbOneCm=Distance Climbed
stat.diveOneCm=Distance Dove
stat.minecartOneCm=Distance by Minecart
stat.boatOneCm=Distance by Boat
stat.pigOneCm=Distance by Pig
stat.horseOneCm=Distance by Horse
stat.jump=Jumps
stat.drop=Items Dropped

stat.damageDealt=Damage Dealt
stat.damageTaken=Damage Taken
stat.deaths=Number of Deaths
stat.mobKills=Mob Kills
stat.animalsBred=Animals Bred
stat.playerKills=Player Kills
stat.fishCaught=Fish Caught
stat.treasureFished=Treasure Fished
stat.junkFished=Junk Fished
stat.talkedToVillager=Talked to Villagers
stat.tradedWithVillager=Traded with Villagers

stat.cakeSlicesEaten=Cake Slices Eaten
stat.cauldronFilled=Cauldrons Filled
stat.cauldronUsed=Water Taken from Cauldron
stat.armorCleaned=Armor Pieces Cleaned
stat.bannerCleaned=Banners Cleaned
stat.brewingstandInteraction=Interactions with Brewing Stand
stat.beaconInteraction=Interactions with Beacon
stat.dropperInspected=Droppers Searched
stat.hopperInspected=Hoppers Searched
stat.dispenserInspected=Dispensers Searched
stat.noteblockPlayed=Noteblocks played
stat.noteblockTuned=Noteblocks tuned
stat.flowerPotted=Plants potted
stat.trappedChestTriggered=Trapped Chests Triggered
stat.enderchestOpened=Ender Chests Opened
stat.itemEnchanted=Items Enchanted
stat.recordPlayed=Records Played
stat.furnaceInteraction=Interactions with Furnace
stat.workbenchInteraction=Interactions with Crafting Table
stat.chestOpened=Chests Opened

stat.mineBlock=%1$s Mined
stat.craftItem=%1$s Crafted
stat.useItem=%1$s Used
stat.breakItem=%1$s Depleted

achievement.get=Achievement get!

achievement.taken=Taken!
achievement.unknown=???

achievement.requires=Requires '%1$s'
achievement.openInventory=Taking Inventory
achievement.openInventory.desc=Press '%1$s' to open your inventory.
achievement.mineWood=Getting Wood
achievement.mineWood.desc=Attack a tree until a block of wood pops out
achievement.buildWorkBench=Benchmarking
achievement.buildWorkBench.desc=Craft a workbench with four blocks of planks
achievement.buildPickaxe=Time to Mine!
achievement.buildPickaxe.desc=Use planks and sticks to make a pickaxe
achievement.buildFurnace=Hot Topic
achievement.buildFurnace.desc=Construct a furnace out of eight stone blocks
achievement.acquireIron=Acquire Hardware
achievement.acquireIron.desc=Smelt an iron ingot
achievement.buildHoe=Time to Farm!
achievement.buildHoe.desc=Use planks and sticks to make a hoe
achievement.makeBread=Bake Bread
achievement.makeBread.desc=Turn wheat into bread
achievement.bakeCake=The Lie
achievement.bakeCake.desc=Wheat, sugar, milk and eggs!
achievement.buildBetterPickaxe=Getting an Upgrade
achievement.buildBetterPickaxe.desc=Construct a better pickaxe
achievement.overpowered=Overpowered
achievement.overpowered.desc=Build a Notch apple
achievement.cookFish=Delicious Fish
achievement.cookFish.desc=Catch and cook fish!
achievement.onARail=On A Rail
achievement.onARail.desc=Travel by minecart at least 1 km from where you started
achievement.buildSword=Time to Strike!
achievement.buildSword.desc=Use planks and sticks to make a sword
achievement.killEnemy=Monster Hunter
achievement.killEnemy.desc=Attack and destroy a monster
achievement.killCow=Cow Tipper
achievement.killCow.desc=Harvest some leather
achievement.breedCow=Repopulation
achievement.breedCow.desc=Breed two cows with wheat
achievement.flyPig=When Pigs Fly
achievement.flyPig.desc=Fly a pig off a cliff
achievement.snipeSkeleton=Sniper Duel
achievement.snipeSkeleton.desc=Kill a skeleton with an arrow from more than 50 meters
achievement.diamonds=DIAMONDS!
achievement.diamonds.desc=Acquire diamonds with your iron tools
achievement.diamondsToYou=Diamonds to you!
achievement.diamondsToYou.desc=Throw diamonds at another player.
achievement.portal=We Need to Go Deeper
achievement.portal.desc=Build a portal to the Nether
achievement.ghast=Return to Sender
achievement.ghast.desc=Destroy a Ghast with a fireball
achievement.blazeRod=Into Fire
achievement.blazeRod.desc=Relieve a Blaze of its rod
achievement.potion=Local Brewery
achievement.potion.desc=Brew a potion
achievement.theEnd=The End?
achievement.theEnd.desc=Locate the End
achievement.theEnd2=The End.
achievement.theEnd2.desc=Defeat the Ender Dragon
achievement.spawnWither=The Beginning?
achievement.spawnWither.desc=Spawn the Wither
achievement.killWither=The Beginning.
achievement.killWither.desc=Kill the Wither
achievement.fullBeacon=Beaconator
achievement.fullBeacon.desc=Create a full beacon
achievement.exploreAllBiomes=Adventuring Time
achievement.exploreAllBiomes.desc=Discover all biomes
achievement.enchantments=Enchanter
achievement.enchantments.desc=Use a book, obsidian and diamonds to construct an enchantment table
achievement.overkill=Overkill
achievement.overkill.desc=Deal nine hearts of damage in a single hit
achievement.bookcase=Librarian
achievement.bookcase.desc=Build some bookshelves to improve your enchantment table

commands.generic.exception=An unknown error occurred while attempting to perform this command
commands.generic.permission=You do not have permission to use this command
commands.generic.syntax=Invalid command syntax
commands.generic.player.notFound=That player cannot be found
commands.generic.entity.notFound=That entity cannot be found
commands.generic.entity.invalidUuid=The entity UUID provided is in an invalid format
commands.generic.entity.invalidType=Entity type '%s' is invalid
commands.generic.notFound=Unknown command. Try /help for a list of commands
commands.generic.parameter.invalid='%s' is not a valid parameter
commands.generic.num.invalid='%s' is not a valid number
commands.generic.boolean.invalid='%s' is not true or false
commands.generic.num.tooSmall=The number you have entered (%d) is too small, it must be at least %d
commands.generic.num.tooBig=The number you have entered (%d) is too big, it must be at most %d
commands.generic.double.tooSmall=The number you have entered (%.2f) is too small, it must be at least %.2f
commands.generic.double.tooBig=The number you have entered (%.2f) is too big, it must be at most %.2f
commands.generic.usage=Usage: %s

commands.setidletimeout.usage=/setidletimeout <Minutes until kick>
commands.setidletimeout.success=Successfully set the idle timeout to %d minutes.
commands.xp.failure.widthdrawXp=Cannot give player negative experience points
commands.xp.success=Given %d experience to %s
commands.xp.success.levels=Given %d levels to %s
commands.xp.success.negative.levels=Taken %d levels from %s
commands.xp.usage=/xp <amount> [player] OR /xp <amount>L [player]
commands.playsound.usage=/playsound <sound> <player> [x] [y] [z] [volume] [pitch] [minimumVolume]
commands.playsound.success=Played sound '%s' to %s
commands.playsound.playerTooFar=Player %s is too far away to hear the sound
commands.give.usage=/give <player> <item> [amount] [data] [dataTag]
commands.give.item.notFound=There is no such item with name %d
commands.give.block.notFound=There is no such block with name %d
commands.give.success=Given %s * %d to %s
commands.give.tagError=Data tag parsing failed: %s
commands.replaceitem.usage=/replaceitem <entity|block> ...
commands.replaceitem.entity.usage=/replaceitem entity <selector> <slot> <item> [amount] [data] [dataTag]
commands.replaceitem.block.usage=/replaceitem block <x> <y> <z> <slot> <item> [amount] [data] [dataTag]
commands.replaceitem.tagError=Data tag parsing failed: %s
commands.replaceitem.noContainer=Block at %d, %d, %d is not a container
commands.replaceitem.failed=Could not replace slot %d with %d * %s
commands.replaceitem.success=Replaced slot %d with %d * %s
commands.stats.usage=/stats <entity|block> ...
commands.stats.entity.usage=/stats entity <selector> <mode>
commands.stats.entity.set.usage=/stats entity <selector> set <stat> <selector> <objective>
commands.stats.entity.clear.usage=/stats entity <selector> clear <stat>
commands.stats.block.usage=/stats block <x> <y> <z> <mode> ...
commands.stats.block.set.usage=/stats block <x> <y> <z> set <stat> <selector> <objective>
commands.stats.block.clear.usage=/stats block <x> <y> <z> clear <stat>
commands.stats.noCompatibleBlock=Block at %d, %d, %d can not track stats
commands.stats.failed=Invalid parameters
commands.stats.cleared=Cleared %s stats
commands.stats.success=Storing %s stats in %s on %s
commands.summon.usage=/summon <EntityName> [x] [y] [z] [dataTag]
commands.summon.success=Object successfully summoned
commands.summon.failed=Unable to summon object
commands.summon.tagError=Data tag parsing failed: %s
commands.summon.outOfWorld=Cannot summon the object out of the world
commands.testforblock.usage=/testforblock <x> <y> <z> <TileName> [dataValue] [dataTag]
commands.testforblock.failed.tile=The block at %d,%d,%d is %s (expected: %s).
commands.testforblock.failed.data=The block at %d,%d,%d had the data value of %s (expected: %s).
commands.testforblock.failed.nbt=The block at %d,%d,%d did not have the required NBT keys.
commands.testforblock.failed.tileEntity=The block at %d,%d,%d is not a tile entity and cannot support tag matching.
commands.testforblock.success=Successfully found the block at %d,%d,%d.
commands.testforblock.outOfWorld=Cannot test for block outside of the world
commands.setblock.usage=/setblock <x> <y> <z> <TileName> [dataValue] [oldBlockHandling] [dataTag]
commands.setblock.success=Block placed
commands.setblock.failed=Unable to place block
commands.setblock.tagError=Data tag parsing failed: %s
commands.setblock.outOfWorld=Cannot place block outside of the world
commands.setblock.notFound=There is no such block with ID/name %s
commands.setblock.noChange=The block couldn't be placed
commands.fill.usage=/fill <x1> <y1> <z1> <x2> <y2> <z2> <TileName> [dataValue] [oldBlockHandling] [dataTag]
commands.fill.outOfWorld=Cannot place blocks outside of the world
commands.fill.tagError=Data tag parsing failed: %s
commands.fill.success=%d blocks filled
commands.fill.failed=No blocks filled
commands.fill.tooManyBlocks=Too many blocks in the specified area (%d > %d)
commands.clone.usage=/clone <x1> <y1> <z1> <x2> <y2> <z2> <x> <y> <z> [maskMode] [cloneMode]
commands.clone.outOfWorld=Cannot access blocks outside of the world
commands.clone.noOverlap=Source and destination can not overlap
commands.clone.success=%d blocks cloned
commands.clone.failed=No blocks cloned
commands.clone.tooManyBlocks=Too many blocks in the specified area (%d > %d)
commands.compare.usage=/testforblocks <x1> <y1> <z1> <x2> <y2> <z2> <x> <y> <z> [mode]
commands.compare.outOfWorld=Cannot access blocks outside of the world
commands.compare.failed=Source and destination are not identical
commands.compare.success=%d blocks compared
commands.compare.tooManyBlocks=Too many blocks in the specified area (%d > %d)
commands.blockdata.usage=/blockdata <x> <y> <z> <dataTag>
commands.blockdata.success=Block data updated to: %s
commands.blockdata.tagError=Data tag parsing failed: %s
commands.blockdata.outOfWorld=Cannot change block outside of the world
commands.blockdata.notValid=The target block is not a data holder block
commands.blockdata.failed=The data tag did not change: %s
commands.entitydata.usage=/entitydata <entity> <dataTag>
commands.entitydata.success=Entity data updated to: %s
commands.entitydata.tagError=Data tag parsing failed: %s
commands.entitydata.noPlayers=%s is a player and cannot be changed
commands.entitydata.failed=The data tag did not change: %s
commands.effect.usage=/effect <player> <effect> [seconds] [amplifier] [hideParticles] OR /effect <player> clear
commands.effect.notFound=There is no such mob effect with ID %d
commands.effect.success=Given %1$s (ID %2$d) * %3$d to %4$s for %5$d seconds
commands.effect.success.removed=Took %1$s from %2$s
commands.effect.success.removed.all=Took all effects from %s
commands.effect.failure.notActive=Couldn't take %1$s from %2$s as they do not have the effect
commands.effect.failure.notActive.all=Couldn't take any effects from %s as they do not have any
commands.enchant.usage=/enchant <player> <enchantment ID> [level]
commands.enchant.notFound=There is no such enchantment with ID %d
commands.enchant.noItem=The target doesn't hold an item
commands.enchant.cantEnchant=The selected enchantment can't be added to the target item
commands.enchant.cantCombine=%1$s can't be combined with %2$s
commands.enchant.success=Enchanting succeeded
commands.particle.usage=/particle <name> <x> <y> <z> <xd> <yd> <zd> <speed> [count] [mode]
commands.particle.success=Playing effect %s for %d times
commands.particle.notFound=Unknown effect name (%s)
commands.clear.usage=/clear [player] [item] [data] [maxCount] [dataTag]
commands.clear.success=Cleared the inventory of %s, removing %d items
commands.clear.testing=%s has %d items that match the criteria
commands.clear.failure=Could not clear the inventory of %s, no items to remove
commands.clear.tagError=Data tag parsing failed: %s
commands.downfall.usage=/toggledownfall
commands.downfall.success=Toggled downfall
commands.time.usage=/time <set|add|query> <value>
commands.time.added=Added %d to the time
commands.time.set=Set the time to %d
commands.time.query=Time is %d
commands.players.usage=/list
commands.players.list=There are %d/%d players online:
commands.banlist.ips=There are %d total banned IP addresses:
commands.banlist.players=There are %d total banned players:
commands.banlist.usage=/banlist [ips|players]
commands.kill.usage=/kill [player|entity]
commands.kill.successful=Killed %s
commands.kick.success=Kicked %s from the game
commands.kick.success.reason=Kicked %s from the game: '%s'
commands.kick.usage=/kick <player> [reason ...]
commands.op.success=Opped %s
commands.op.failed=Could not op %s
commands.op.usage=/op <player>
commands.deop.success=De-opped %s
commands.deop.failed=Could not de-op %s
commands.deop.usage=/deop <player>
commands.say.usage=/say <message ...>
commands.ban.success=Banned player %s
commands.ban.failed=Could not ban player %s
commands.ban.usage=/ban <name> [reason ...]
commands.unban.success=Unbanned player %s
commands.unban.failed=Could not unban player %s
commands.unban.usage=/pardon <name>
commands.banip.invalid=You have entered an invalid IP address or a player that is not online
commands.banip.success=Banned IP address %s
commands.banip.success.players=Banned IP address %s belonging to %s
commands.banip.usage=/ban-ip <address|name> [reason ...]
commands.unbanip.invalid=You have entered an invalid IP address
commands.unbanip.success=Unbanned IP address %s
commands.unbanip.usage=/pardon-ip <address>
commands.save.usage=/save-all
commands.save-on.alreadyOn=Saving is already turned on.
commands.save-on.usage=/save-on
commands.save-off.alreadyOff=Saving is already turned off.
commands.save-off.usage=/save-off
commands.save.enabled=Turned on world auto-saving
commands.save.disabled=Turned off world auto-saving
commands.save.start=Saving...
commands.save.success=Saved the world
commands.save.failed=Saving failed: %s
commands.stop.usage=/stop
commands.stop.start=Stopping the server
commands.tp.success=Teleported %s to %s
commands.tp.success.coordinates=Teleported %s to %s, %s, %s
commands.tp.usage=/tp [target player] <destination player> OR /tp [target player] <x> <y> <z> [<y-rot> <x-rot>]
commands.tp.notSameDimension=Unable to teleport because players are not in the same dimension
commands.whitelist.list=There are %d (out of %d seen) whitelisted players:
commands.whitelist.enabled=Turned on the whitelist
commands.whitelist.disabled=Turned off the whitelist
commands.whitelist.reloaded=Reloaded the whitelist
commands.whitelist.add.success=Added %s to the whitelist
commands.whitelist.add.failed=Could not add %s to the whitelist
commands.whitelist.add.usage=/whitelist add <player>
commands.whitelist.remove.success=Removed %s from the whitelist
commands.whitelist.remove.failed=Could not remove %s from the whitelist
commands.whitelist.remove.usage=/whitelist remove <player>
commands.whitelist.usage=/whitelist <on|off|list|add|remove|reload>
commands.scoreboard.usage=/scoreboard <objectives|players|teams> ...
commands.scoreboard.noMultiWildcard=Only one user wildcard allowed
commands.scoreboard.allMatchesFailed=All matches failed
commands.scoreboard.teamNotFound=No team was found by the name '%s'
commands.scoreboard.objectiveNotFound=No objective was found by the name '%s'
commands.scoreboard.objectiveReadOnly=The objective '%s' is read-only and cannot be set
commands.scoreboard.objectives.usage=/scoreboard objectives <list|add|remove|setdisplay> ...
commands.scoreboard.objectives.setdisplay.usage=/scoreboard objectives setdisplay <slot> [objective]
commands.scoreboard.objectives.setdisplay.invalidSlot=No such display slot '%s'
commands.scoreboard.objectives.setdisplay.successCleared=Cleared objective display slot '%s'
commands.scoreboard.objectives.setdisplay.successSet=Set the display objective in slot '%s' to '%s'
commands.scoreboard.objectives.add.usage=/scoreboard objectives add <name> <criteriaType> [display name ...]
commands.scoreboard.objectives.add.wrongType=Invalid objective criteria type '%s'
commands.scoreboard.objectives.add.alreadyExists=An objective with the name '%s' already exists
commands.scoreboard.objectives.add.tooLong=The name '%s' is too long for an objective, it can be at most %d characters long
commands.scoreboard.objectives.add.displayTooLong=The display name '%s' is too long for an objective, it can be at most %d characters long
commands.scoreboard.objectives.add.success=Added new objective '%s' successfully
commands.scoreboard.objectives.remove.usage=/scoreboard objectives remove <name>
commands.scoreboard.objectives.remove.success=Removed objective '%s' successfully
commands.scoreboard.objectives.list.count=Showing %d objective(s) on scoreboard:
commands.scoreboard.objectives.list.entry=- %s: displays as '%s' and is type '%s'
commands.scoreboard.objectives.list.empty=There are no objectives on the scoreboard
commands.scoreboard.players.usage=/scoreboard players <set|add|remove|reset|list|enable|test|operation> ...
commands.scoreboard.players.name.tooLong=The name '%s' is too long for a player, it can be at most %d characters long
commands.scoreboard.players.set.success=Set score of %s for player %s to %d
commands.scoreboard.players.set.tagMismatch=The dataTag does not match for %s
commands.scoreboard.players.set.tagError=Could not parse dataTag, reason: %s
commands.scoreboard.players.set.usage=/scoreboard players set <player> <objective> <score> [dataTag]
commands.scoreboard.players.add.usage=/scoreboard players add <player> <objective> <count> [dataTag]
commands.scoreboard.players.remove.usage=/scoreboard players remove <player> <objective> <count> [dataTag]
commands.scoreboard.players.reset.usage=/scoreboard players reset <player> [objective]
commands.scoreboard.players.reset.success=Reset scores of player %s
commands.scoreboard.players.resetscore.success=Reset score %s of player %s
commands.scoreboard.players.list.usage=/scoreboard players list [name]
commands.scoreboard.players.list.count=Showing %d tracked players on the scoreboard:
commands.scoreboard.players.list.empty=There are no tracked players on the scoreboard
commands.scoreboard.players.list.player.count=Showing %d tracked objective(s) for %s:
commands.scoreboard.players.list.player.entry=- %2$s: %1$d (%3$s)
commands.scoreboard.players.list.player.empty=Player %s has no scores recorded
commands.scoreboard.players.enable.usage=/scoreboard players enable <player> <trigger>
commands.scoreboard.players.enable.success=Enabled trigger %s for %s
commands.scoreboard.players.enable.noTrigger=Objective %s is not a trigger
commands.scoreboard.players.test.usage=/scoreboard players test <player> <objective> <min> <max>
commands.scoreboard.players.test.notFound=No %s score for %s found
commands.scoreboard.players.test.failed=Score %d is NOT in range %d to %d
commands.scoreboard.players.test.success=Score %d is in range %d to %d
commands.scoreboard.players.operation.usage=/scoreboard players operation <targetName> <targetObjective> <operation> <selector> <objective>
commands.scoreboard.players.operation.notFound=No %s score for %s found
commands.scoreboard.players.operation.invalidOperation=Invalid operation %s
commands.scoreboard.players.operation.success=Operation applied successfully
commands.scoreboard.teams.usage=/scoreboard teams <list|add|remove|empty|join|leave|option> ...
commands.scoreboard.teams.add.usage=/scoreboard teams add <name> [display name ...]
commands.scoreboard.teams.add.alreadyExists=A team with the name '%s' already exists
commands.scoreboard.teams.add.tooLong=The name '%s' is too long for a team, it can be at most %d characters long
commands.scoreboard.teams.add.displayTooLong=The display name '%s' is too long for a team, it can be at most %d characters long
commands.scoreboard.teams.add.success=Added new team '%s' successfully
commands.scoreboard.teams.list.usage=/scoreboard teams list [name]
commands.scoreboard.teams.list.count=Showing %d teams on the scoreboard:
commands.scoreboard.teams.list.entry=- %1$s: '%2$s' has %3$d players
commands.scoreboard.teams.list.empty=There are no teams registered on the scoreboard
commands.scoreboard.teams.list.player.count=Showing %d player(s) in team %s:
commands.scoreboard.teams.list.player.entry=- %2$s: %1$d (%3$s)
commands.scoreboard.teams.list.player.empty=Team %s has no players
commands.scoreboard.teams.empty.usage=/scoreboard teams empty <team>
commands.scoreboard.teams.empty.alreadyEmpty=Team %s is already empty, cannot remove nonexistant players
commands.scoreboard.teams.empty.success=Removed all %d player(s) from team %s
commands.scoreboard.teams.remove.usage=/scoreboard teams remove <name>
commands.scoreboard.teams.remove.success=Removed team %s
commands.scoreboard.teams.join.usage=/scoreboard teams join <team> [player]
commands.scoreboard.teams.join.success=Added %d player(s) to team %s: %s
commands.scoreboard.teams.join.failure=Could not add %d player(s) to team %s: %s
commands.scoreboard.teams.leave.usage=/scoreboard teams leave [player]
commands.scoreboard.teams.leave.success=Removed %d player(s) from their teams: %s
commands.scoreboard.teams.leave.failure=Could not remove %d player(s) from their teams: %s
commands.scoreboard.teams.leave.noTeam=You are not in a team
commands.scoreboard.teams.option.usage=/scoreboard teams option <team> <friendlyfire|color|seeFriendlyInvisibles|nametagVisibility|deathMessageVisibility> <value>
commands.scoreboard.teams.option.noValue=Valid values for option %s are: %s
commands.scoreboard.teams.option.success=Set option %s for team %s to %s
commands.execute.usage=/execute <entity> <x> <y> <z> <command> OR /execute <entity> <x> <y> <z> detect <x> <y> <z> <block> <data> <command>
commands.execute.allInvocationsFailed=All invocations failed: '%s'
commands.execute.failed=Failed to execute '%s' as %s
commands.gamemode.success.self=Set own game mode to %s
commands.gamemode.success.other=Set %s's game mode to %s
commands.gamemode.usage=/gamemode <mode> [player]
commands.defaultgamemode.usage=/defaultgamemode <mode>
commands.defaultgamemode.success=The world's default game mode is now %s
commands.me.usage=/me <action ...>
commands.help.header=--- Showing help page %d of %d (/help <page>) ---
commands.help.footer=Tip: Use the <tab> key while typing a command to auto-complete the command or its arguments
commands.help.usage=/help [page|command name]
commands.trigger.usage=/trigger <objective> <add|set> <value>
commands.trigger.invalidObjective=Invalid trigger name %s
commands.trigger.invalidMode=Invalid trigger mode %s
commands.trigger.disabled=Trigger %s is not enabled
commands.trigger.invalidPlayer=Only players can use the /trigger command
commands.trigger.success=Trigger %s changed with %s %s
commands.publish.usage=/publish
commands.publish.started=Local game hosted on port %s
commands.publish.failed=Unable to host local game
commands.debug.start=Started debug profiling
commands.debug.stop=Stopped debug profiling after %.2f seconds (%d ticks)
commands.debug.notStarted=Can't stop profiling when we haven't started yet!
commands.debug.usage=/debug <start|stop>
commands.chunkinfo.usage=/chunkinfo [<x> <y> <z>]
commands.chunkinfo.location=Chunk location: (%d, %d, %d)
commands.chunkinfo.noChunk=No chunk found at chunk position %d, %d, %d
commands.chunkinfo.notEmpty=Chunk is not empty.
commands.chunkinfo.empty=Chunk is empty.
commands.chunkinfo.notCompiled=Chunk is not compiled.
commands.chunkinfo.compiled=Chunk is compiled.
commands.chunkinfo.hasNoRenderableLayers=Chunk has no renderable layers.
commands.chunkinfo.hasLayers=Chunk has layers: %s
commands.chunkinfo.isEmpty=Chunk has empty layers: %s
commands.chunkinfo.vertices=%s layer's buffer contains %d vertices
commands.chunkinfo.data=First 64 vertices are: %s
commands.tellraw.usage=/tellraw <player> <raw json message>
commands.tellraw.jsonException=Invalid json: %s
commands.message.usage=/tell <player> <private message ...>
commands.message.sameTarget=You can't send a private message to yourself!
commands.message.display.outgoing=You whisper to %s: %s
commands.message.display.incoming=%s whispers to you: %s
commands.difficulty.usage=/difficulty <new difficulty>
commands.difficulty.success=Set game difficulty to %s
commands.spawnpoint.usage=/spawnpoint [player] [<x> <y> <z>]
commands.spawnpoint.success=Set %s's spawn point to (%d, %d, %d)
commands.setworldspawn.usage=/setworldspawn [<x> <y> <z>]
commands.setworldspawn.success=Set the world spawn point to (%d, %d, %d)
commands.gamerule.usage=/gamerule <rule name> [value]
commands.gamerule.success=Game rule has been updated
commands.gamerule.norule=No game rule called '%s' is available
commands.gamerule.nopermission=Only server owners can change '%s'
commands.weather.usage=/weather <clear|rain|thunder> [duration in seconds]
commands.weather.clear=Changing to clear weather
commands.weather.rain=Changing to rainy weather
commands.weather.thunder=Changing to rain and thunder
commands.testfor.usage=/testfor <player> [dataTag]
commands.testfor.failure=%s did not match the required data structure
commands.testfor.success=Found %s
commands.testfor.tagError=Data tag parsing failed: %s
commands.seed.usage=/seed
commands.seed.success=Seed: %s
commands.spreadplayers.usage=/spreadplayers <x> <z> <spreadDistance> <maxRange> <respectTeams true|false> <player ...>
commands.spreadplayers.spreading.teams=Spreading %s teams %s blocks around %s,%s (min %s blocks apart)
commands.spreadplayers.spreading.players=Spreading %s players %s blocks around %s,%s (min %s blocks apart)
commands.spreadplayers.success.teams=Successfully spread %s teams around %s,%s
commands.spreadplayers.success.players=Successfully spread %s players around %s,%s
commands.spreadplayers.info.teams=(Average distance between teams is %s blocks apart after %s iterations)
commands.spreadplayers.info.players=(Average distance between players is %s blocks apart after %s iterations)
commands.spreadplayers.failure.teams=Could not spread %s teams around %s,%s (too many players for space - try using spread of at most %s)
commands.spreadplayers.failure.players=Could not spread %s players around %s,%s (too many players for space - try using spread of at most %s)
commands.achievement.usage=/achievement <give|take> <stat_name|*> [player]
commands.achievement.unknownAchievement=Unknown achievement or statistic '%s'
commands.achievement.alreadyHave=Player %s already has achievement %s
commands.achievement.dontHave=Player %s doesn't have achievement %s
commands.achievement.give.success.all=Successfully given all achievements to %s
commands.achievement.give.success.one=Successfully given %s the stat %s
commands.achievement.take.success.all=Successfully taken all achievements from %s
commands.achievement.take.success.one=Successfully taken the stat %s from %s
commands.achievement.statTooLow=Player %s does not have the stat %s
commands.worldborder.usage=/worldborder <set|center|damage|warning|get|add> ...
commands.worldborder.add.usage=/worldborder add <sizeInBlocks> [timeInSeconds]
commands.worldborder.set.usage=/worldborder set <sizeInBlocks> [timeInSeconds]
commands.worldborder.set.success=Set world border to %s blocks wide (from %s blocks)
commands.worldborder.get.success=World border is currently %s blocks wide
commands.worldborder.setSlowly.shrink.success=Shrinking world border to %s blocks wide (down from %s blocks) over %s seconds
commands.worldborder.setSlowly.grow.success=Growing world border to %s blocks wide (up from %s blocks) over %s seconds
commands.worldborder.center.usage=/worldborder center <x> <z>
commands.worldborder.center.success=Set world border center to %s,%s
commands.worldborder.damage.usage=/worldborder damage <buffer|amount>
commands.worldborder.damage.buffer.usage=/worldborder damage buffer <sizeInBlocks>
commands.worldborder.damage.buffer.success=Set world border damage buffer to %s blocks (from %s blocks)
commands.worldborder.damage.amount.usage=/worldborder damage amount <damagePerBlock>
commands.worldborder.damage.amount.success=Set world border damage amount to %s per block (from %s per block)
commands.worldborder.warning.usage=/worldborder warning <time|distance>
commands.worldborder.warning.time.usage=/worldborder warning time <seconds>
commands.worldborder.warning.time.success=Set world border warning to %s seconds away (from %s seconds)
commands.worldborder.warning.distance.usage=/worldborder warning distance <blocks>
commands.worldborder.warning.distance.success=Set world border warning to %s blocks away (from %s blocks)
commands.title.usage=/title <player> <title|subtitle|clear|reset|times> ...
commands.title.usage.title=/title <player> title|subtitle <raw json title>
commands.title.usage.clear=/title <player> clear|reset
commands.title.usage.times=/title <player> times <fadeIn> <stay> <fadeOut>
commands.title.success=Title command successfully executed

itemGroup.buildingBlocks=Building Blocks
itemGroup.decorations=Decoration Blocks
itemGroup.redstone=Redstone
itemGroup.transportation=Transportation
itemGroup.misc=Miscellaneous
itemGroup.search=Search Items
itemGroup.food=Foodstuffs
itemGroup.tools=Tools
itemGroup.combat=Combat
itemGroup.brewing=Brewing
itemGroup.materials=Materials
itemGroup.inventory=Survival Inventory

inventory.binSlot=Destroy Item

advMode.setCommand=Set Console Command for Block
advMode.setCommand.success=Command set: %s
advMode.command=Console Command
advMode.nearestPlayer=Use "@p" to target nearest player
advMode.randomPlayer=Use "@r" to target random player
advMode.allPlayers=Use "@a" to target all players
advMode.allEntities=Use "@e" to target all entities
advMode.previousOutput=Previous Output

advMode.notEnabled=Command blocks are not enabled on this server
advMode.notAllowed=Must be an opped player in creative mode

mount.onboard=Press %1$s to dismount

build.tooHigh=Height limit for building is %s blocks

attribute.modifier.plus.0=+%d %s
attribute.modifier.plus.1=+%d%% %s
attribute.modifier.plus.2=+%d%% %s
attribute.modifier.take.0=-%d %s
attribute.modifier.take.1=-%d%% %s
attribute.modifier.take.2=-%d%% %s

attribute.name.horse.jumpStrength=Horse Jump Strength
attribute.name.zombie.spawnReinforcements=Zombie Reinforcements
attribute.name.generic.maxHealth=Max Health
attribute.name.generic.followRange=Mob Follow Range
attribute.name.generic.knockbackResistance=Knockback Resistance
attribute.name.generic.movementSpeed=Speed
attribute.name.generic.attackDamage=Attack Damage

screenshot.success=Saved screenshot as %s
screenshot.failure=Couldn't save screenshot: %s

item.banner.black.name=Black Banner
item.banner.red.name=Red Banner
item.banner.green.name=Green Banner
item.banner.brown.name=Brown Banner
item.banner.blue.name=Blue Banner
item.banner.purple.name=Purple Banner
item.banner.cyan.name=Cyan Banner
item.banner.silver.name=Light Gray Banner
item.banner.gray.name=Gray Banner
item.banner.pink.name=Pink Banner
item.banner.lime.name=Lime Banner
item.banner.yellow.name=Yellow Banner
item.banner.lightBlue.name=Light Blue Banner
item.banner.magenta.name=Magenta Banner
item.banner.orange.name=Orange Banner
item.banner.white.name=White Banner

item.banner.square_bottom_left.black=Black Base Dexter Canton
item.banner.square_bottom_left.red=Red Base Dexter Canton
item.banner.square_bottom_left.green=Green Base Dexter Canton
item.banner.square_bottom_left.brown=Brown Base Dexter Canton
item.banner.square_bottom_left.blue=Blue Base Dexter Canton
item.banner.square_bottom_left.purple=Purple Base Dexter Canton
item.banner.square_bottom_left.cyan=Cyan Base Dexter Canton
item.banner.square_bottom_left.silver=Light Gray Base Dexter Canton
item.banner.square_bottom_left.gray=Gray Base Dexter Canton
item.banner.square_bottom_left.pink=Pink Base Dexter Canton
item.banner.square_bottom_left.lime=Lime Base Dexter Canton
item.banner.square_bottom_left.yellow=Yellow Base Dexter Canton
item.banner.square_bottom_left.lightBlue=Light Blue Base Dexter Canton
item.banner.square_bottom_left.magenta=Magenta Base Dexter Canton
item.banner.square_bottom_left.orange=Orange Base Dexter Canton
item.banner.square_bottom_left.white=White Base Dexter Canton

item.banner.square_bottom_right.black=Black Base Sinister Canton
item.banner.square_bottom_right.red=Red Base Sinister Canton
item.banner.square_bottom_right.green=Green Base Sinister Canton
item.banner.square_bottom_right.brown=Brown Base Sinister Canton
item.banner.square_bottom_right.blue=Blue Base Sinister Canton
item.banner.square_bottom_right.purple=Purple Base Sinister Canton
item.banner.square_bottom_right.cyan=Cyan Base Sinister Canton
item.banner.square_bottom_right.silver=Light Gray Base Sinister Canton
item.banner.square_bottom_right.gray=Gray Base Sinister Canton
item.banner.square_bottom_right.pink=Pink Base Sinister Canton
item.banner.square_bottom_right.lime=Lime Base Sinister Canton
item.banner.square_bottom_right.yellow=Yellow Base Sinister Canton
item.banner.square_bottom_right.lightBlue=Light Blue Base Sinister Canton
item.banner.square_bottom_right.magenta=Magenta Base Sinister Canton
item.banner.square_bottom_right.orange=Orange Base Sinister Canton
item.banner.square_bottom_right.white=White Base Sinister Canton

item.banner.square_top_left.black=Black Chief Dexter Canton
item.banner.square_top_left.red=Red Chief Dexter Canton
item.banner.square_top_left.green=Green Chief Dexter Canton
item.banner.square_top_left.brown=Brown Chief Dexter Canton
item.banner.square_top_left.blue=Blue Chief Dexter Canton
item.banner.square_top_left.purple=Purple Chief Dexter Canton
item.banner.square_top_left.cyan=Cyan Chief Dexter Canton
item.banner.square_top_left.silver=Light Gray Chief Dexter Canton
item.banner.square_top_left.gray=Gray Chief Dexter Canton
item.banner.square_top_left.pink=Pink Chief Dexter Canton
item.banner.square_top_left.lime=Lime Chief Dexter Canton
item.banner.square_top_left.yellow=Yellow Chief Dexter Canton
item.banner.square_top_left.lightBlue=Light Blue Chief Dexter Canton
item.banner.square_top_left.magenta=Magenta Chief Dexter Canton
item.banner.square_top_left.orange=Orange Chief Dexter Canton
item.banner.square_top_left.white=White Chief Dexter Canton

item.banner.square_top_right.black=Black Chief Sinister Canton
item.banner.square_top_right.red=Red Chief Sinister Canton
item.banner.square_top_right.green=Green Chief Sinister Canton
item.banner.square_top_right.brown=Brown Chief Sinister Canton
item.banner.square_top_right.blue=Blue Chief Sinister Canton
item.banner.square_top_right.purple=Purple Chief Sinister Canton
item.banner.square_top_right.cyan=Cyan Chief Sinister Canton
item.banner.square_top_right.silver=Light Gray Chief Sinister Canton
item.banner.square_top_right.gray=Gray Chief Sinister Canton
item.banner.square_top_right.pink=Pink Chief Sinister Canton
item.banner.square_top_right.lime=Lime Chief Sinister Canton
item.banner.square_top_right.yellow=Yellow Chief Sinister Canton
item.banner.square_top_right.lightBlue=Light Blue Chief Sinister Canton
item.banner.square_top_right.magenta=Magenta Chief Sinister Canton
item.banner.square_top_right.orange=Orange Chief Sinister Canton
item.banner.square_top_right.white=White Chief Sinister Canton

item.banner.stripe_bottom.black=Black Base Fess
item.banner.stripe_bottom.red=Red Base Fess
item.banner.stripe_bottom.green=Green Base Fess
item.banner.stripe_bottom.brown=Brown Base Fess
item.banner.stripe_bottom.blue=Blue Base Fess
item.banner.stripe_bottom.purple=Purple Base Fess
item.banner.stripe_bottom.cyan=Cyan Base Fess
item.banner.stripe_bottom.silver=Light Gray Base Fess
item.banner.stripe_bottom.gray=Gray Base Fess
item.banner.stripe_bottom.pink=Pink Base Fess
item.banner.stripe_bottom.lime=Lime Base Fess
item.banner.stripe_bottom.yellow=Yellow Base Fess
item.banner.stripe_bottom.lightBlue=Light Blue Base Fess
item.banner.stripe_bottom.magenta=Magenta Base Fess
item.banner.stripe_bottom.orange=Orange Base Fess
item.banner.stripe_bottom.white=White Base Fess

item.banner.stripe_top.black=Black Chief Fess
item.banner.stripe_top.red=Red Chief Fess
item.banner.stripe_top.green=Green Chief Fess
item.banner.stripe_top.brown=Brown Chief Fess
item.banner.stripe_top.blue=Blue Chief Fess
item.banner.stripe_top.purple=Purple Chief Fess
item.banner.stripe_top.cyan=Cyan Chief Fess
item.banner.stripe_top.silver=Light Gray Chief Fess
item.banner.stripe_top.gray=Gray Chief Fess
item.banner.stripe_top.pink=Pink Chief Fess
item.banner.stripe_top.lime=Lime Chief Fess
item.banner.stripe_top.yellow=Yellow Chief Fess
item.banner.stripe_top.lightBlue=Light Blue Chief Fess
item.banner.stripe_top.magenta=Magenta Chief Fess
item.banner.stripe_top.orange=Orange Chief Fess
item.banner.stripe_top.white=White Chief Fess

item.banner.stripe_left.black=Black Pale Dexter
item.banner.stripe_left.red=Red Pale Dexter
item.banner.stripe_left.green=Green Pale Dexter
item.banner.stripe_left.brown=Brown Pale Dexter
item.banner.stripe_left.blue=Blue Pale Dexter
item.banner.stripe_left.purple=Purple Pale Dexter
item.banner.stripe_left.cyan=Cyan Pale Dexter
item.banner.stripe_left.silver=Light Gray Pale Dexter
item.banner.stripe_left.gray=Gray Pale Dexter
item.banner.stripe_left.pink=Pink Pale Dexter
item.banner.stripe_left.lime=Lime Pale Dexter
item.banner.stripe_left.yellow=Yellow Pale Dexter
item.banner.stripe_left.lightBlue=Light Blue Pale Dexter
item.banner.stripe_left.magenta=Magenta Pale Dexter
item.banner.stripe_left.orange=Orange Pale Dexter
item.banner.stripe_left.white=White Pale Dexter

item.banner.stripe_right.black=Black Pale Sinister
item.banner.stripe_right.red=Red Pale Sinister
item.banner.stripe_right.green=Green Pale Sinister
item.banner.stripe_right.brown=Brown Pale Sinister
item.banner.stripe_right.blue=Blue Pale Sinister
item.banner.stripe_right.purple=Purple Pale Sinister
item.banner.stripe_right.cyan=Cyan Pale Sinister
item.banner.stripe_right.silver=Light Gray Pale Sinister
item.banner.stripe_right.gray=Gray Pale Sinister
item.banner.stripe_right.pink=Pink Pale Sinister
item.banner.stripe_right.lime=Lime Pale Sinister
item.banner.stripe_right.yellow=Yellow Pale Sinister
item.banner.stripe_right.lightBlue=Light Blue Pale Sinister
item.banner.stripe_right.magenta=Magenta Pale Sinister
item.banner.stripe_right.orange=Orange Pale Sinister
item.banner.stripe_right.white=White Pale Sinister

item.banner.stripe_center.black=Black Pale
item.banner.stripe_center.red=Red Pale
item.banner.stripe_center.green=Green Pale
item.banner.stripe_center.brown=Brown Pale
item.banner.stripe_center.blue=Blue Pale
item.banner.stripe_center.purple=Purple Pale
item.banner.stripe_center.cyan=Cyan Pale
item.banner.stripe_center.silver=Light Gray Pale
item.banner.stripe_center.gray=Gray Pale
item.banner.stripe_center.pink=Pink Pale
item.banner.stripe_center.lime=Lime Pale
item.banner.stripe_center.yellow=Yellow Pale
item.banner.stripe_center.lightBlue=Light Blue Pale
item.banner.stripe_center.magenta=Magenta Pale
item.banner.stripe_center.orange=Orange Pale
item.banner.stripe_center.white=White Pale

item.banner.stripe_middle.black=Black Fess
item.banner.stripe_middle.red=Red Fess
item.banner.stripe_middle.green=Green Fess
item.banner.stripe_middle.brown=Brown Fess
item.banner.stripe_middle.blue=Blue Fess
item.banner.stripe_middle.purple=Purple Fess
item.banner.stripe_middle.cyan=Cyan Fess
item.banner.stripe_middle.silver=Light Gray Fess
item.banner.stripe_middle.gray=Gray Fess
item.banner.stripe_middle.pink=Pink Fess
item.banner.stripe_middle.lime=Lime Fess
item.banner.stripe_middle.yellow=Yellow Fess
item.banner.stripe_middle.lightBlue=Light Blue Fess
item.banner.stripe_middle.magenta=Magenta Fess
item.banner.stripe_middle.orange=Orange Fess
item.banner.stripe_middle.white=White Fess

item.banner.stripe_downright.black=Black Bend
item.banner.stripe_downright.red=Red Bend
item.banner.stripe_downright.green=Green Bend
item.banner.stripe_downright.brown=Brown Bend
item.banner.stripe_downright.blue=Blue Bend
item.banner.stripe_downright.purple=Purple Bend
item.banner.stripe_downright.cyan=Cyan Bend
item.banner.stripe_downright.silver=Light Gray Bend
item.banner.stripe_downright.gray=Gray Bend
item.banner.stripe_downright.pink=Pink Bend
item.banner.stripe_downright.lime=Lime Bend
item.banner.stripe_downright.yellow=Yellow Bend
item.banner.stripe_downright.lightBlue=Light Blue Bend
item.banner.stripe_downright.magenta=Magenta Bend
item.banner.stripe_downright.orange=Orange Bend
item.banner.stripe_downright.white=White Bend

item.banner.stripe_downleft.black=Black Bend Sinister
item.banner.stripe_downleft.red=Red Bend Sinister
item.banner.stripe_downleft.green=Green Bend Sinister
item.banner.stripe_downleft.brown=Brown Bend Sinister
item.banner.stripe_downleft.blue=Blue Bend Sinister
item.banner.stripe_downleft.purple=Purple Bend Sinister
item.banner.stripe_downleft.cyan=Cyan Bend Sinister
item.banner.stripe_downleft.silver=Light Gray Bend Sinister
item.banner.stripe_downleft.gray=Gray Bend Sinister
item.banner.stripe_downleft.pink=Pink Bend Sinister
item.banner.stripe_downleft.lime=Lime Bend Sinister
item.banner.stripe_downleft.yellow=Yellow Bend Sinister
item.banner.stripe_downleft.lightBlue=Light Blue Bend Sinister
item.banner.stripe_downleft.magenta=Magenta Bend Sinister
item.banner.stripe_downleft.orange=Orange Bend Sinister
item.banner.stripe_downleft.white=White Bend Sinister

item.banner.small_stripes.black=Black Paly
item.banner.small_stripes.red=Red Paly
item.banner.small_stripes.green=Green Paly
item.banner.small_stripes.brown=Brown Paly
item.banner.small_stripes.blue=Blue Paly
item.banner.small_stripes.purple=Purple Paly
item.banner.small_stripes.cyan=Cyan Paly
item.banner.small_stripes.silver=Light Gray Paly
item.banner.small_stripes.gray=Gray Paly
item.banner.small_stripes.pink=Pink Paly
item.banner.small_stripes.lime=Lime Paly
item.banner.small_stripes.yellow=Yellow Paly
item.banner.small_stripes.lightBlue=Light Blue Paly
item.banner.small_stripes.magenta=Magenta Paly
item.banner.small_stripes.orange=Orange Paly
item.banner.small_stripes.white=White Paly

item.banner.cross.black=Black Saltire
item.banner.cross.red=Red Saltire
item.banner.cross.green=Green Saltire
item.banner.cross.brown=Brown Saltire
item.banner.cross.blue=Blue Saltire
item.banner.cross.purple=Purple Saltire
item.banner.cross.cyan=Cyan Saltire
item.banner.cross.silver=Light Gray Saltire
item.banner.cross.gray=Gray Saltire
item.banner.cross.pink=Pink Saltire
item.banner.cross.lime=Lime Saltire
item.banner.cross.yellow=Yellow Saltire
item.banner.cross.lightBlue=Light Blue Saltire
item.banner.cross.magenta=Magenta Saltire
item.banner.cross.orange=Orange Saltire
item.banner.cross.white=White Saltire

item.banner.triangle_bottom.black=Black Chevron
item.banner.triangle_bottom.red=Red Chevron
item.banner.triangle_bottom.green=Green Chevron
item.banner.triangle_bottom.brown=Brown Chevron
item.banner.triangle_bottom.blue=Blue Chevron
item.banner.triangle_bottom.purple=Purple Chevron
item.banner.triangle_bottom.cyan=Cyan Chevron
item.banner.triangle_bottom.silver=Light Gray Chevron
item.banner.triangle_bottom.gray=Gray Chevron
item.banner.triangle_bottom.pink=Pink Chevron
item.banner.triangle_bottom.lime=Lime Chevron
item.banner.triangle_bottom.yellow=Yellow Chevron
item.banner.triangle_bottom.lightBlue=Light Blue Chevron
item.banner.triangle_bottom.magenta=Magenta Chevron
item.banner.triangle_bottom.orange=Orange Chevron
item.banner.triangle_bottom.white=White Chevron

item.banner.triangle_top.black=Black Inverted Chevron
item.banner.triangle_top.red=Red Inverted Chevron
item.banner.triangle_top.green=Green Inverted Chevron
item.banner.triangle_top.brown=Brown Inverted Chevron
item.banner.triangle_top.blue=Blue Inverted Chevron
item.banner.triangle_top.purple=Purple Inverted Chevron
item.banner.triangle_top.cyan=Cyan Inverted Chevron
item.banner.triangle_top.silver=Light Gray Inverted Chevron
item.banner.triangle_top.gray=Gray Inverted Chevron
item.banner.triangle_top.pink=Pink Inverted Chevron
item.banner.triangle_top.lime=Lime Inverted Chevron
item.banner.triangle_top.yellow=Yellow Inverted Chevron
item.banner.triangle_top.lightBlue=Light Blue Inverted Chevron
item.banner.triangle_top.magenta=Magenta Inverted Chevron
item.banner.triangle_top.orange=Orange Inverted Chevron
item.banner.triangle_top.white=White Inverted Chevron

item.banner.triangles_bottom.black=Black Base Indented
item.banner.triangles_bottom.red=Red Base Indented
item.banner.triangles_bottom.green=Green Base Indented
item.banner.triangles_bottom.brown=Brown Base Indented
item.banner.triangles_bottom.blue=Blue Base Indented
item.banner.triangles_bottom.purple=Purple Base Indented
item.banner.triangles_bottom.cyan=Cyan Base Indented
item.banner.triangles_bottom.silver=Light Gray Base Indented
item.banner.triangles_bottom.gray=Gray Base Indented
item.banner.triangles_bottom.pink=Pink Base Indented
item.banner.triangles_bottom.lime=Lime Base Indented
item.banner.triangles_bottom.yellow=Yellow Base Indented
item.banner.triangles_bottom.lightBlue=Light Blue Base Indented
item.banner.triangles_bottom.magenta=Magenta Base Indented
item.banner.triangles_bottom.orange=Orange Base Indented
item.banner.triangles_bottom.white=White Base Indented

item.banner.triangles_top.black=Black Chief Indented
item.banner.triangles_top.red=Red Chief Indented
item.banner.triangles_top.green=Green Chief Indented
item.banner.triangles_top.brown=Brown Chief Indented
item.banner.triangles_top.blue=Blue Chief Indented
item.banner.triangles_top.purple=Purple Chief Indented
item.banner.triangles_top.cyan=Cyan Chief Indented
item.banner.triangles_top.silver=Light Gray Chief Indented
item.banner.triangles_top.gray=Gray Chief Indented
item.banner.triangles_top.pink=Pink Chief Indented
item.banner.triangles_top.lime=Lime Chief Indented
item.banner.triangles_top.yellow=Yellow Chief Indented
item.banner.triangles_top.lightBlue=Light Blue Chief Indented
item.banner.triangles_top.magenta=Magenta Chief Indented
item.banner.triangles_top.orange=Orange Chief Indented
item.banner.triangles_top.white=White Chief Indented

item.banner.diagonal_left.black=Black Per Bend Sinister
item.banner.diagonal_left.red=Red Per Bend Sinister
item.banner.diagonal_left.green=Green Per Bend Sinister
item.banner.diagonal_left.brown=Brown Per Bend Sinister
item.banner.diagonal_left.blue=Blue Per Bend Sinister
item.banner.diagonal_left.purple=Purple Per Bend Sinister
item.banner.diagonal_left.cyan=Cyan Per Bend Sinister
item.banner.diagonal_left.silver=Light Gray Per Bend Sinister
item.banner.diagonal_left.gray=Gray Per Bend Sinister
item.banner.diagonal_left.pink=Pink Per Bend Sinister
item.banner.diagonal_left.lime=Lime Per Bend Sinister
item.banner.diagonal_left.yellow=Yellow Per Bend Sinister
item.banner.diagonal_left.lightBlue=Light Blue Per Bend Sinister
item.banner.diagonal_left.magenta=Magenta Per Bend Sinister
item.banner.diagonal_left.orange=Orange Per Bend Sinister
item.banner.diagonal_left.white=White Per Bend Sinister

item.banner.diagonal_right.black=Black Per Bend
item.banner.diagonal_right.red=Red Per Bend
item.banner.diagonal_right.green=Green Per Bend
item.banner.diagonal_right.brown=Brown Per Bend
item.banner.diagonal_right.blue=Blue Per Bend
item.banner.diagonal_right.purple=Purple Per Bend
item.banner.diagonal_right.cyan=Cyan Per Bend
item.banner.diagonal_right.silver=Light Gray Per Bend
item.banner.diagonal_right.gray=Gray Per Bend
item.banner.diagonal_right.pink=Pink Per Bend
item.banner.diagonal_right.lime=Lime Per Bend
item.banner.diagonal_right.yellow=Yellow Per Bend
item.banner.diagonal_right.lightBlue=Light Blue Per Bend
item.banner.diagonal_right.magenta=Magenta Per Bend
item.banner.diagonal_right.orange=Orange Per Bend
item.banner.diagonal_right.white=White Per Bend

item.banner.diagonal_up_left.black=Black Per Bend Inverted
item.banner.diagonal_up_left.red=Red Per Bend Inverted
item.banner.diagonal_up_left.green=Green Per Bend Inverted
item.banner.diagonal_up_left.brown=Brown Per Bend Inverted
item.banner.diagonal_up_left.blue=Blue Per Bend Inverted
item.banner.diagonal_up_left.purple=Purple Per Bend Inverted
item.banner.diagonal_up_left.cyan=Cyan Per Bend Inverted
item.banner.diagonal_up_left.silver=Light Gray Per Bend Inverted
item.banner.diagonal_up_left.gray=Gray Per Bend Inverted
item.banner.diagonal_up_left.pink=Pink Per Bend Inverted
item.banner.diagonal_up_left.lime=Lime Per Bend Inverted
item.banner.diagonal_up_left.yellow=Yellow Per Bend Inverted
item.banner.diagonal_up_left.lightBlue=Light Blue Per Bend Inverted
item.banner.diagonal_up_left.magenta=Magenta Per Bend Inverted
item.banner.diagonal_up_left.orange=Orange Per Bend Inverted
item.banner.diagonal_up_left.white=White Per Bend Inverted

item.banner.diagonal_up_right.black=Black Per Bend Sinister Inverted
item.banner.diagonal_up_right.red=Red Per Bend Sinister Inverted
item.banner.diagonal_up_right.green=Green Per Bend Sinister Inverted
item.banner.diagonal_up_right.brown=Brown Per Bend Sinister Inverted
item.banner.diagonal_up_right.blue=Blue Per Bend Sinister Inverted
item.banner.diagonal_up_right.purple=Purple Per Bend Sinister Inverted
item.banner.diagonal_up_right.cyan=Cyan Per Bend Sinister Inverted
item.banner.diagonal_up_right.silver=Light Gray Per Bend Sinister Inverted
item.banner.diagonal_up_right.gray=Gray Per Bend Sinister Inverted
item.banner.diagonal_up_right.pink=Pink Per Bend Sinister Inverted
item.banner.diagonal_up_right.lime=Lime Per Bend Sinister Inverted
item.banner.diagonal_up_right.yellow=Yellow Per Bend Sinister Inverted
item.banner.diagonal_up_right.lightBlue=Light Blue Per Bend Sinister Inverted
item.banner.diagonal_up_right.magenta=Magenta Per Bend Sinister Inverted
item.banner.diagonal_up_right.orange=Orange Per Bend Sinister Inverted
item.banner.diagonal_up_right.white=White Per Bend Sinister Inverted

item.banner.circle.black=Black Roundel
item.banner.circle.red=Red Roundel
item.banner.circle.green=Green Roundel
item.banner.circle.brown=Brown Roundel
item.banner.circle.blue=Blue Roundel
item.banner.circle.purple=Purple Roundel
item.banner.circle.cyan=Cyan Roundel
item.banner.circle.silver=Light Gray Roundel
item.banner.circle.gray=Gray Roundel
item.banner.circle.pink=Pink Roundel
item.banner.circle.lime=Lime Roundel
item.banner.circle.yellow=Yellow Roundel
item.banner.circle.lightBlue=Light Blue Roundel
item.banner.circle.magenta=Magenta Roundel
item.banner.circle.orange=Orange Roundel
item.banner.circle.white=White Roundel

item.banner.rhombus.black=Black Lozenge
item.banner.rhombus.red=Red Lozenge
item.banner.rhombus.green=Green Lozenge
item.banner.rhombus.brown=Brown Lozenge
item.banner.rhombus.blue=Blue Lozenge
item.banner.rhombus.purple=Purple Lozenge
item.banner.rhombus.cyan=Cyan Lozenge
item.banner.rhombus.silver=Light Gray Lozenge
item.banner.rhombus.gray=Gray Lozenge
item.banner.rhombus.pink=Pink Lozenge
item.banner.rhombus.lime=Lime Lozenge
item.banner.rhombus.yellow=Yellow Lozenge
item.banner.rhombus.lightBlue=Light Blue Lozenge
item.banner.rhombus.magenta=Magenta Lozenge
item.banner.rhombus.orange=Orange Lozenge
item.banner.rhombus.white=White Lozenge

item.banner.half_vertical.black=Black Per Pale
item.banner.half_vertical.red=Red Per Pale
item.banner.half_vertical.green=Green Per Pale
item.banner.half_vertical.brown=Brown Per Pale
item.banner.half_vertical.blue=Blue Per Pale
item.banner.half_vertical.purple=Purple Per Pale
item.banner.half_vertical.cyan=Cyan Per Pale
item.banner.half_vertical.silver=Light Gray Per Pale
item.banner.half_vertical.gray=Gray Per Pale
item.banner.half_vertical.pink=Pink Per Pale
item.banner.half_vertical.lime=Lime Per Pale
item.banner.half_vertical.yellow=Yellow Per Pale
item.banner.half_vertical.lightBlue=Light Blue Per Pale
item.banner.half_vertical.magenta=Magenta Per Pale
item.banner.half_vertical.orange=Orange Per Pale
item.banner.half_vertical.white=White Per Pale

item.banner.half_horizontal.black=Black Per Fess
item.banner.half_horizontal.red=Red Per Fess
item.banner.half_horizontal.green=Green Per Fess
item.banner.half_horizontal.brown=Brown Per Fess
item.banner.half_horizontal.blue=Blue Per Fess
item.banner.half_horizontal.purple=Purple Per Fess
item.banner.half_horizontal.cyan=Cyan Per Fess
item.banner.half_horizontal.silver=Light Gray Per Fess
item.banner.half_horizontal.gray=Gray Per Fess
item.banner.half_horizontal.pink=Pink Per Fess
item.banner.half_horizontal.lime=Lime Per Fess
item.banner.half_horizontal.yellow=Yellow Per Fess
item.banner.half_horizontal.lightBlue=Light Blue Per Fess
item.banner.half_horizontal.magenta=Magenta Per Fess
item.banner.half_horizontal.orange=Orange Per Fess
item.banner.half_horizontal.white=White Per Fess

item.banner.half_vertical_right.black=Black Per Pale Inverted
item.banner.half_vertical_right.red=Red Per Pale Inverted
item.banner.half_vertical_right.green=Green Per Pale Inverted
item.banner.half_vertical_right.brown=Brown Per Pale Inverted
item.banner.half_vertical_right.blue=Blue Per Pale Inverted
item.banner.half_vertical_right.purple=Purple Per Pale Inverted
item.banner.half_vertical_right.cyan=Cyan Per Pale Inverted
item.banner.half_vertical_right.silver=Light Gray Per Pale Inverted
item.banner.half_vertical_right.gray=Gray Per Pale Inverted
item.banner.half_vertical_right.pink=Pink Per Pale Inverted
item.banner.half_vertical_right.lime=Lime Per Pale Inverted
item.banner.half_vertical_right.yellow=Yellow Per Pale Inverted
item.banner.half_vertical_right.lightBlue=Light Blue Per Pale Inverted
item.banner.half_vertical_right.magenta=Magenta Per Pale Inverted
item.banner.half_vertical_right.orange=Orange Per Pale Inverted
item.banner.half_vertical_right.white=White Per Pale Inverted

item.banner.half_horizontal_bottom.black=Black Per Fess Inverted
item.banner.half_horizontal_bottom.red=Red Per Fess Inverted
item.banner.half_horizontal_bottom.green=Green Per Fess Inverted
item.banner.half_horizontal_bottom.brown=Brown Per Fess Inverted
item.banner.half_horizontal_bottom.blue=Blue Per Fess Inverted
item.banner.half_horizontal_bottom.purple=Purple Per Fess Inverted
item.banner.half_horizontal_bottom.cyan=Cyan Per Fess Inverted
item.banner.half_horizontal_bottom.silver=Light Gray Per Fess Inverted
item.banner.half_horizontal_bottom.gray=Gray Per Fess Inverted
item.banner.half_horizontal_bottom.pink=Pink Per Fess Inverted
item.banner.half_horizontal_bottom.lime=Lime Per Fess Inverted
item.banner.half_horizontal_bottom.yellow=Yellow Per Fess Inverted
item.banner.half_horizontal_bottom.lightBlue=Light Blue Per Fess Inverted
item.banner.half_horizontal_bottom.magenta=Magenta Per Fess Inverted
item.banner.half_horizontal_bottom.orange=Orange Per Fess Inverted
item.banner.half_horizontal_bottom.white=White Per Fess Inverted

item.banner.creeper.black=Black Creeper Charge
item.banner.creeper.red=Red Creeper Charge
item.banner.creeper.green=Green Creeper Charge
item.banner.creeper.brown=Brown Creeper Charge
item.banner.creeper.blue=Blue Creeper Charge
item.banner.creeper.purple=Purple Creeper Charge
item.banner.creeper.cyan=Cyan Creeper Charge
item.banner.creeper.silver=Light Gray Creeper Charge
item.banner.creeper.gray=Gray Creeper Charge
item.banner.creeper.pink=Pink Creeper Charge
item.banner.creeper.lime=Lime Creeper Charge
item.banner.creeper.yellow=Yellow Creeper Charge
item.banner.creeper.lightBlue=Light Blue Creeper Charge
item.banner.creeper.magenta=Magenta Creeper Charge
item.banner.creeper.orange=Orange Creeper Charge
item.banner.creeper.white=White Creeper Charge

item.banner.bricks.black=Black Field Masoned
item.banner.bricks.red=Red Field Masoned
item.banner.bricks.green=Green Field Masoned
item.banner.bricks.brown=Brown Field Masoned
item.banner.bricks.blue=Blue Field Masoned
item.banner.bricks.purple=Purple Field Masoned
item.banner.bricks.cyan=Cyan Field Masoned
item.banner.bricks.silver=Light Gray Field Masoned
item.banner.bricks.gray=Gray Field Masoned
item.banner.bricks.pink=Pink Field Masoned
item.banner.bricks.lime=Lime Field Masoned
item.banner.bricks.yellow=Yellow Field Masoned
item.banner.bricks.lightBlue=Light Blue Field Masoned
item.banner.bricks.magenta=Magenta Field Masoned
item.banner.bricks.orange=Orange Field Masoned
item.banner.bricks.white=White Field Masoned

item.banner.gradient.black=Black Gradient
item.banner.gradient.red=Red Gradient
item.banner.gradient.green=Green Gradient
item.banner.gradient.brown=Brown Gradient
item.banner.gradient.blue=Blue Gradient
item.banner.gradient.purple=Purple Gradient
item.banner.gradient.cyan=Cyan Gradient
item.banner.gradient.silver=Light Gray Gradient
item.banner.gradient.gray=Gray Gradient
item.banner.gradient.pink=Pink Gradient
item.banner.gradient.lime=Lime Gradient
item.banner.gradient.yellow=Yellow Gradient
item.banner.gradient.lightBlue=Light Blue Gradient
item.banner.gradient.magenta=Magenta Gradient
item.banner.gradient.orange=Orange Gradient
item.banner.gradient.white=White Gradient

item.banner.gradient_up.black=Black Base Gradient
item.banner.gradient_up.red=Red Base Gradient
item.banner.gradient_up.green=Green Base Gradient
item.banner.gradient_up.brown=Brown Base Gradient
item.banner.gradient_up.blue=Blue Base Gradient
item.banner.gradient_up.purple=Purple Base Gradient
item.banner.gradient_up.cyan=Cyan Base Gradient
item.banner.gradient_up.silver=Light Gray Base Gradient
item.banner.gradient_up.gray=Gray Base Gradient
item.banner.gradient_up.pink=Pink Base Gradient
item.banner.gradient_up.lime=Lime Base Gradient
item.banner.gradient_up.yellow=Yellow Base Gradient
item.banner.gradient_up.lightBlue=Light Blue Base Gradient
item.banner.gradient_up.magenta=Magenta Base Gradient
item.banner.gradient_up.orange=Orange Base Gradient
item.banner.gradient_up.white=White Base Gradient

item.banner.skull.black=Black Skull Charge
item.banner.skull.red=Red Skull Charge
item.banner.skull.green=Green Skull Charge
item.banner.skull.brown=Brown Skull Charge
item.banner.skull.blue=Blue Skull Charge
item.banner.skull.purple=Purple Skull Charge
item.banner.skull.cyan=Cyan Skull Charge
item.banner.skull.silver=Light Gray Skull Charge
item.banner.skull.gray=Gray Skull Charge
item.banner.skull.pink=Pink Skull Charge
item.banner.skull.lime=Lime Skull Charge
item.banner.skull.yellow=Yellow Skull Charge
item.banner.skull.lightBlue=Light Blue Skull Charge
item.banner.skull.magenta=Magenta Skull Charge
item.banner.skull.orange=Orange Skull Charge
item.banner.skull.white=White Skull Charge

item.banner.flower.black=Black Flower Charge
item.banner.flower.red=Red Flower Charge
item.banner.flower.green=Green Flower Charge
item.banner.flower.brown=Brown Flower Charge
item.banner.flower.blue=Blue Flower Charge
item.banner.flower.purple=Purple Flower Charge
item.banner.flower.cyan=Cyan Flower Charge
item.banner.flower.silver=Light Gray Flower Charge
item.banner.flower.gray=Gray Flower Charge
item.banner.flower.pink=Pink Flower Charge
item.banner.flower.lime=Lime Flower Charge
item.banner.flower.yellow=Yellow Flower Charge
item.banner.flower.lightBlue=Light Blue Flower Charge
item.banner.flower.magenta=Magenta Flower Charge
item.banner.flower.orange=Orange Flower Charge
item.banner.flower.white=White Flower Charge

item.banner.border.black=Black Bordure
item.banner.border.red=Red Bordure
item.banner.border.green=Green Bordure
item.banner.border.brown=Brown Bordure
item.banner.border.blue=Blue Bordure
item.banner.border.purple=Purple Bordure
item.banner.border.cyan=Cyan Bordure
item.banner.border.silver=Light Gray Bordure
item.banner.border.gray=Gray Bordure
item.banner.border.pink=Pink Bordure
item.banner.border.lime=Lime Bordure
item.banner.border.yellow=Yellow Bordure
item.banner.border.lightBlue=Light Blue Bordure
item.banner.border.magenta=Magenta Bordure
item.banner.border.orange=Orange Bordure
item.banner.border.white=White Bordure

item.banner.curly_border.black=Black Bordure Indented
item.banner.curly_border.red=Red Bordure Indented
item.banner.curly_border.green=Green Bordure Indented
item.banner.curly_border.brown=Brown Bordure Indented
item.banner.curly_border.blue=Blue Bordure Indented
item.banner.curly_border.purple=Purple Bordure Indented
item.banner.curly_border.cyan=Cyan Bordure Indented
item.banner.curly_border.silver=Light Gray Bordure Indented
item.banner.curly_border.gray=Gray Bordure Indented
item.banner.curly_border.pink=Pink Bordure Indented
item.banner.curly_border.lime=Lime Bordure Indented
item.banner.curly_border.yellow=Yellow Bordure Indented
item.banner.curly_border.lightBlue=Light Blue Bordure Indented
item.banner.curly_border.magenta=Magenta Bordure Indented
item.banner.curly_border.orange=Orange Bordure Indented
item.banner.curly_border.white=White Bordure Indented

item.banner.mojang.black=Black Thing
item.banner.mojang.red=Red Thing
item.banner.mojang.green=Green Thing
item.banner.mojang.brown=Brown Thing
item.banner.mojang.blue=Blue Thing
item.banner.mojang.purple=Purple Thing
item.banner.mojang.cyan=Cyan Thing
item.banner.mojang.silver=Light Gray Thing
item.banner.mojang.gray=Gray Thing
item.banner.mojang.pink=Pink Thing
item.banner.mojang.lime=Lime Thing
item.banner.mojang.yellow=Yellow Thing
item.banner.mojang.lightBlue=Light Blue Thing
item.banner.mojang.magenta=Magenta Thing
item.banner.mojang.orange=Orange Thing
item.banner.mojang.white=White Thing

item.banner.straight_cross.black=Black Cross
item.banner.straight_cross.red=Red Cross
item.banner.straight_cross.green=Green Cross
item.banner.straight_cross.brown=Brown Cross
item.banner.straight_cross.blue=Blue Cross
item.banner.straight_cross.purple=Purple Cross
item.banner.straight_cross.cyan=Cyan Cross
item.banner.straight_cross.silver=Light Gray Cross
item.banner.straight_cross.gray=Gray Cross
item.banner.straight_cross.pink=Pink Cross
item.banner.straight_cross.lime=Lime Cross
item.banner.straight_cross.yellow=Yellow Cross
item.banner.straight_cross.lightBlue=Light Blue Cross
item.banner.straight_cross.magenta=Magenta Cross
item.banner.straight_cross.orange=Orange Cross
item.banner.straight_cross.white=White Cross
