package net.minecraft.client.input

import de.florianmichael.viamcp.fixes.AttackOrder
import net.bloom.bloomclient.features.component.components.patcher.ZoomComponent
import net.bloom.bloomclient.features.module.modules.combat.ModuleAutoClicker
import net.bloom.bloomclient.features.module.modules.combat.ModuleNoHitDelay
import net.bloom.bloomclient.features.module.modules.other.ModulePatcher
import net.bloom.bloomclient.features.module.modules.other.ModuleZoom
import net.bloom.bloomclient.features.module.modules.world.ModuleFastPlace
import net.minecraft.block.Block
import net.minecraft.block.material.Material
import net.minecraft.client.Minecraft
import net.minecraft.client.MinecraftInstance
import net.minecraft.client.gui.GuiScreen
import net.minecraft.client.settings.KeyBinding
import net.minecraft.entity.EntityLeashKnot
import net.minecraft.entity.EntityList
import net.minecraft.entity.item.*
import net.minecraft.entity.item.EntityMinecart.EnumMinecartType
import net.minecraft.init.Items
import net.minecraft.item.Item
import net.minecraft.item.ItemBlock
import net.minecraft.tileentity.TileEntity
import net.minecraft.util.MovingObjectPosition.MovingObjectType
import org.lwjgl.input.Mouse

object MouseHandler: MinecraftInstance() {

	fun startHandlingMouseInput() {
        mc.thePlayer ?: return

        val currentScreen = mc.currentScreen

        if (currentScreen == null || currentScreen.allowUserInput) {
            mc.mcProfiler.endStartSection("mouse")

            handleMouseClicks()
            while (Mouse.next())
            	handleMouseScroll()
        }
    }

    private fun handleMouseScroll() {
    	mc.thePlayer ?: return

        val mouseButton = Mouse.getEventButton()
        val mouseButtonState = Mouse.getEventButtonState()

    	KeyBinding.setKeyBindState(mouseButton - 100, mouseButtonState)

        if (mouseButtonState) {
        	if (mc.thePlayer.isSpectator && mouseButton == 2)
        		mc.ingameGUI.spectatorGui.onMiddleClick()
        	else
        		KeyBinding.onTick(mouseButton - 100)
        }

        if (mc.systemTime - mc.systemTime <= 200L) {
            var mouseWheelDelta = Mouse.getEventDWheel()

            if (mouseWheelDelta == 0)
            	return

            if (mc.thePlayer.isSpectator) {
            	mouseWheelDelta = if (mouseWheelDelta < 0) -1 else 1

                if (mc.ingameGUI.spectatorGui.isMenuActive) {
                    mc.ingameGUI.spectatorGui.onMouseScroll(-mouseWheelDelta)
                } else {
                    val flySpeedIncrement = mouseWheelDelta.toFloat() * 0.005f

                    mc.thePlayer.capabilities?.let { it ->
                        val newFlySpeed = (it.flySpeed + flySpeedIncrement).coerceIn(0.0f, 0.2f)
                        it.flySpeed = newFlySpeed
                    }
                }
            } else {
                if (ModulePatcher.state && ModulePatcher.disableHotbarScrolling.get() || ModuleZoom.state && ModuleZoom.scrollToZoom.get() && ZoomComponent.zoomed)
                    return

                mc.thePlayer.inventory?.changeCurrentItem(mouseWheelDelta)
            }
        }
    }

    private fun handleMouseClicks() {
        if (mc.thePlayer.isUsingItem) {
            if (!mc.gameSettings.keyBindUseItem.isKeyDown) {
                mc.playerController.onStoppedUsingItem(mc.thePlayer)
            }

            while (mc.gameSettings.keyBindAttack.isPressed) {
                // Empty loop to simulate holding the key
            }

            while (mc.gameSettings.keyBindUseItem.isPressed) {
                // Empty loop to simulate holding the key
            }

            while (mc.gameSettings.keyBindPickBlock.isPressed) {
                // Empty loop to simulate holding the key
            }
        } else {
            while (mc.gameSettings.keyBindAttack.isPressed) {
                clickMouse()
            }

            while (mc.gameSettings.keyBindUseItem.isPressed) {
                rightClickMouse()
            }

            while (mc.gameSettings.keyBindPickBlock.isPressed) {
                middleClickMouse()
            }
        }

        if (mc.gameSettings.keyBindUseItem.isKeyDown && mc.rightClickDelayTimer == 0 && !mc.thePlayer.isUsingItem) {
            rightClickMouse()
        }

        mc.sendClickBlockToController(mc.currentScreen == null && mc.gameSettings.keyBindAttack.isKeyDown && mc.inGameHasFocus)

    }

    fun clickMouse() {
        if (mc.leftClickCounter > 0)
            return

        AttackOrder.sendConditionalSwing(mc.objectMouseOver)

        val objectMouse = mc.objectMouseOver ?: run {
            Minecraft.logger.warn("An issue occurred while performing the left click action.")
            if (mc.playerController.isNotCreative)
                mc.leftClickCounter = 10
            return
        }

        when (objectMouse.typeOfHit) {
            MovingObjectType.ENTITY -> AttackOrder.sendFixedAttack(mc.thePlayer, objectMouse.entityHit, true)
            MovingObjectType.BLOCK -> {
                val blockpos = objectMouse.blockPos
                if (mc.theWorld.getBlockState(blockpos).block.material != Material.air)
                    mc.playerController.clickBlock(blockpos, objectMouse.sideHit)
            }
            else -> {
                if (ModuleNoHitDelay.state || ModuleAutoClicker.state && ModuleAutoClicker.shouldLeftClick)
                    return

                if (mc.playerController.isNotCreative) {
                    mc.leftClickCounter = 10
                }

            }
        }
    }

    fun rightClickMouse() {
        mc.thePlayer ?: return

        if (mc.playerController.isHittingBlock)
            return

        var isActionPerformed = true
        val currentItem = mc.thePlayer.inventory.currentItemStack
        val objectMouse = mc.objectMouseOver

        if (ModuleFastPlace.canFastPlace()) {
            mc.rightClickDelayTimer = ModuleFastPlace.speedValue.get()
        } else {
            mc.rightClickDelayTimer = 4
        }

        if (objectMouse == null) {
            Minecraft.logger.warn("Null returned as 'hitResult', this shouldn't happen!")
        } else when (objectMouse.typeOfHit) {
            MovingObjectType.ENTITY -> {
                if (mc.playerController.isPlayerRightClickingOnEntity(mc.thePlayer, objectMouse.entityHit, objectMouse)){
                    isActionPerformed = false
                } else if (mc.playerController.interactWithEntitySendPacket(mc.thePlayer, objectMouse.entityHit)){
                    isActionPerformed = false
                }
            }

            MovingObjectType.BLOCK -> {
                val blockPos = objectMouse.blockPos
                if (!mc.theWorld.getBlockState(blockPos).block.material.equals(Material.air)) {
                    val initialStackSize = currentItem?.stackSize ?: 0

                    if (mc.playerController.onPlayerRightClick(mc.thePlayer, mc.theWorld, currentItem, blockPos, objectMouse.sideHit, objectMouse.hitVec)) {
                        isActionPerformed = false
                        mc.thePlayer.swingItem()
                    }

                    if (currentItem != null) {
                        if (currentItem.stackSize == 0) {
                            mc.thePlayer.inventory.mainInventory[mc.thePlayer.inventory.currentItem] = null
                        } else if (currentItem.stackSize != initialStackSize) {
                            mc.entityRenderer.itemRenderer.resetEquippedProgress()
                        }
                    }
                }
            }

            else -> {}
        }

        if (isActionPerformed && currentItem != null && mc.playerController.sendUseItem(mc.thePlayer, mc.theWorld, currentItem)) {
            mc.entityRenderer.itemRenderer.resetEquippedProgress2()
        }
    }

    fun middleClickMouse() {
        val mouseObj = mc.objectMouseOver ?: return
        val isCreativeMode = mc.thePlayer.capabilities.isCreativeMode
        var i = 0
        var isMetaSpecific = false
        var tileentity: TileEntity? = null
        val item: Item

        if (mouseObj.typeOfHit == MovingObjectType.BLOCK) {
            val blockpos = mouseObj.blockPos
            val block = mc.theWorld.getBlockState(blockpos).block

            if (block.material == Material.air)
                return

            item = block.getItem(mc.theWorld, blockpos) ?: return

            if (isCreativeMode && GuiScreen.isCtrlKeyDown())
                tileentity = mc.theWorld.getTileEntity(blockpos)

            val block1 = if (item is ItemBlock && !block.isFlowerPot) Block.getBlockFromItem(item) else block
            i = block1.getDamageValue(mc.theWorld, blockpos)
            isMetaSpecific = item.hasSubtypes
        } else {
            if (mouseObj.typeOfHit != MovingObjectType.ENTITY || !isCreativeMode)
                return

            val entityHit = mouseObj.entityHit ?: return

            item = when (entityHit) {
                is EntityPainting -> Items.painting
                is EntityLeashKnot -> Items.lead
                is EntityItemFrame -> {
                    val itemStack = entityHit.displayedItem

                    if (itemStack == null)
                        Items.item_frame
                    else {
                        i = itemStack.metadata
                        isMetaSpecific = true
                        itemStack.item
                    }
                }
                is EntityMinecart -> when (entityHit.minecartType) {
                    EnumMinecartType.FURNACE -> Items.furnace_minecart
                    EnumMinecartType.CHEST -> Items.chest_minecart
                    EnumMinecartType.TNT -> Items.tnt_minecart
                    EnumMinecartType.HOPPER -> Items.hopper_minecart
                    EnumMinecartType.COMMAND_BLOCK -> Items.command_block_minecart
                    else -> Items.minecart
                }
                is EntityBoat -> Items.boat
                is EntityArmorStand -> Items.armor_stand
                else -> {
                    i = EntityList.getEntityID(entityHit)

                    EntityList.entityEggs[i] ?: return
                    Items.spawn_egg
                }
            }
        }

        if (tileentity == null)
            mc.thePlayer.inventory.setCurrentItem(item, i, isMetaSpecific, isCreativeMode)
        else {
            val itemstack1 = mc.pickBlockWithNBT(item, i, tileentity)
            mc.thePlayer.inventory.setInventorySlotContents(mc.thePlayer.inventory.currentItem, itemstack1)
        }

        if (isCreativeMode) {
            val j = mc.thePlayer.inventoryContainer.inventorySlots.size - 9 + mc.thePlayer.inventory.currentItem
            mc.playerController.sendSlotPacket(mc.thePlayer.inventory.getStackInSlot(mc.thePlayer.inventory.currentItem), j)
        }
    }


}