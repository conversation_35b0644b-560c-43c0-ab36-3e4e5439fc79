package net.minecraft.client.gui

import java.awt.Color

interface IFontRenderer {

    fun drawString(text: String?, x: Float, y: Float, color: Int): Int

    fun drawString(text: String?, x: Int, y: Int, color: Int): Int

    fun drawString(text: String?, x: Float, y: Float, color: Int, dropShadow: Boolean): Int

    fun drawString(text: String?, x: Float, y: Float, color: Int, side: Side): Int

    fun drawString(text: String?, x: Float, y: Float, color: Int, side: Side, dropShadow: Boolean): Int

    fun drawCenteredString(text: String?, x: Float, y: Float, color: Int): Int

    fun drawCenteredString(text: String?, x: Float, y: Float, color: Int, dropShadow: Boolean): Int

    fun drawStringWithShadow(text: String?, x: Float, y: Float, color: Int): Int

    fun getStringWidth(text: String?): Int

    fun drawString(text: String?, x: Float, y: Float, color: Color): Int = drawString(text, x, y, color.rgb)

    fun drawString(text: String?, x: Int, y: Int, color: Color): Int = drawString(text, x, y, color.rgb)

    fun drawString(text: String?, x: Float, y: Float, color: Color, dropShadow: Boolean): Int = drawString(text, x, y, color.rgb, dropShadow)

    fun drawString(text: String?, x: Float, y: Float, color: Color, side: Side): Int = drawString(text, x, y, color.rgb, side)

    fun drawString(text: String?, x: Float, y: Float, color: Color, side: Side, dropShadow: Boolean): Int = drawString(text, x, y, color.rgb, side, dropShadow)

    fun drawCenteredString(text: String?, x: Float, y: Float, color: Color): Int = drawCenteredString(text, x, y, color.rgb)

    fun drawCenteredString(text: String?, x: Float, y: Float, color: Color, dropShadow: Boolean): Int = drawCenteredString(text, x, y, color.rgb, dropShadow)

    fun drawStringWithShadow(text: String?, x: Float, y: Float, color: Color): Int = drawStringWithShadow(text, x, y, color.rgb)

}