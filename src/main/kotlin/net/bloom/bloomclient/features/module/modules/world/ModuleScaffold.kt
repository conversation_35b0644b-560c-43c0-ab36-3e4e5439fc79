package net.bloom.bloomclient.features.module.modules.world

import net.bloom.bloomclient.event.*
import net.bloom.bloomclient.features.component.components.player.MovementCorrection
import net.bloom.bloomclient.features.component.components.player.RotationComponent.setRotation
import net.bloom.bloomclient.features.module.Module
import net.bloom.bloomclient.features.module.ModuleCategory
import net.bloom.bloomclient.features.module.modules.world.scaffold.adstrafe.ADStrafeAlways
import net.bloom.bloomclient.features.module.modules.world.scaffold.adstrafe.ADStrafeOnEdge
import net.bloom.bloomclient.features.module.modules.world.scaffold.adstrafe.NoADStrafe
import net.bloom.bloomclient.features.module.modules.world.scaffold.sneaking.NoSneaking
import net.bloom.bloomclient.features.module.modules.world.scaffold.sneaking.NormalSneaking
import net.bloom.bloomclient.features.module.modules.world.scaffold.sneaking.SilentSneaking
import net.bloom.bloomclient.features.module.modules.world.scaffold.sprint.*
import net.bloom.bloomclient.features.module.modules.world.scaffold.techniques.*
import net.bloom.bloomclient.features.module.modules.world.scaffold.tower.*
import net.bloom.bloomclient.features.shared.rotationspeed.HumanizedRotationSpeedMode
import net.bloom.bloomclient.features.shared.rotationspeed.InterpolationSpeedMode
import net.bloom.bloomclient.features.shared.rotationspeed.LinearRotationSpeedMode
import net.bloom.bloomclient.features.shared.smoothrotation.*
import net.bloom.bloomclient.utils.Constants
import net.bloom.bloomclient.utils.extension.plus
import net.bloom.bloomclient.utils.extension.step
import net.bloom.bloomclient.utils.extension.times
import net.bloom.bloomclient.utils.player.InventoryUtils
import net.bloom.bloomclient.utils.player.MovementUtils
import net.bloom.bloomclient.utils.player.MovementUtils.isMoving
import net.bloom.bloomclient.utils.player.RotationUtils
import net.bloom.bloomclient.utils.simulation.SimulatedPlayer
import net.bloom.bloomclient.value.values.*
import net.lenni0451.lambdaevents.EventHandler
import net.minecraft.block.*
import net.minecraft.block.material.Material
import net.minecraft.client.settings.GameSettings
import net.minecraft.init.Blocks
import net.minecraft.util.*
import kotlin.math.cos
import kotlin.math.floor
import kotlin.math.sin


object ModuleScaffold: Module("Scaffold", "Scaffold", "Use huge balls to rolling on mid-air", ModuleCategory.WORLD) {
    private val techniqueMode by ModeValue("Mode", arrayOf(
        NormalTechnique,
        ExpandTechnique,
        NoRotationTechnique,
        TellyTechnique,
        WalkTechnique,
        GodBridgeTechnique,
        PitchMultiplyTechnique
    ), this)

    val towerModes = ModeValue("Tower", arrayOf(
        AirTower,
        MatrixTower,
        NCPTower,
        VanillaTower,
        VulcanTower,
        MMCTower,
        WatchDogTower,
        NormalTower,
        LowHopTower,
        FastJumpTower,
        LegitTower,
        NoTower
    ), this)

    val towerSprint = ListValue("TowerSprint", "Normal", arrayOf("Normal", "MotionY"))

    val towerSprintFix = BoolValue("TowerSprintFix", true) { towerSprint.get().lowercase() == "motiony" }

    val sprintModes = ModeValue("Sprint", arrayOf(
        NormalSprint,
        LegitSprint,
        OmniSprint,
        ToggleSprint,
        OffSprint
    ), this)

    val sneaking = ModeValue("Sneaking", arrayOf(
        SilentSneaking,
        NormalSneaking,
        NoSneaking
    ), this)

    val adStrafe = ModeValue("ADStrafe", arrayOf(
        ADStrafeAlways,
        ADStrafeOnEdge,
        NoADStrafe
    ), this)

    //Rotation
    private val rotationYawSpeed = FloatRangeValue("YawSpeed", 60F, 60f, 0F, 180f)
    private val rotationPitchSpeed = FloatRangeValue("PitchSpeed", 60F, 60f, 0F, 180f)
    private val gcdFix = BoolValue("SensitivityFixed", true)
    private val sensitivity = FloatValue("Sensitivity", 0.5f, 0f, 1f)

    private val rotationSpeedMode by ModeValue("RotationSpeedMode", arrayOf(
        LinearRotationSpeedMode(),
        HumanizedRotationSpeedMode(),
        InterpolationSpeedMode()
    ), this)

    private val smoothRotationMode by ModeValue("SmoothRotationMode", arrayOf(
        LinearSmoothRotationMode(),
        QuadSmoothRotationMode(),
        SineSmoothRotationMode(),
        QuadSineSmoothRotationMode()
    ), this)

    private val smoothTimingMode = ListValue("SmoothTimingMode", arrayOf("Angle", "Time"))
    private val resetTicks by IntegerValue("ResetTicks", 0, 0, 20)

    private val sameY by ListValue("SameY", "Off", arrayOf("Off", "Same", "Jump"))
    private val timer = FloatValue("Timer", 1f, 0.1f, 5f)
    private val safeWalk = BoolValue("SafeWalk", false)
    private val jumpIfPlayerFallInNextTick = BoolValue("JumpIfPlayerFallInNextTick", false)
    private val movementCorrection = BoolValue("MovementCorrection", true)

    var blockPos = BlockPos.ORIGIN
    var enumFacing = EnumFacing.UP
    var startY = 0.0
    var targetYaw = 0f
    var targetPitch = 0f
    var placedBlocksWithoutEagle = 0

    override fun onDisable() {
        mc.gameSettings ?: return

        mc.gameSettings.keyBindUseItem.pressed = false
        mc.gameSettings.keyBindSneak.pressed = GameSettings.isKeyDown(mc.gameSettings.keyBindSneak)
        mc.gameSettings.keyBindSprint.pressed = false
        mc.timer.timerSpeed = 1f
    }

    override fun onEnable() {
        mc.thePlayer ?: return

        startY = floor(mc.thePlayer.posY)
    }

    @EventHandler
    fun onGameLoop(event: GameLoopEvent) {
        mc.timer.timerSpeed = timer.get()
    }

    @EventHandler
    fun onJump(event: JumpEvent){
        if(towerSprint.get().lowercase() == "motiony"){
            event.sprint = false
            event.sprintFixes = towerSprintFix.get()
        }
    }

    fun calculateBlockPos(){
        val player = mc.thePlayer ?: return
        val position = Vec3(mc.thePlayer.posX, mc.thePlayer.posY, mc.thePlayer.posZ)

        when {
            player.onGround || (mc.gameSettings.keyBindJump.isKeyDown && !isMoving) ->
                startY = floor(player.posY)
            player.posY < startY ->
                startY = player.posY
        }

        val playerPos = BlockPos(player)
        val positions = mutableListOf<Vec3>()
        val blockMap = mutableMapOf<Vec3, BlockPos>()

        val searchHeights = if (canSameY) listOf((startY - 1).toInt()) else (playerPos.y - 1 downTo playerPos.y - 5).toList()

        for (y in searchHeights) {
            for (x in playerPos.x + 5 downTo playerPos.x - 5) {
                for (z in playerPos.z + 5 downTo playerPos.z - 5) {
                    val blockPos = BlockPos(x, y, z)

                    val block = mc.theWorld.getBlockState(blockPos).block

                    if (block is BlockLiquid || block is BlockAir ||
                        block is BlockChest || block is BlockFurnace)
                        continue

                    val hitVec = getBestHitFeet(position, blockPos)
                    positions.add(hitVec)
                    blockMap[hitVec] = blockPos
                }
            }
        }

        blockPos = positions
            .minByOrNull { player.getDistanceSq(it.xCoord, it.yCoord, it.zCoord) }
            ?.let { blockMap[it] }
            ?: return
    }

    fun calculateEnumFacing() {
        val player = mc.thePlayer ?: return
        val position = Vec3(mc.thePlayer.posX, mc.thePlayer.posY, mc.thePlayer.posZ)

        // Find best facing direction for placement
        val positions = mutableListOf<Vec3>()
        val facingMap = mutableMapOf<Vec3, EnumFacing>()

        for (facing in EnumFacing.VALUES){
            if(facing == EnumFacing.UP && canSameY)
                continue

            val neighborPos = blockPos.add(facing.directionVec)

            if(neighborPos == BlockPos(mc.thePlayer))
                continue

            val neighborBlock = mc.theWorld.getBlockState(neighborPos).block

            if((neighborBlock.material.isSolid ||
                    !neighborBlock.isTranslucent ||
                    neighborBlock is BlockLadder ||
                    neighborBlock is BlockCarpet ||
                    neighborBlock is BlockSnow ||
                    neighborBlock is BlockSkull) &&
            !neighborBlock.material.isLiquid &&
            neighborBlock !is BlockContainer)
                continue

            val hitVec = getBestHitFeet(position, neighborPos)
            positions.add(hitVec)
            facingMap[hitVec] = facing
        }

        enumFacing = positions
            .minByOrNull { player.getDistanceSq(it.xCoord, it.yCoord, it.zCoord) }
            ?.let { facingMap[it] }
            ?: return
    }

    @EventHandler
    fun onUpdate(event: GameLoopEvent){
        calculateBlockPos()

        calculateEnumFacing()
    }

    @EventHandler
    fun onRotation(event: PlayerRotationEvent){
        mc.thePlayer ?: return

        techniqueMode.calculateRotation(mc.objectMouseOver, blockPos, enumFacing)

        setRotation(
            Rotation(targetYaw, targetPitch),
            minYawSpeed = rotationYawSpeed.minValue,
            maxYawSpeed = rotationYawSpeed.maxValue,
            minPitchSpeed = rotationPitchSpeed.minValue,
            maxPitchSpeed = rotationPitchSpeed.maxValue,
            gcdfix = gcdFix.get(),
            sensitivity = sensitivity.get().toDouble(),
            smoothMode = smoothRotationMode,
            smoothTiming = when(smoothTimingMode.get().lowercase()){
                "time" -> SmoothTimingMode.TIME
                else -> SmoothTimingMode.ANGLE
            },
            speedMode = rotationSpeedMode,
            fixType = if (movementCorrection.get()) MovementCorrection.Type.SILENT else MovementCorrection.Type.NONE,
            silent = true,
            ticks = resetTicks
        )
    }

    @EventHandler
    fun onMouse(event: MouseInputEvent) {
        mc.thePlayer ?: return

        //Switch Slot
        val blockSlot = InventoryUtils.findBlockForScaffoldInHotbar()
        if (blockSlot != null && blockSlot != -1) {
            mc.thePlayer.inventory.currentItem = blockSlot - 36
        }

        when (techniqueMode){
            ExpandTechnique -> {
                val targetBlock = expand(Vec3(mc.thePlayer.posX, mc.thePlayer.posY - 1, mc.thePlayer.posZ))
                val expandPlaceInfo = getBlockFacingOld(targetBlock) ?: return

                tryToPlace(expandPlaceInfo.first, expandPlaceInfo.second, mc.objectMouseOver.hitVec)
            }

            NoRotationTechnique -> {
                tryToPlace(blockPos, enumFacing, mc.objectMouseOver.hitVec)
            }

            else -> if (mc.objectMouseOver.blockPos == blockPos && mc.objectMouseOver.sideHit == enumFacing) {
                tryToPlace(blockPos, enumFacing, mc.objectMouseOver.hitVec)
            }
        }
    }

    fun tryToPlace(blockPos: BlockPos, enumFacing: EnumFacing, vec: Vec3) {
        val currentItem = mc.thePlayer.inventory.currentItemStack
        val hitVec = modifyVec(vec, enumFacing, Vec3(blockPos))

        if (mc.theWorld.getBlockState(blockPos).block.material != Material.air) {
            val initialStackSize = currentItem?.stackSize ?: 0

            if (mc.playerController.onPlayerRightClick(mc.thePlayer, mc.theWorld, currentItem, blockPos, enumFacing, hitVec)) {
                mc.thePlayer.swingItem()
                placedBlocksWithoutEagle++
            }

            if (currentItem != null) {
                if (currentItem.stackSize == 0) {
                    mc.thePlayer.inventory.mainInventory[mc.thePlayer.inventory.currentItem] = null
                } else if (currentItem.stackSize != initialStackSize) {
                    mc.entityRenderer.itemRenderer.resetEquippedProgress()
                }
            }
        }
    }

    @EventHandler
    fun onInput(event: MoveInputEvent) {
        val player = SimulatedPlayer.fromClientPlayer(mc.thePlayer.movementInput, false)
        player.tick()

        val collidingBox = mc.thePlayer.entityBoundingBox.addCoord(mc.thePlayer.motionX, mc.thePlayer.motionY, mc.thePlayer.motionZ).expand(-0.175, 0.0, -0.175)

        if (safeWalk.get() && mc.thePlayer.onGround && mc.theWorld.getCollidingBoundingBoxes(mc.thePlayer, collidingBox).isEmpty())
            event.sneak = true

        if ((techniqueMode == TellyTechnique || sameY == "Jump") && mc.thePlayer.onGround && isMoving || jumpIfPlayerFallInNextTick.get() && !player.onGround)
            event.jump = true
    }

    fun calculateRotationByAim(blockPos: BlockPos, enumFacing: EnumFacing) {
        val rotationList = mutableListOf<Rotation>()

        for (yaw in -180.0..180.0 step 45.0) {
            for (pitch in 45.0..90.0 step 0.1) {
                val rotation = Rotation(yaw.toFloat(), pitch.toFloat())

                val hitBlock = mc.thePlayer.rayTrace(
                    rotation.yaw,
                    rotation.pitch,
                    mc.playerController.blockReachDistance.toDouble(),
                    1.0F
                )

                if (hitBlock.blockPos == blockPos && hitBlock.sideHit == enumFacing)
                    rotationList.add(rotation)
            }
        }

        rotationList.minByOrNull {
            RotationUtils.getRotationDifference(it, mc.thePlayer.rotation)
        }?.let {
            targetYaw = it.yaw
            targetPitch = it.pitch
        }
    }

    fun isObjectMouseOverBlock(facing: EnumFacing, block: BlockPos, rotation: Rotation = mc.thePlayer.rotation): MovingObjectPosition? {
        val eye = mc.thePlayer.eyes
        val look = mc.thePlayer.getVectorForRotation(rotation.pitch, rotation.yaw)
        val vec = eye + look * mc.playerController.blockReachDistance.toDouble()
        val hitBlock = mc.theWorld.rayTraceBlocks(eye, vec, false, false, true)
        if (hitBlock.blockPos == block && hitBlock.sideHit == facing)
            return hitBlock

        return null
    }

    private val canSameY: Boolean
        get() = sameY != "Off" && !mc.gameSettings.keyBindJump.isKeyDown && isMoving

    private fun getBlockFacingOld(pos: BlockPos): Pair<BlockPos, EnumFacing>? {
        val hashMap = HashMap<Vec3, Pair<BlockPos, EnumFacing>>()
        val positions = ArrayList<Vec3>()

        for ((offset, facing) in Constants.BLOCKFACINGS) {
            val checkPos = pos.add(offset.x, offset.y, offset.z)

            if (mc.theWorld.getBlockState(checkPos).block != Blocks.air) {
                val position = Vec3(checkPos)
                positions.add(position)
                hashMap[position] = checkPos to facing
            }
        }

        return hashMap[positions.minByOrNull { mc.thePlayer.getDistance(it.xCoord, it.yCoord, it.zCoord) }]
    }

    fun expand(position: Vec3): BlockPos {
        var blockPos = position

        if (ExpandTechnique.expand.get() > 0 && isMoving) {
            val movingYaw = MathHelper.wrapAngleTo180(mc.thePlayer.playerYaw)
            val direction = MovementUtils.getDirection(movingYaw)
            val expandVector = Vec3(-sin(direction), position.yCoord, cos(direction))
            var bestExpand = 0.0

            for (length in 0..ExpandTechnique.expand.get()){
                val blockPlace = BlockPos(position + Vec3(expandVector.xCoord * length, 0.0, expandVector.zCoord * length))
                val (_, enumFacing) = getBlockFacingOld(blockPlace) ?: continue
                if (enumFacing != EnumFacing.UP) {
                    bestExpand = length.toDouble()
                }
            }

            blockPos = position + Vec3(expandVector.xCoord * bestExpand, 0.0, expandVector.zCoord * bestExpand)
        }

        return BlockPos(blockPos)
    }

    private fun getBestHitFeet(playerPos: Vec3, blockPos: BlockPos): Vec3 {
        val block = mc.theWorld.getBlockState(blockPos).block

        return Vec3(
            playerPos.xCoord.coerceIn(blockPos.x.toDouble(), blockPos.x.toDouble() + block.blockBoundsMaxX),
            playerPos.yCoord.coerceIn(blockPos.y.toDouble(), blockPos.y.toDouble() + block.blockBoundsMaxY),
            playerPos.zCoord.coerceIn(blockPos.z.toDouble(), blockPos.z.toDouble() + block.blockBoundsMaxZ)
        )
    }

    fun modifyVec(original: Vec3, direction: EnumFacing, pos: Vec3): Vec3 {
        val x = original.xCoord
        val y = original.yCoord
        val z = original.zCoord

        val side = direction.opposite

        return when (side.axis ?: return original) {
            EnumFacing.Axis.Y -> Vec3(x, pos.yCoord + side.directionVec.y.coerceAtLeast(0), z)
            EnumFacing.Axis.X -> Vec3(pos.xCoord + side.directionVec.x.coerceAtLeast(0), y, z)
            EnumFacing.Axis.Z -> Vec3(x, y, pos.zCoord + side.directionVec.z.coerceAtLeast(0))
        }
    }
}
