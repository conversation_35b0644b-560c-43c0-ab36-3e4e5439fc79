package net.bloom.bloomclient.features.module.modules.render

import net.bloom.bloomclient.features.module.Module
import net.bloom.bloomclient.features.module.ModuleCategory
import net.bloom.bloomclient.ui.hud.designer.GuiHUDDesigner

object ModuleHUDDesigner: Module(name = "HUDDegisner", category = ModuleCategory.RENDER, onlyEnable = true) {

    override fun onEnable() {
        mc.displayGuiScreen(GuiHUDDesigner)
    }

}