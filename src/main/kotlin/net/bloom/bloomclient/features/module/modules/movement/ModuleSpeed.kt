package net.bloom.bloomclient.features.module.modules.movement

import net.bloom.bloomclient.features.module.Module
import net.bloom.bloomclient.features.module.ModuleCategory
import net.bloom.bloomclient.features.module.modules.movement.speed.GrimCollideSpeedMode
import net.bloom.bloomclient.features.module.modules.movement.speed.IntaveSpeedMode
import net.bloom.bloomclient.value.values.ModeValue

object ModuleSpeed: Module(name = "Speed", category = ModuleCategory.MOVEMENT) {

    private val speedMode = ModeValue("Mode", arrayOf(
        GrimCollideSpeedMode,
        IntaveSpeedMode
    ), this)

    override fun onDisable() {
        mc.timer.timerSpeed = 1f
    }

}