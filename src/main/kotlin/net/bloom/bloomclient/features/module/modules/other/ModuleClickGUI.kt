package net.bloom.bloomclient.features.module.modules.other

import net.bloom.bloomclient.event.GuiKeyPressEvent
import net.bloom.bloomclient.event.NanoVGRenderEvent
import net.bloom.bloomclient.features.module.Module
import net.bloom.bloomclient.features.module.ModuleCategory
import net.bloom.bloomclient.ui.clickgui.augustus.AugustusClickGUI
import net.bloom.bloomclient.ui.clickgui.rise.RiseClickGUI
import net.bloom.bloomclient.ui.clickgui.tab.TabClickGUI
import net.bloom.bloomclient.utils.struct.MSTimer
import net.bloom.bloomclient.value.values.FloatValue
import net.bloom.bloomclient.value.values.ListValue
import net.lenni0451.lambdaevents.EventHandler
import org.lwjgl.input.Keyboard

object ModuleClickGUI: Module(
	name = "ClickGUI",
	description = "Display a GUI that you can edit settings and toggle modules.",
	category = ModuleCategory.OTHER,
	keyBind = Keyboard.KEY_RSHIFT
) {
	private val msTimer = MSTimer()
	private val mode = ListValue("Mode", "Rise", arrayOf("Augustus", "Bloom", "Rise"))
	val scale = FloatValue("Scale", 1f, 0.1f, 2f)

	override fun onEnable() {
		val clickgui = when (mode.get().lowercase()) {
			"augustus" -> AugustusClickGUI
			"bloom" -> TabClickGUI
			"rise" -> RiseClickGUI
			else -> return
		}

		mc.displayGuiScreen(clickgui)
		msTimer.reset()
	}

	@EventHandler
	fun on2D(event: NanoVGRenderEvent) {
		if (mode.get() != "Rise") return
		RiseClickGUI.render()
	}

	@EventHandler
	fun onGuiKey(event: GuiKeyPressEvent) {
		if (!msTimer.hasTimePassed(50)) return
		if (event.key == keyBind) mc.displayGuiScreen(null)
	}
}