package net.bloom.bloomclient.features.module.modules.combat

import net.bloom.bloomclient.event.AttackEvent
import net.bloom.bloomclient.features.module.Module
import net.bloom.bloomclient.features.module.ModuleCategory
import net.bloom.bloomclient.utils.player.InventoryUtils
import net.lenni0451.lambdaevents.EventHandler

object ModuleAutoWeapon : <PERSON>du<PERSON>(name = "AutoWeapon", category = ModuleCategory.COMBAT) {

    @EventHandler
    fun onAttack(event: AttackEvent) {
        runSelectBestWeapon()
    }

    fun runSelectBestWeapon() {
        val slot = InventoryUtils.getBestWeaponSlotForAttacking()

        if (slot != -1 && slot != mc.thePlayer.inventory.currentItem)
            mc.thePlayer.inventory.currentItem = slot
    }
}