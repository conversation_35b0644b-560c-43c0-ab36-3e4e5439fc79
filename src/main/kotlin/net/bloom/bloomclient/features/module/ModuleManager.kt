package net.bloom.bloomclient.features.module

import net.bloom.bloomclient.BloomClient
import net.lenni0451.lambdaevents.EventHandler
import net.bloom.bloomclient.event.KeyEvent
import net.bloom.bloomclient.utils.ClassUtils
import net.bloom.bloomclient.utils.ClientUtils
import java.util.*

class ModuleManager: EventListener {

    val modules = TreeSet<Module> {module1, module2 -> module1.name.compareTo(module2.name)}
    val moduleClassMap = hashMapOf<Class<out Module>, Module>()

    init {
        BloomClient.eventManager.register(this)
    }

    fun registerModules() {
        ClientUtils.LOGGER.info("[ModuleManager] Loading modules...")
        ClassUtils.resolvePackage("${this.javaClass.`package`.name}.modules", Module::class.java).forEach { registerModule(it) }
        ClientUtils.LOGGER.info("[ModuleManager] Successfully loaded ${modules.size} modules.")
    }

    fun registerModules(vararg modules: Module) {
        for (module in modules)
            registerModule(module)
    }

    fun registerModules(vararg moduleClasses: Class<out Module>) {
        for (moduleClass in moduleClasses)
            registerModule(moduleClass)
    }


    fun registerModule(module: Module) {
        modules += module
        moduleClassMap[module.javaClass] = module

        module.onInitialize()
        BloomClient.commandManager.registerCommand(ModuleCommand(module, module.values))
    }

    fun registerModule(moduleClass: Class<out Module>) {
        try {
            registerModule(moduleClass.getDeclaredConstructor().newInstance())
        } catch (e: IllegalAccessException) {
            registerModule(ClassUtils.getObjectInstance(moduleClass) as Module)
        } catch (e: Exception) {
            ClientUtils.LOGGER.error("Failed to load module: ${moduleClass.name}: ${e.message}")
        }
    }

    fun unregisterModule(module: Module) {
        modules -= module
        moduleClassMap.remove(module::class.java)
        BloomClient.eventManager.unregister(module)
    }

    @EventHandler
    fun onKey(event: KeyEvent) {
        modules.filter { it.keyBind == event.key }.forEach { it.toggle() }
    }

    fun shutdown() {
        modules.forEach { it.onShutdown() }
    }

    fun getModule(name: String) = modules.find { it.name.equals(name, true) }

    fun <T: Module> getModule(clazz: Class<T>) = moduleClassMap[clazz] as T?

    operator fun <T: Module> get(clazz: Class<T>) = getModule(clazz)

    operator fun get(category: ModuleCategory) = modules.filter { it.category == category }
}