package net.bloom.bloomclient.features.module.modules.render

import net.bloom.bloomclient.event.Render2DEvent
import net.bloom.bloomclient.features.module.Module
import net.bloom.bloomclient.features.module.ModuleCategory
import net.bloom.bloomclient.ui.hud.HUD
import net.lenni0451.lambdaevents.EventHandler

object ModuleHUD: Module(
	name = "HUD",
	description = "Display an HUD.",
	category = ModuleCategory.RENDER,
	defaultEnable = true
) {
	@EventHandler
	fun onRender2D(event: Render2DEvent) {
		HUD.render(false)
	}
}