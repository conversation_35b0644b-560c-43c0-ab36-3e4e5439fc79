package net.bloom.bloomclient.features.module.modules.movement.noslow

import net.bloom.bloomclient.event.PostMotionEvent
import net.bloom.bloomclient.event.PreMotionEvent
import net.bloom.bloomclient.event.UpdateEvent
import net.bloom.bloomclient.features.component.components.player.PacketComponent.sendPacket
import net.bloom.bloomclient.features.mode.Mode
import net.bloom.bloomclient.features.module.modules.movement.ModuleNoSlow
import net.bloom.bloomclient.value.values.BoolValue
import net.bloom.bloomclient.value.values.ListValue
import net.lenni0451.lambdaevents.EventHandler
import net.minecraft.network.play.client.C07PacketPlayerDigging
import net.minecraft.network.play.client.C07PacketPlayerDigging.Action.RELEASE_USE_ITEM
import net.minecraft.util.BlockPos
import net.minecraft.util.EnumFacing

object VanillaConsumeNoSlow: Mode("Vanilla")

object IntaveConsumeNoSlow: Mode("Intave") {
    private val modes = ListValue("Modes", "New", arrayOf("Release", "New"))
    @EventHandler
    fun onMotion(event: PreMotionEvent) {
        mc.thePlayer ?: return

        if (!ModuleNoSlow.isUsingFood)
            return

        when (modes.get().lowercase()){
            "release" -> sendPacket(C07PacketPlayerDigging(RELEASE_USE_ITEM, BlockPos.ORIGIN, EnumFacing.UP))
            "new" -> {
                val usedTicks = mc.thePlayer.itemInUseDuration - mc.thePlayer.itemInUseCount
                val timeLeft = mc.thePlayer.itemInUseCount

                if (usedTicks >= mc.thePlayer.itemInUseDuration - 2 || timeLeft == 0)
                    sendPacket(C07PacketPlayerDigging(RELEASE_USE_ITEM, BlockPos.ORIGIN, EnumFacing.UP))
            }
        }
    }
}

object CustomConsumeNoSlow: Mode("Custom") {
    private val consumeSwitchPre by BoolValue("SwitchItemPre", false)
    private val consumeSwitchPost by BoolValue("SwitchItemPost", false)
    private val consumeC08Pre by BoolValue("C08Pre", false)
    private val consumeC08Post by BoolValue("C08Post", true)
    private val consumeC08Update by BoolValue("C08Update", false)
    private val consumeC0CPre by BoolValue("C0CPre", false)
    private val consumeC0CPost by BoolValue("C0CPost", false)
    private val consumeC07NRPre by BoolValue("C07NReleasePre", false)
    private val consumeC07NRPost by BoolValue("C07NReleasePost", false)
    private val consumeC07NRUpdate by BoolValue("C07NReleaseUp", false)
    private val consumeC07BRPre by BoolValue("C07BReleasePre", false)
    private val consumeC07BRPost by BoolValue("C07BReleasePost", false)
    private val consumeC07BRUpdate by BoolValue("C07BReleaseUp", false)
    private val consumeC07NDPre by BoolValue("C07NDropPre", false)
    private val consumeC07NDPost by BoolValue("C07NDropPost", false)
    private val consumeC07NDUpdate by BoolValue("C07NDropUp", false)
    private val consumeC07BDPre by BoolValue("C07BDropPre", false)
    private val consumeC07BDPost by BoolValue("C07BDropPost", false)
    private val consumeC07BDUpdate by BoolValue("C07BDropUp", false)

    @EventHandler
    fun onPreMotion(event: PreMotionEvent) {
        if (consumeC08Pre) ModuleNoSlow.sendC08()
        if (consumeC0CPre) ModuleNoSlow.sendC0C()
        if (consumeC07NRPre) ModuleNoSlow.sendC07NR()
        if (consumeC07BRPre) ModuleNoSlow.sendC07BR()
        if (consumeC07NDPre) ModuleNoSlow.sendC07ND()
        if (consumeC07BDPre) ModuleNoSlow.sendC07BD()

        if (consumeSwitchPre)
            ModuleNoSlow.sendSwitchPackets()
    }

    @EventHandler
    fun onPostMotion(event: PostMotionEvent) {
        if (consumeC08Post) ModuleNoSlow.sendC08()
        if (consumeC0CPost) ModuleNoSlow.sendC0C()
        if (consumeC07NRPost) ModuleNoSlow.sendC07NR()
        if (consumeC07BRPost) ModuleNoSlow.sendC07BR()
        if (consumeC07NDPost) ModuleNoSlow.sendC07ND()
        if (consumeC07BDPost) ModuleNoSlow.sendC07BD()

        if (consumeSwitchPost)
            ModuleNoSlow.sendSwitchPackets()
    }

    @EventHandler
    fun onUpdate(event: UpdateEvent) {
        if (consumeC08Update) ModuleNoSlow.sendC08()
        if (consumeC07NRUpdate) ModuleNoSlow.sendC07NR()
        if (consumeC07BRUpdate) ModuleNoSlow.sendC07BR()
        if (consumeC07NDUpdate) ModuleNoSlow.sendC07ND()
        if (consumeC07BDUpdate) ModuleNoSlow.sendC07BD()
    }
}
