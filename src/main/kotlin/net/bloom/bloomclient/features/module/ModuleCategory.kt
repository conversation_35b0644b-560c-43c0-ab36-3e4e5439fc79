package net.bloom.bloomclient.features.module

import net.bloom.bloomclient.ui.clickgui.rise.screen.CategoryScreen

enum class ModuleCategory(val categoryName: String, val screen: CategoryScreen) {
    COMBAT("Combat", CategoryScreen()),
    MOVEMENT("Movement", CategoryScreen()),
    OTHER("Other", CategoryScreen()),
    PLAYER("Player", CategoryScreen()),
    RENDER("Render", CategoryScreen()),
    CLIENT("Client", CategoryScreen()),
    WORLD("World", CategoryScreen()),
}