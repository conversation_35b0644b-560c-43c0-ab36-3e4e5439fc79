package net.bloom.bloomclient.features.module.modules.render

import net.bloom.bloomclient.event.ReceivedPacketEvent
import net.bloom.bloomclient.features.module.Module
import net.bloom.bloomclient.features.module.ModuleCategory
import net.bloom.bloomclient.value.values.BoolValue
import net.lenni0451.lambdaevents.EventHandler
import net.minecraft.network.play.server.S3FPacketCustomPayload

object ModuleAntiBlind : Module(
    name = "AntiBlind",
    description = "Make you not be blinded by conditions.",
    category = ModuleCategory.RENDER
) {
    val confusionEffect = BoolValue("Confusion", true)
    val pumpkinEffect = BoolValue("Pumpkin", true)
    val bossHealth = BoolValue("BossHealth", true)
    private val bookPage = BoolValue("BookPage", true)
    val achievements = BoolValue("Achievements", true)

    @EventHandler
    fun onPacket(event: ReceivedPacketEvent){
        if (!bookPage.get())
            return

        val packet = event.packet
        if (packet is S3FPacketCustomPayload && packet.channelName == "MC|BOpen")
            event.isCancelled = true
    }
}