package net.bloom.bloomclient.features.module.modules.movement.noslow

import net.bloom.bloomclient.event.PostMotionEvent
import net.bloom.bloomclient.event.PreMotionEvent
import net.bloom.bloomclient.event.UpdateEvent
import net.bloom.bloomclient.features.mode.Mode
import net.bloom.bloomclient.features.module.modules.movement.ModuleNoSlow
import net.bloom.bloomclient.value.values.BoolValue
import net.lenni0451.lambdaevents.EventHandler

object VanillaBowNoSlow: Mode("Vanilla")

object CustomBowNoSlow: Mode("Custom") {
    private val bowSwitchPre by <PERSON><PERSON><PERSON><PERSON><PERSON>("SwitchItemPre", false)
    private val bowSwitchPost by <PERSON><PERSON><PERSON><PERSON><PERSON>("SwitchItemPost", false)
    private val bowC08Pre by <PERSON><PERSON><PERSON><PERSON><PERSON>("C08Pre", false)
    private val bowC08Post by <PERSON><PERSON><PERSON><PERSON><PERSON>("C08Post", true)
    private val bowC08Update by <PERSON><PERSON><PERSON><PERSON><PERSON>("C08Update", false)
    private val bowC0CPre by <PERSON><PERSON><PERSON><PERSON><PERSON>("C0CPre", false)
    private val bowC0CPost by <PERSON><PERSON><PERSON><PERSON><PERSON>("C0CPost", false)
    private val bowC07NRPre by <PERSON><PERSON><PERSON>alue("C07NReleasePre", false)
    private val bowC07NRPost by BoolValue("C07NReleasePost", false)
    private val bowC07NRUpdate by BoolValue("C07NReleaseUp", false)
    private val bowC07BRPre by BoolValue("C07BReleasePre", false)
    private val bowC07BRPost by BoolValue("C07BReleasePost", false)
    private val bowC07BRUpdate by BoolValue("C07BReleaseUp", false)
    private val bowC07NDPre by BoolValue("C07NDropPre", false)
    private val bowC07NDPost by BoolValue("C07NDropPost", false)
    private val bowC07NDUpdate by BoolValue("C07NDropUp", false)
    private val bowC07BDPre by BoolValue("C07BDropPre", false)
    private val bowC07BDPost by BoolValue("C07BDropPost", false)
    private val bowC07BDUpdate by BoolValue("C07BDropUp", false)

    @EventHandler
    fun onPreMotion(event: PreMotionEvent) {
        if (bowC08Pre) ModuleNoSlow.sendC08()
        if (bowC0CPre) ModuleNoSlow.sendC0C()
        if (bowC07NRPre) ModuleNoSlow.sendC07NR()
        if (bowC07BRPre) ModuleNoSlow.sendC07BR()
        if (bowC07NDPre) ModuleNoSlow.sendC07ND()
        if (bowC07BDPre) ModuleNoSlow.sendC07BD()

        if (bowSwitchPre)
            ModuleNoSlow.sendSwitchPackets()
    }

    @EventHandler
    fun onPostMotion(event: PostMotionEvent) {
        if (bowC08Post) ModuleNoSlow.sendC08()
        if (bowC0CPost) ModuleNoSlow.sendC0C()
        if (bowC07NRPost) ModuleNoSlow.sendC07NR()
        if (bowC07BRPost) ModuleNoSlow.sendC07BR()
        if (bowC07NDPost) ModuleNoSlow.sendC07ND()
        if (bowC07BDPost) ModuleNoSlow.sendC07BD()

        if (bowSwitchPost)
            ModuleNoSlow.sendSwitchPackets()
    }

    @EventHandler
    fun onUpdate(event: UpdateEvent) {
        if (bowC08Update) ModuleNoSlow.sendC08()
        if (bowC07NRUpdate) ModuleNoSlow.sendC07NR()
        if (bowC07BRUpdate) ModuleNoSlow.sendC07BR()
        if (bowC07NDUpdate) ModuleNoSlow.sendC07ND()
        if (bowC07BDUpdate) ModuleNoSlow.sendC07BD()
    }
}
