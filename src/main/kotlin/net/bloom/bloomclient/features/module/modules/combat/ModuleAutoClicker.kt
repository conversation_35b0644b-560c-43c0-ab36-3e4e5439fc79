package net.bloom.bloomclient.features.module.modules.combat

import net.bloom.bloomclient.event.GameLoopEvent
import net.bloom.bloomclient.features.module.Module
import net.bloom.bloomclient.features.module.ModuleCategory
import net.bloom.bloomclient.features.module.modules.combat.autoclicker.DragClickAutoClicker
import net.bloom.bloomclient.features.module.modules.combat.autoclicker.NormalAutoClicker
import net.bloom.bloomclient.value.values.BoolValue
import net.bloom.bloomclient.value.values.ModeValue
import net.lenni0451.lambdaevents.EventHandler
import net.minecraft.client.settings.KeyBinding

object ModuleAutoClicker: Module(name = "AutoClicker", description = "Constantly clicks when holding down a mouse button.", category = ModuleCategory.COMBAT) {
    private val leftClick = BoolValue("LeftClick", true)
    private val rightClick = BoolValue("RightClick", true)

    private val modes = ModeValue("Mode", arrayOf(
        <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
        DragClickAutoClicker
    ), this)

    private val breakBlocks = BoolValue("BreakBlocks", true)

    val shouldLeftClick: Boolean
        get() = mc.gameSettings.keyBindAttack.isKeyDown && leftClick.get() && (!breakBlocks.get() || mc.playerController.curBlockDamageMP == 0f)

    private val shouldRightClick: Boolean
        get() = mc.gameSettings.keyBindUseItem.isKeyDown && !mc.thePlayer.isUsingItem && rightClick.get()

    @EventHandler
    fun onGameLoop(event: GameLoopEvent) {
        val mode = modes.get()

        if (shouldLeftClick)
            mode.handleLeftClick()

        if (shouldRightClick)
            mode.handleRightClick()
    }

    fun click(rightClick: Boolean) {
        val keyCode = if (rightClick) mc.gameSettings.keyBindUseItem.keyCode else mc.gameSettings.keyBindAttack.keyCode
        KeyBinding.setKeyBindState(keyCode, true)
        KeyBinding.onTick(keyCode)
    }

}