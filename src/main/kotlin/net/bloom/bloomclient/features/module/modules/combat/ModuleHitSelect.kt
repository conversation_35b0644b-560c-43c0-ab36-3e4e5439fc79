package net.bloom.bloomclient.features.module.modules.combat

import net.bloom.bloomclient.event.AttackEvent
import net.bloom.bloomclient.event.PreUpdateEvent
import net.bloom.bloomclient.features.module.Module
import net.bloom.bloomclient.features.module.ModuleCategory
import net.bloom.bloomclient.utils.player.MovementUtils
import net.bloom.bloomclient.value.values.IntegerValue
import net.bloom.bloomclient.value.values.ListValue
import net.lenni0451.lambdaevents.EventHandler

object ModuleHitSelect : Module(name = "HitSelect", description = "Choose the best time to hit.", category = ModuleCategory.COMBAT) {
    private val modes = ListValue("Modes", "Active", arrayOf("Pause", "Active"))
    private val preferences = ListValue("Preferences", "MoveSpeed", arrayOf("MoveSpeed", "KBReduction", "CriticalHits"))
    private val chance = IntegerValue("Chance", 80, 0, 100)
    private val delay = IntegerValue("Delay", 420, 300, 500)
    private var attackTime: Long = -1
    private var currentShouldAttack = false

    fun canAttack(): Boolean {
        var canAttack = currentShouldAttack

        if (!ModuleHitSelect.state || modes.get() == "Active")
            canAttack = true

        debug(canAttack.toString())

        return canAttack
    }

    @EventHandler(priority = -5)
    fun onAttack(event: AttackEvent) {
        if (modes.get() == "Active" && !currentShouldAttack) {
            event.isCancelled = true
            return
        }

        if (canAttack())
            attackTime = System.currentTimeMillis()
    }

    @EventHandler(priority = 5)
    fun onPreUpdate(event: PreUpdateEvent) {
        currentShouldAttack = false

        if (Math.random() * 100 > chance.get()) {
            currentShouldAttack = true
        } else {
            when (preferences.get().lowercase()) {
                "kbreduction" -> currentShouldAttack = mc.thePlayer.hurtTime > 0 && !mc.thePlayer.onGround && MovementUtils.isMoving
                "criticalhits" -> currentShouldAttack = !mc.thePlayer.onGround && mc.thePlayer.motionY < 0
            }

            if (!currentShouldAttack)
                currentShouldAttack = System.currentTimeMillis() - attackTime >= delay.get()
        }
    }
}