package net.bloom.bloomclient.features.module.modules.combat.killaura.aimvector

import net.bloom.bloomclient.features.mode.Mode
import net.bloom.bloomclient.utils.player.RotationUtils
import net.bloom.bloomclient.value.values.FloatRangeValue
import net.bloom.bloomclient.value.values.FloatValue
import net.minecraft.entity.EntityLivingBase
import net.minecraft.util.Vec3
import net.minecraft.util.VectorRotation

abstract class AimVectorMode(mode: String): Mode(mode) {
    abstract fun getVectorRotation(entity: EntityLivingBase): VectorRotation
}

object NormalAimVectorMode: AimVectorMode("Normal") {
    override fun getVectorRotation(entity: EntityLivingBase): VectorRotation {
        val vector = RotationUtils.clampVecInBoundingBox(mc.thePlayer.getPositionEyes(1f), entity.entityBoundingBox)

        val rotation = RotationUtils.toRotation(vector)
        return VectorRotation(entity, vector, rotation)
    }
}

object BruteforceAimVectorMode: AimVectorMode("Bruteforce") {
    private val vectorRangeX = FloatRangeValue("VectorRangeX", 0f, 1f, 0f, 1f)
    private val vectorStepX = FloatValue("VectorStepX", 0.1f, 0.1f, 1f)
    private val vectorRangeY = FloatRangeValue("VectorRangeY", 0f, 1f, 0f, 1f)
    private val vectorStepY = FloatValue("VectorStepY", 0.1f, 0.1f, 1f)
    private val vectorRangeZ = FloatRangeValue("VectorRangeZ", 0f, 1f, 0f, 1f)
    private val vectorStepZ = FloatValue("VectorStepZ", 0.1f, 0.1f, 1f)

    override fun getVectorRotation(entity: EntityLivingBase): VectorRotation {
        var bestVectorRotation = NormalAimVectorMode.getVectorRotation(entity)
        var vectorRotations = mutableListOf<VectorRotation>()

        for (x in vectorRangeX.getValueRangeDouble(vectorStepX.get().toDouble())) {
            for (y in vectorRangeY.getValueRangeDouble(vectorStepY.get().toDouble())) {
                for (z in vectorRangeZ.getValueRangeDouble(vectorStepZ.get().toDouble())) {
                    vectorRotations += RotationUtils.calculateRotationFromVector(entity, Vec3(x, y, z)) ?: continue
                }
            }
        }

        vectorRotations.minByOrNull {
            it.vec.distanceTo(bestVectorRotation.vec)
        }?.let {
            bestVectorRotation = it
        }

        return bestVectorRotation
    }
}

object CenterAimVectorMode: AimVectorMode("Center") {
    override fun getVectorRotation(entity: EntityLivingBase): VectorRotation {
        return RotationUtils.calculateRotationFromVector(entity, Vec3(0.5, 0.5, 0.5)) ?: NormalAimVectorMode.getVectorRotation(entity)
    }
}
