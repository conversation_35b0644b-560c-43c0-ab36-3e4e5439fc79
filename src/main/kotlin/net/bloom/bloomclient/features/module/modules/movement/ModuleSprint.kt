package net.bloom.bloomclient.features.module.modules.movement

import net.bloom.bloomclient.event.PreSprintEvent
import net.bloom.bloomclient.features.module.Module
import net.bloom.bloomclient.features.module.ModuleCategory
import net.lenni0451.lambdaevents.EventHandler
import net.minecraft.client.settings.GameSettings

object ModuleSprint : Module(name = "Sprint", description = "Automatically sprints all the time.", category = ModuleCategory.MOVEMENT) {
    @EventHandler(priority = 5)
    fun onGameLoop(event: PreSprintEvent) {
        mc.thePlayer ?: return
        mc.gameSettings.keyBindSprint.pressed = true
    }

    override fun onDisable() {
        mc.thePlayer ?: return
        mc.gameSettings.keyBindSprint.pressed = GameSettings.isKeyDown(mc.gameSettings.keyBindSprint)
    }
}