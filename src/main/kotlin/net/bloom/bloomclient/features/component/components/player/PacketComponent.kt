package net.bloom.bloomclient.features.component.components.player

import net.bloom.bloomclient.event.GameLoopEvent
import net.bloom.bloomclient.event.ReceivedPacketEvent
import net.bloom.bloomclient.event.SentPacketEvent
import net.bloom.bloomclient.event.WorldEvent
import net.bloom.bloomclient.features.component.Component
import net.bloom.bloomclient.utils.struct.MSTimer
import net.bloom.bloomclient.utils.struct.RollingArrayLongBuffer
import net.lenni0451.lambdaevents.EventHandler
import net.minecraft.network.INetHandler
import net.minecraft.network.NetworkManager
import net.minecraft.network.Packet
import net.minecraft.network.play.INetHandlerPlayClient
import net.minecraft.network.play.INetHandlerPlayServer
import java.util.concurrent.locks.ReentrantLock
import kotlin.collections.plusAssign
import kotlin.concurrent.withLock
import kotlin.concurrent.write


object PacketComponent : Component() {
    private val inboundBuffer = RollingArrayLongBuffer(1000)
    private val outboundBuffer = RollingArrayLongBuffer(1000)

    var inbound: Int
        get() = inboundBuffer.getTimestampsSince(System.currentTimeMillis() - 1000L)
        set(_) = inboundBuffer.add(System.currentTimeMillis())

    var outbound: Int
        get() = outboundBuffer.getTimestampsSince(System.currentTimeMillis() - 1000L)
        set(_) = outboundBuffer.add(System.currentTimeMillis())

    private val packetTimer = MSTimer()

    @EventHandler
    fun onSentPacket(event: SentPacketEvent) {
        outbound++
    }

    @EventHandler
    fun onReceivedPacket(event: ReceivedPacketEvent){
        inbound++
    }

    @JvmStatic
    fun sendPacketNoEvent(packet: Packet<*>) {
        sendPacket(packet, false)
    }

    fun receivePacketNoEvent(packet: Packet<INetHandlerPlayServer>) {
        val netManager = mc.netHandler?.networkManager ?: return

        if (netManager.channel.isOpen) {
            try {
                packet.processPacket(netManager.packetListener as INetHandlerPlayServer)
            } catch (_: Exception) {}
        }
    }

    fun processPacket(packet: Packet<*>) {
        val netManager = mc.netHandler?.networkManager ?: return

        try {
            (packet as Packet<INetHandler>).processPacket(netManager.netHandler)
        } catch (_: Exception) {}
    }

    private val queuedPackets = ArrayDeque<Packet<*>>()
    private val queueLock = ReentrantLock()

    fun schedulePacketProcess(elements: Collection<Packet<*>>): Boolean = queueLock.withLock {
        queuedPackets.addAll(elements)
    }

    fun schedulePacketProcess(element: Packet<*>): Boolean = queueLock.withLock {
        queuedPackets.add(element)
    }

    fun isQueueEmpty(): Boolean = queueLock.withLock {
        queuedPackets.isEmpty()
    }

    @EventHandler
    fun onGameLopp(event: GameLoopEvent){
        queueLock.withLock {
            queuedPackets.removeEach { packet ->
                handlePacket(packet)
                true
            }
        }
    }

    @EventHandler
    fun onWorld(event: WorldEvent){
        queueLock.withLock {
            queuedPackets.clear()
        }
    }

    @JvmStatic
    fun sendPacket(packet: Packet<*>, triggerEvent: Boolean = true) {
        if (triggerEvent) {
            mc.netHandler?.addToSendQueue(packet)
            return
        }

        val netManager = mc.netHandler?.networkManager ?: return

        if (netManager.isChannelOpen) {
            netManager.flushOutboundQueue()
            netManager.dispatchPacket(packet, null)
        } else {
            netManager.readWriteLock.write {
                netManager.outboundPacketsQueue += NetworkManager.InboundHandlerTuplePacketListener(packet, null)
            }
        }
    }

    @JvmStatic
    fun sendPackets(vararg packets: Packet<*>, triggerEvents: Boolean = true) =
        packets.forEach { sendPacket(it, triggerEvents) }

    @JvmStatic
    fun handlePackets(vararg packets: Packet<*>) =
        packets.forEach { handlePacket(it) }

    @JvmStatic
    private fun handlePacket(packet: Packet<*>?) {
        (packet as Packet<INetHandlerPlayClient>).processPacket(mc.netHandler)
    }

    inline fun <T> MutableCollection<T>.removeEach(max: Int = this.size, predicate: (T) -> Boolean) {
        var i = 0
        val iterator = iterator()
        while (iterator.hasNext()) {
            if (i > max) {
                break
            }

            val next = iterator.next()
            if (predicate(next)) {
                iterator.remove()
                i++
            }
        }
    }
}