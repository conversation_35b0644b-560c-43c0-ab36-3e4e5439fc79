package net.bloom.bloomclient.features.component.components.client

import net.bloom.bloomclient.event.TickEvent
import net.bloom.bloomclient.features.component.Component
import net.lenni0451.lambdaevents.EventHandler

object DelayedActionComponent: Component() {
    val actions = mutableListOf<Action>()

    @EventHandler
    fun onTick(event: TickEvent) {
        actions.removeIf {
            if (it.ticks > 0)
                it.ticks--

            if (it.ticks == 0) {
                it.action()
                true
            } else false
        }
    }

    fun runAction(ticks: Int, action: () -> Unit) {
        actions.add(Action(ticks, action))
    }

    class Action(var ticks: Int, var action: () -> Unit)

}