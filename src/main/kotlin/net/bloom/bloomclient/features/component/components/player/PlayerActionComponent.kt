package net.bloom.bloomclient.features.component.components.player

import net.bloom.bloomclient.event.SentPacketEvent
import net.bloom.bloomclient.features.component.Component
import net.lenni0451.lambdaevents.EventHandler
import net.minecraft.network.play.client.*

object PlayerActionComponent : Component() {
    private var slot = false
    private var attack = false
    private var swing = false
    private var block = false
    private var inventory = false

    fun isPlayerDoing(): Boolean {
        return isPlayerDoing(slot = true, attack = true, swing = true, block = true, inventory = true)
    }

    fun isPlayerDoing(slot: <PERSON><PERSON><PERSON>, attack: <PERSON><PERSON><PERSON>, swing: <PERSON><PERSON><PERSON>, block: <PERSON><PERSON><PERSON>, inventory: <PERSON><PERSON>an): <PERSON><PERSON><PERSON> {
        return this.slot && slot || this.attack && attack || this.swing && swing || this.block && block || this.inventory && inventory
    }

    fun reset() {
        slot = false
        swing = false
        attack = false
        block = false
        inventory = false
    }

    @EventHandler
    fun onPacket(event: SentPacketEvent) {
        when (val packet = event.packet) {
            is C09PacketHeldItemChange -> slot = true
            is C0APacketAnimation -> swing = true
            is C02PacketUseEntity -> attack = true
            is C08PacketPlayerBlockPlacement, is C07PacketPlayerDigging -> block = true
            is C0EPacketClickWindow, is C0DPacketCloseWindow -> inventory = true
            is C16PacketClientStatus -> {
                if (packet.status == C16PacketClientStatus.EnumState.OPEN_INVENTORY_ACHIEVEMENT)
                    inventory = true
            }
            is C03PacketPlayer -> reset()
        }
    }
}