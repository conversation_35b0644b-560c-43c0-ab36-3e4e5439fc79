package net.bloom.bloomclient.features.shared.randomizedclicker

import net.bloom.bloomclient.features.mode.Mode
import net.bloom.bloomclient.utils.MathUtils
import net.bloom.bloomclient.utils.RandomUtils
import net.bloom.bloomclient.value.values.IntRangeValue
import net.bloom.bloomclient.value.values.IntegerValue
import java.security.SecureRandom
import java.util.*
import kotlin.math.*
import kotlin.random.Random

abstract class RandomizedClickerMode(mode: String): Mode(mode) {
    abstract fun calculate(delay: Long): Long
}

class NoRandomizedClickerMode: RandomizedClickerMode("None") {
    override fun calculate(delay: Long) = delay
}

class LegitClickerMode: RandomizedClickerMode("Legit") {
    val startCPS = IntegerValue("StartCPS", 8, 1, 20)
    val cps = IntRangeValue("CPS", 15, 17, 0, 20)

    private var lastClickTime = System.currentTimeMillis()
    private var lastCPSChange = System.currentTimeMillis()

    // Randomized CPS ranges
    private val minCPS = cps.minValue
    private val maxCPS = cps.maxValue
    private var currentCPS = startCPS.get().toDouble()

    override fun calculate(delay: Long): Long {
        val now = System.currentTimeMillis()
        val timeSinceLastClick = now - lastClickTime

        // Occasionally change CPS to avoid pattern detection (AutoClickK, AutoClickM)
        if (now - lastCPSChange > 3000L || clickHistory.size < 10) {
            currentCPS = minCPS + (Random.nextDouble() * (maxCPS - minCPS))
            lastCPSChange = now
        }

        // Base delay calculation
        var newDelay = (1000.0 / currentCPS).toLong()

        // Apply human-like jitter (AutoClickB, AutoClickH)
        newDelay += (Random.nextDouble() * 30 - 15).toLong() // ±15ms jitter

        // Random spikes/delays (AutoClickG, AutoClickO)
        if (Random.nextDouble() < 0.05) { // 5% chance of a spike
            newDelay = if (Random.nextBoolean()) newDelay * 2 else max(30, newDelay / 2)
        }

        // Ensure minimum delay (AutoClickA)
        newDelay = max(50, newDelay)

        // Store history for statistical checks
        clickHistory.add(timeSinceLastClick)
        if (clickHistory.size > MAX_HISTORY_SIZE) {
            clickHistory.removeFirst()
        }

        // Adjust for statistical checks (AutoClickC, AutoClickD, AutoClickE, etc.)
        newDelay = adjustForDetection(newDelay)

        lastClickTime = now
        return newDelay
    }

    private val clickHistory = LinkedList<Long>()
    private val MAX_HISTORY_SIZE = 50

    private fun adjustForDetection(delay: Long): Long {
        if (clickHistory.size < 10)
            return delay

        val average = clickHistory.average()
        val variance = clickHistory.map { (it - average).pow(2) }.average()
        val stdDev = sqrt(variance)
        val skewness = MathUtils.getSkewness(clickHistory)
        val kurtosis = MathUtils.getKurtosis(clickHistory)
        val distinctDelays = clickHistory.distinct().count()

        // AutoClickA (CPS too high)
        val currentCPS = 1000.0 / average
        if (currentCPS > 17.0) {
            return delay + 10 // Slow down slightly
        }

        // AutoClickB (Low standard deviation)
        if (stdDev < 167.0) {
            return if (Random.nextBoolean()) delay + 20 else delay - 20
        }

        // AutoClickC (Rounded CPS)
        if (abs(currentCPS - currentCPS.roundToInt()) < 0.08) {
            return delay + (if (Random.nextBoolean()) 5 else -5)
        }

        // AutoClickD (Low skewness)
        if (skewness < -0.01) {
            return if (Random.nextDouble() < 0.3) delay * 2 else delay
        }

        // AutoClickE (Low variance)
        if (variance / 1000.0 < 28.2) {
            return delay + (Random.nextDouble() * 40 - 20).toLong()
        }

        // AutoClickF (Not enough distinct delays)
        if (distinctDelays < 13) {
            return delay + (if (Random.nextBoolean()) 15 else -15)
        }

        // AutoClickG (Too few outliers)
        if (clickHistory.count { it > 150 } < 3) {
            return if (Random.nextDouble() < 0.2) delay * 3 else delay
        }

        // AutoClickH (Similar deviations)
        if (clickHistory.size >= 20) {
            val last20 = clickHistory.takeLast(20)
            val lastDeviation = sqrt(last20.map { (it - last20.average()).pow(2) }.average())
            if (abs(stdDev - lastDeviation) < 7.52) {
                return delay + (Random.nextDouble() * 30 - 15).toLong()
            }
        }

        // AutoClickI (Low kurtosis)
        if (kurtosis / 1000.0 < 41.78) {
            return if (Random.nextDouble() < 0.1) delay * 4 else delay
        }

        // AutoClickJ (Too consistent)
        if (clickHistory.size >= 10) {
            val last10 = clickHistory.takeLast(10).sorted()
            if (last10.last() - last10.first() < 50) {
                return delay + (Random.nextDouble() * 50 - 25).toLong()
            }
        }

        // AutoClickT (Kurtosis too low)
        if (kurtosis < 13 * 1000) {
            return if (Random.nextDouble() < 0.15) delay * 3 else delay
        }

        return delay
    }
}

class MultiplierRandomizedClickerMode: RandomizedClickerMode("Multiplier") {
    private val factorValue = IntRangeValue("Factor", 5, 10, 1, 10)

    override fun calculate(delay: Long): Long {
        var randomizeFactor = 1
        val randomizeValue = RandomUtils.nextInt(factorValue.minValue, factorValue.maxValue)
        if (randomizeValue > 0)
            randomizeFactor += randomizeValue * 10

        val randomize = abs(SecureRandom.getInstanceStrong().nextGaussian() * randomizeFactor).toLong()
        return delay + randomize
    }
}