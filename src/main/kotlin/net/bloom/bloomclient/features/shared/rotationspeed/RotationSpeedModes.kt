package net.bloom.bloomclient.features.shared.rotationspeed

import net.bloom.bloomclient.features.mode.Mode
import net.bloom.bloomclient.utils.RandomUtils
import net.bloom.bloomclient.utils.extension.safeDiv
import net.bloom.bloomclient.utils.extension.withGCD
import net.bloom.bloomclient.utils.player.RotationUtils
import net.bloom.bloomclient.value.values.ListValue
import net.minecraft.util.MathHelper
import net.minecraft.util.Rotation
import kotlin.math.abs
import kotlin.math.hypot
import kotlin.math.min
import kotlin.math.pow

abstract class RotationSpeedMode(mode: String): Mode(mode) {
    abstract fun calculate(currentRotation: Rotation, targetRotation: Rotation, yawSpeed: Float, pitchSpeed: Float, sensitivity: Double, gcdFix: Boolean): Rotation
}

class LinearRotationSpeedMode: RotationSpeedMode("Linear") {
    override fun calculate(currentRotation: Rotation, targetRotation: Rotation, yawSpeed: Float, pitchSpeed: Float, sensitivity: Double, gcdFix: Boolean): Rotation {
        val yDiff = RotationUtils.getAngleDifference(targetRotation.yaw, currentRotation.yaw)
        val pDiff = targetRotation.pitch - currentRotation.pitch

        val rotationDifference = hypot(yDiff, pDiff)

        var (straightLineYaw, straightLinePitch) = run {
            abs(yDiff safeDiv rotationDifference) * yawSpeed to
            abs(pDiff safeDiv rotationDifference) * pitchSpeed
        }

        straightLineYaw = yDiff.coerceIn(-straightLineYaw, straightLineYaw)
        straightLinePitch = pDiff.coerceIn(-straightLinePitch, straightLinePitch)

        val targetYaw = currentRotation.yaw + straightLineYaw
        val targetPitch = MathHelper.clamp_float(currentRotation.pitch + straightLinePitch, -90f, 90f)

        return Rotation(targetYaw, targetPitch).also {
            if(gcdFix) it.fixedRotation()
        }
    }
}

class InterpolationSpeedMode: RotationSpeedMode("Interpolation") {
    override fun calculate(currentRotation: Rotation, targetRotation: Rotation, yawSpeed: Float, pitchSpeed: Float, sensitivity: Double, gcdFix: Boolean): Rotation {
        val yDiff = RotationUtils.getAngleDifference(targetRotation.yaw, currentRotation.yaw)
        val pDiff = targetRotation.pitch - currentRotation.pitch

        val multiplierYaw = (sensitivity * 0.6f + 0.2f).pow(3) * 8.0f
        val multiplierPitch = (sensitivity * 0.6f + 0.2f).pow(3) * 8.0f

        val straightLineYaw = yDiff * multiplierYaw * 0.15f
        val straightLinePitch = pDiff * multiplierPitch * 0.15f

        val targetYaw = currentRotation.yaw + straightLineYaw
        val targetPitch = MathHelper.clamp_double(currentRotation.pitch + straightLinePitch, -90.0, 90.0)

        return Rotation(targetYaw.toFloat(), targetPitch.toFloat())
    }
}

/**
 * <AUTHOR>
 */
class HumanizedRotationSpeedMode: RotationSpeedMode("Humanized") {
    private val timing = ListValue("Timing", "OnStart", arrayOf("OnStart", "OnSlowdown", "Always"))

    override fun calculate(currentRotation: Rotation, targetRotation: Rotation, yawSpeed: Float, pitchSpeed: Float, sensitivity: Double, gcdFix: Boolean): Rotation {
        val yDiff = RotationUtils.getAngleDifference(targetRotation.yaw, currentRotation.yaw)
        val pDiff = RotationUtils.getAngleDifference(targetRotation.pitch, currentRotation.pitch)

        val rotationDifference = hypot(yDiff, pDiff)

        var (straightLineYaw, straightLinePitch) = run {
            val baseYawSpeed = abs(yDiff safeDiv rotationDifference) * yawSpeed * RandomUtils.nextFloat(0.9f, 1.1f)
            val basePitchSpeed = abs(pDiff safeDiv rotationDifference) * pitchSpeed * RandomUtils.nextFloat(0.9f, 1.1f)
            baseYawSpeed to basePitchSpeed
        }

        straightLineYaw = yDiff.coerceIn(-straightLineYaw, straightLineYaw)
        straightLinePitch = pDiff.coerceIn(-straightLinePitch, straightLinePitch)

        // Humans usually have some small jitter when moving their mouse from point A to point B.
        // Usually when a rotation axis' difference is prioritized.
        if (rotationDifference > 0F) {
            val yawJitter = RandomUtils.nextFloat(-0.03F, 0.03F) * straightLineYaw
            val pitchJitter = RandomUtils.nextFloat(-0.02F, 0.02F) * straightLinePitch

            straightLineYaw += yawJitter
            straightLinePitch += pitchJitter
        }

        val minYaw = RandomUtils.nextFloat(min(0.1f, RotationUtils.getFixedAngleDelta()), 0.1f).withGCD()
        val minPitch = RandomUtils.nextFloat(min(0.1f, RotationUtils.getFixedAngleDelta()), 0.1f).withGCD()

        applySlowDown(straightLineYaw, minYaw, true) {
            straightLineYaw = it
        }

        applySlowDown(straightLinePitch, minPitch, false) {
            straightLinePitch = it
        }

        return Rotation(currentRotation.yaw + straightLineYaw, currentRotation.pitch + straightLinePitch).also {
            if(gcdFix) it.fixedRotation()
        }
    }

    private fun applySlowDown(diff: Float, min: Float, yaw: Boolean, action: (Float) -> Unit) {
        if (diff == 0f) {
            action(diff)
            return
        }

        val lastTick1 = if (yaw) {
            RotationUtils.getAngleDifference(serverRotation.yaw, lastServerRotation.yaw)
        } else {
            RotationUtils.getAngleDifference(serverRotation.pitch, lastServerRotation.pitch)
        }

        val diffAbs = abs(diff)
        val isSlowingDown = diffAbs <= abs(lastTick1)

        if (diffAbs.withGCD() <= min && (timing.get() == "Always" || timing.get() == "OnSlowDown" && isSlowingDown || timing.get() == "OnStart" && lastTick1 == 0F)) {
            action(0f)
            return
        }

        val range = when (lastTick1) {
            0f -> {
                val inc = 0.2f * (diffAbs / 50f).coerceIn(0f, 1f)

                0.1F + inc..0.5F + inc
            }
            else -> 0.3f..0.7f
        }

        val new = lastTick1 + (diff - lastTick1) * RandomUtils.nextFloat(range.start, range.endInclusive)

        if (abs(new.withGCD()) <= min && isSlowingDown) {
            action(diff)
        } else {
            action(new)
        }
    }

}