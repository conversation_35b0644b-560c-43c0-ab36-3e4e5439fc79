package net.bloom.bloomclient.utils

object StringUtils {

	val allowedCharactersArray =
		charArrayOf('/', '\n', '\r', '\t', '\u0000', '', '`', '?', '*', '\\', '<', '>', '|', '\"', ':')

	private val allowedCharArray =
		"\u00c0\u00c1\u00c2\u00c8\u00ca\u00cb\u00cd\u00d3\u00d4\u00d5\u00da\u00df\u00e3\u00f5\u011f\u0130\u0131\u0152\u0153\u015e\u015f\u0174\u0175\u017e\u0207\u0000\u0000\u0000\u0000\u0000\u0000\u0000 !\"#$%&\'()*+,-./0123456789:;<=>?@ABCDEFGHIJKLMNOPQRSTUVWXYZ[\\]^_`abcdefghijklmnopqrstuvwxyz{|}~\u0000\u00c7\u00fc\u00e9\u00e2\u00e4\u00e0\u00e5\u00e7\u00ea\u00eb\u00e8\u00ef\u00ee\u00ec\u00c4\u00c5\u00c9\u00e6\u00c6\u00f4\u00f6\u00f2\u00fb\u00f9\u00ff\u00d6\u00dc\u00f8\u00a3\u00d8\u00d7\u0192\u00e1\u00ed\u00f3\u00fa\u00f1\u00d1\u00aa\u00ba\u00bf\u00ae\u00ac\u00bd\u00bc\u00a1\u00ab\u00bb\u2591\u2592\u2593\u2502\u2524\u2561\u2562\u2556\u2555\u2563\u2551\u2557\u255d\u255c\u255b\u2510\u2514\u2534\u252c\u251c\u2500\u253c\u255e\u255f\u255a\u2554\u2569\u2566\u2560\u2550\u256c\u2567\u2568\u2564\u2565\u2559\u2558\u2552\u2553\u256b\u256a\u2518\u250c\u2588\u2584\u258c\u2590\u2580\u03b1\u03b2\u0393\u03c0\u03a3\u03c3\u03bc\u03c4\u03a6\u0398\u03a9\u03b4\u221e\u2205\u2208\u2229\u2261\u00b1\u2265\u2264\u2320\u2321\u00f7\u2248\u00b0\u2219\u00b7\u221a\u207f\u00b2\u25a0\u0000"
			.toCharArray()

	fun toCompleteString(args: Array<String>, start: Int) = args.drop(start).joinToString(" ")

	fun isAllowedCharacter(character: Char) =
		character.code != 167 && character.code >= 32 && character.code != 127

	fun randomMagicText(text: String): String = buildString(text.length) {
		for (c in text) {
			if (isAllowedCharacter(c)) {
				val index = RandomUtils.nextInt(allowedCharArray.size)
				append(allowedCharArray[index])
			}
		}
	}

	/**
	 * Used to build output as Hex
	 */
	private val DIGITS_LOWER =
		charArrayOf('0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f')

	/**
	 * Used to build output as Hex
	 */
	private val DIGITS_UPPER =
		charArrayOf('0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F')

	/**
	 * Converts an array of bytes into an array of characters representing the hexadecimal values of each byte in order.
	 * The returned array will be double the length of the passed array, as it takes two characters to represent any
	 * given byte.
	 *
	 * @param data a byte[] to convert to Hex characters
	 * @param toLowerCase `true` converts to lowercase, `false` to uppercase
	 * @return A char[] containing hexadecimal characters in the selected case
	 */
	fun encodeHex(data: ByteArray, toLowerCase: Boolean): CharArray {
		return encodeHex(data, if (toLowerCase) DIGITS_LOWER else DIGITS_UPPER)
	}

	/**
	 * Converts an array of bytes into an array of characters representing the hexadecimal values of each byte in order.
	 * The returned array will be double the length of the passed array, as it takes two characters to represent any
	 * given byte.
	 *
	 * @param data a byte[] to convert to Hex characters
	 * @param toDigits the output alphabet (must contain at least 16 chars)
	 * @return A char[] containing the appropriate characters from the alphabet
	 *         For best results, this should be either upper- or lower-case hex.
	 */
	private fun encodeHex(data: ByteArray, toDigits: CharArray): CharArray {
		val l = data.size
		val out = CharArray(l shl 1)
		// two characters form the hex value.
		var i = 0
		var j = 0
		while (i < l) {
			out[j++] = toDigits[0xF0 and data[i].toInt() ushr 4]
			out[j++] = toDigits[0x0F and data[i].toInt()]
			i++
		}
		return out
	}

}