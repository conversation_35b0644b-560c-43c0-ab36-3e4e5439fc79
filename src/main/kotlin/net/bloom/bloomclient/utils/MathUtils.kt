package net.bloom.bloomclient.utils

import com.google.common.collect.Lists
import java.math.BigDecimal
import java.math.RoundingMode
import java.util.concurrent.ThreadLocalRandom
import kotlin.math.PI
import kotlin.math.exp
import kotlin.math.pow
import kotlin.math.sqrt

object MathUtils {
	fun interpolate(current: Double, old: Double, scale: Double) = old + (current - old) * scale
	fun interpolate(current: Float, old: Float, scale: Float) = old + (current - old) * scale

	fun isHover(posX: Double, posY: Double, startX: Double, startY: Double, endX: Double, endY: Double): Boolean {
		return posX in startX..endX && posY >= startY && posY <= endY
	}

	fun isHover(posX: Float, posY: Float, startX: Float, startY: Float, endX: Float, endY: Float): Boolean {
		return posX in startX..endX && posY >= startY && posY <= endY
	}

	@JvmStatic
	fun isHover(posX: Int, posY: Int, startX: Int, startY: Int, endX: Int, endY: Int): Boolean {
		return posX in startX..endX && posY >= startY && posY <= endY
	}

	fun isHover(posX: Number, posY: Number, startX: Number, startY: Number, endX: Number, endY: Number): Boolean {
		return isHover(posX.toFloat(), posY.toFloat(), startX.toFloat(), startY.toFloat(), endX.toFloat(), endY.toFloat())
	}

	fun mouseOver(
		mouseX: Double, mouseY: Double,
		x: Double, y: Double,
		width: Double, height: Double
	) = mouseX in x..(x + width) && mouseY in y..(y + height)

	fun mouseOver(
		mouseX: Float, mouseY: Float,
		x: Float, y: Float,
		width: Float, height: Float
	) = mouseX in x..(x + width) && mouseY in y..(y + height)

	fun mouseOver(
		mouseX: Int, mouseY: Int,
		x: Int, y: Int,
		width: Int, height: Int
	) = mouseX in x..(x + width) && mouseY in y..(y + height)

	fun round(number: Float, scale: Int): BigDecimal {
        val bd = BigDecimal(number.toString())
        return bd.setScale(scale, RoundingMode.CEILING)
    }

	fun calculateGaussianValue(x: Int, sigma: Float): Float {
		val sigmaSq = sigma * sigma
		return 1.0f / (2.0f * PI.toFloat() * sigmaSq) * exp(-(x * x) / (2.0f * sigmaSq))
	}

	fun lerp(min: Float, max: Float, scale: Float) = min + (max - min) * scale

	fun getRandom(min: Float, max: Float): Float {
		var min = min
		var max = max
		if (min == max) {
			return min
		} else if (min > max) {
			val d = min
			min = max
			max = d
		}
		return ThreadLocalRandom.current().nextDouble(min.toDouble(), max.toDouble()).toFloat()
	}

	fun getVariance(data: Collection<Number>): Double {
		var count = 0
		var sum = 0.0
		var variance = 0.0
		for (number in data) {
			sum += number.toDouble()
			++count
		}
		val average = sum / count
		for (number in data) {
			variance += (number.toDouble() - average).pow(2.0)
		}
		return variance
	}

	fun getStandardDeviation(data: Collection<Number>): Double {
		val variance = getVariance(data)
		return sqrt(variance)
	}

	fun getSkewness(data: Collection<Number>): Double {
		var sum = 0.0
		var count = 0
		val numbers = Lists.newArrayList<Double>()
		for (number in data) {
			sum += number.toDouble()
			++count
			numbers.add(number.toDouble())
		}
		numbers.sort()
		val mean = sum / count
		val median = if ((count % 2 != 0)) numbers[count / 2] else ((numbers[(count - 1) / 2] + numbers[count / 2]) / 2.0)
		val variance = getVariance(data)
		return 3.0 * (mean - median) / variance
	}

	fun getAverage(data: Collection<Number>?): Double {
		if (data.isNullOrEmpty()) {
			return 0.0
		}
		var sum = 0.0
		for (number in data) {
			sum += number.toDouble()
		}
		return sum / data.size
	}

	fun getKurtosis(data: Collection<Number>): Double {
		var sum = 0.0
		var count = 0
		for (number in data) {
			sum += number.toDouble()
			++count
		}
		if (count < 3.0) {
			return 0.0
		}
		val efficiencyFirst = count * (count + 1.0) / ((count - 1.0) * (count - 2.0) * (count - 3.0))
		val efficiencySecond: Double = 3.0 * (count - 1.0).pow(2.0) / ((count - 2.0) * (count - 3.0))
		val average = sum / count
		var variance = 0.0
		var varianceSquared = 0.0
		for (number2 in data) {
			variance += (average - number2.toDouble()).pow(2.0)
			varianceSquared += (average - number2.toDouble()).pow(4.0)
		}
		return efficiencyFirst * (varianceSquared / (variance / sum).pow(2.0)) - efficiencySecond
	}

	fun round(value: Double, places: Int): Double {
		require(places >= 0)
		return BigDecimal(value).setScale(places, RoundingMode.HALF_UP).toDouble()
	}

	fun getCps(data: Collection<Number>?): Double {
		return 20.0 / getAverage(data) * 50.0
	}

	fun getDistinct(data: Collection<Number?>): Int {
		return data.stream().distinct().count().toInt()
	}
}