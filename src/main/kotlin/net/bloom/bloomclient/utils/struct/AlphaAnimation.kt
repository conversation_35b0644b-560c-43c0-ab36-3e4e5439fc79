package net.bloom.bloomclient.utils.struct

data class AlphaAnimation(var current: Float, var target: Float) {

    fun update(delta: Float) {
        if ((target > 0.01f || current > 0.01f) && current != target) {
            current = if (target > current) {
                minOf(current + delta, target)
            } else {
                maxOf(current - delta, target)
            }
        }
    }

}
