package net.bloom.bloomclient.utils.extension

class FloatRangeIterator(val range: ClosedFloatingPointRange<Float>, val step: Float = 0.1f) : Iterator<Float> {
    private var value = range.start

    override fun hasNext() = value < range.endInclusive

    override fun next(): Float {
        val returned = value
        value = (value + step).coerceAtMost(range.endInclusive)
        return returned
    }
}

infix fun Float.until(to: Float): ClosedFloatingPointRange<Float> = this..(to - Float.MIN_VALUE)

operator fun ClosedFloatingPointRange<Float>.iterator() = FloatRangeIterator(this)
infix fun ClosedFloatingPointRange<Float>.step(step: Float) = FloatRangeIterator(this, step)