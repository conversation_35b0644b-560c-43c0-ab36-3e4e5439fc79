package net.bloom.bloomclient.utils.player

import net.bloom.bloomclient.features.module.modules.combat.ModuleKillAura
import net.bloom.bloomclient.utils.extension.*
import net.minecraft.client.MinecraftInstance
import net.minecraft.entity.Entity
import net.minecraft.entity.EntityLivingBase
import net.minecraft.util.*
import kotlin.math.*


object RotationUtils: MinecraftInstance() {
    /**
     * Calculate difference between two angle points
     *
     * @param a angle point
     * @param b angle point
     * @return difference between angle points
     */
    @JvmStatic
    fun getAngleDifference(a: Float, b: Float): Float {
        return MathHelper.wrapAngleTo180(a - b)
    }

    /**
     * Returns the smallest angle difference possible with a specific sensitivity ("gcd")
     */
    fun getFixedAngleDelta(sensitivity: Float = mc.gameSettings.mouseSensitivity) = (sensitivity * 0.6f + 0.2f).pow(3) * 1.2f

    fun clampVecInBoundingBox(look: Vec3, axisAlignedBB: AxisAlignedBB): Vec3 {
        return Vec3(
            look.xCoord.coerceIn(axisAlignedBB.minX, axisAlignedBB.maxX),
            look.yCoord.coerceIn(axisAlignedBB.minY, axisAlignedBB.maxY),
            look.zCoord.coerceIn(axisAlignedBB.minZ, axisAlignedBB.maxZ)
        )
    }

    @JvmStatic
    fun getDistanceToEntityBox(entity: Entity): Double {
        val eyes = mc.thePlayer.getPositionEyes(1f)
        val pos = getBestHitVec(entity)
        val x = abs(pos.xCoord - eyes.xCoord)
        val y = abs(pos.yCoord - eyes.yCoord)
        val z = abs(pos.zCoord - eyes.zCoord)
        return sqrt(x * x + y * y + z * z)
    }

    @JvmStatic
    fun getBestHitVec(entity: Entity): Vec3 {
        val hitVec = getHitVec(entity)
        hitVec.yCoord -= 0.4
        return hitVec
    }

    fun getHitVec(entity: Entity): Vec3 {
        val positionEyes = mc.thePlayer.getPositionEyes(1f)
        val f11 = entity.collisionBorderSize
        val entityBoundingBox = entity.entityBoundingBox.expand(f11.toDouble())
        return clampVecInBoundingBox(positionEyes, entityBoundingBox)
    }

    fun getVectorForRotation(pitch: Float, yaw: Float): Vec3 {
        val f = MathHelper.cos(-yaw * 0.017453292f - Math.PI.toFloat())
        val f1 = MathHelper.sin(-yaw * 0.017453292f - Math.PI.toFloat())
        val f2 = -MathHelper.cos(-pitch * 0.017453292f)
        val f3 = MathHelper.sin(-pitch * 0.017453292f)
        return Vec3((f1 * f2).toDouble(), f3.toDouble(), (f * f2).toDouble())
    }

    /**
     * Translate vec to rotation
     * Diff supported
     *
     * @param vec target vec
     * @param predict predict new location of your body
     * @return rotation
     */
    @JvmOverloads
    fun toRotation(vec: Vec3, predict: Boolean = false, diff: Vec3? = null): Rotation {
        val eyesPos = Vec3(mc.thePlayer.posX, mc.thePlayer.entityBoundingBox.minY + mc.thePlayer.eyeHeight, mc.thePlayer.posZ)

        if (predict)
            eyesPos.addVector(mc.thePlayer.motionX, mc.thePlayer.motionY, mc.thePlayer.motionZ)

        val (diffX, diffY, diffZ) = diff ?: (vec - eyesPos)

        return toRotationFromDiff(diffX, diffY, diffZ)
    }

    fun toRotationFromDiff(x: Double, y: Double, z: Double): Rotation {
        return Rotation(
            MathHelper.wrapAngleTo180(Math.toDegrees(atan2(z, x)).toFloat() - 90f),
            MathHelper.wrapAngleTo180((-Math.toDegrees(atan2(y, sqrt(x * x + z * z)))).toFloat())
        )
    }

    fun getRotationDifference(rotation: Rotation) = getRotationDifference(rotation, mc.thePlayer.rotation)

    fun getRotationDifference(rotation1: Rotation, rotation2: Rotation): Double {
        return hypot(getAngleDifference(rotation1.yaw, rotation2.yaw).toDouble(), (rotation1.pitch - rotation2.pitch).toDouble())
    }

    fun calculateRotationFromVector(entity: EntityLivingBase, vector: Vec3): VectorRotation? {
        val vec = entity.entityBoundingBox.lerpWith(vector)
        val rotation = toRotation(vec)

        val obj = RaycastUtils.perfromRaycast(1f, rotation, ModuleKillAura.attackRangeValue.get(), 0f) ?: return null
        return if (obj.typeOfHit == MovingObjectPosition.MovingObjectType.ENTITY && obj.entityHit == entity) {
            VectorRotation(entity, vec, rotation)
        } else null
    }
}