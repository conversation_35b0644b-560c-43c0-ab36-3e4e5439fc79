package net.bloom.bloomclient.utils.player

import com.google.common.base.Predicates
import net.minecraft.client.MinecraftInstance
import net.minecraft.entity.Entity
import net.minecraft.util.*

object RaycastUtils: MinecraftInstance() {

    fun perfromRaycast(partialTicks: Float, rots: Rotation, range: Double, hitBoxExpand: Double): MovingObjectPosition? {
        var objectMouseOver: MovingObjectPosition? = null
        val entity = mc.renderViewEntity
        if (entity != null && mc.theWorld != null) {
            mc.mcProfiler.startSection("pick")
            mc.pointedEntity = null
            var d0 = range
            objectMouseOver = entity.rayTraceCustom(range, partialTicks, rots.yaw, rots.pitch)
            var d2 = range
            val vec3 = entity.getPositionEyes(partialTicks)
            var flag = false
            if (mc.playerController.extendedReach()) {
                d0 = 6.0
                d2 = 6.0
            } else if (range > 3.0) {
                flag = true
            }

            if (objectMouseOver != null) {
                d2 = objectMouseOver.hitVec.distanceTo(vec3)
            }

            val vec4 = entity.getLookCustom(partialTicks, rots.yaw, rots.pitch)
            val vec5 = vec3.addVector(vec4.xCoord * d0, vec4.yCoord * d0, vec4.zCoord * d0)
            var pointedEntity: Entity? = null
            var vec6: Vec3? = null
            val list = mc.theWorld.getEntitiesInAABBexcluding(
                entity,
                entity.entityBoundingBox.addCoord(vec4.xCoord * d0, vec4.yCoord * d0, vec4.zCoord * d0)
                    .expand(1.0, 1.0, 1.0),
                Predicates.and(EntitySelectors.NOT_SPECTATING)
            )
            var d3 = d2

            for (entity2 in list) {
                val f2 = (entity2.collisionBorderSize + hitBoxExpand).toFloat()
                val axisalignedbb = entity2.entityBoundingBox.expand(f2.toDouble(), f2.toDouble(), f2.toDouble())
                val movingobjectposition = axisalignedbb.calculateIntercept(vec3, vec5)
                if (axisalignedbb.isVecInside(vec3)) {
                    if (d3 >= 0.0) {
                        pointedEntity = entity2
                        vec6 = if (movingobjectposition == null) vec3 else movingobjectposition.hitVec
                        d3 = 0.0
                    }
                } else if (movingobjectposition != null) {
                    val d4 = vec3.distanceTo(movingobjectposition.hitVec)
                    if (d4 <= d3 || d3 == 0.0) {
                        if (entity2 === entity.ridingEntity) {
                            if (d3 == 0.0) {
                                pointedEntity = entity2
                                vec6 = movingobjectposition.hitVec
                            }
                        } else {
                            pointedEntity = entity2
                            vec6 = movingobjectposition.hitVec
                            d3 = d4
                        }
                    }
                }
            }

            if (pointedEntity != null && flag && vec3.distanceTo(vec6) > range) {
                pointedEntity = null
                objectMouseOver = MovingObjectPosition(
                    MovingObjectPosition.MovingObjectType.MISS,
                    vec6,
                    null,
                    BlockPos(vec6)
                )
            }

            if (pointedEntity != null && (d3 < d2 || objectMouseOver == null)) {
                objectMouseOver = MovingObjectPosition(pointedEntity, vec6)
            }
        }

        return objectMouseOver
    }

    fun perfromRaycast(partialTicks: Float, rots: Rotation, range: Float, hitBoxExpand: Float): MovingObjectPosition? {
        return perfromRaycast(partialTicks, rots, range.toDouble(), hitBoxExpand.toDouble())
    }

}