package net.bloom.bloomclient.utils.render

import net.minecraft.client.renderer.GlStateManager
import java.awt.Color

object GLUtils {

    fun glColor(red: Float, green: Float, blue: Float, alpha: Float) {
        GlStateManager.color(red, green, blue, alpha)
    }

    fun glColor(red: Int, green: Int, blue: Int, alpha: Int) {
        glColor(red / 255f, green / 255f, blue / 255f, alpha / 255f)
    }

    fun glColor(color: Color) {
        glColor(color.red, color.green, color.blue, color.alpha)
    }

    fun glColor(color: Color, alpha: Float) {
        glColor(color.red / 255f, color.green / 255f, color.blue / 255f, alpha)
    }

    fun glColor(color: Int) {
        val alpha = color shr 24 and 0xFF
        val red = color shr 16 and 0xFF
        val green = color shr 8 and 0xFF
        val blue = color and 0xFF
        glColor(red, green, blue, alpha)
    }

    fun glColor(color: Int, alpha: Float) {
        val red = (color shr 16 and 0xFF) / 255f
        val green = (color shr 8 and 0xFF) / 255f
        val blue = (color and 0xFF) / 255f
        glColor(red, green, blue, alpha)
    }

}