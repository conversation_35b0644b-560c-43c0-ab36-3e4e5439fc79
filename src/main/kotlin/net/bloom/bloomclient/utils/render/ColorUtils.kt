package net.bloom.bloomclient.utils.render

import java.awt.Color

object ColorUtils {

    val hexColors = IntArray(16) { i ->
        val baseColor = (i shr 3 and 1) * 85

        val red = (i shr 2 and 1) * 170 + baseColor + if (i == 6) 85 else 0
        val green = (i shr 1 and 1) * 170 + baseColor
        val blue = (i and 1) * 170 + baseColor

        (red and 255 shl 16) or (green and 255 shl 8) or (blue and 255)
    }


    fun getAlphaColor(color: Int, alpha: Int): Int {
        val newAlpha = 255 * alpha / 100
        return (Color((Color(color)).red, (Color(color)).green, (Color(color)).blue, newAlpha)).rgb
    }
}