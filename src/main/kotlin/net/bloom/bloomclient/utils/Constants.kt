package net.bloom.bloomclient.utils

import net.minecraft.client.MinecraftInstance
import net.minecraft.init.Blocks
import net.minecraft.network.handshake.client.C00Handshake
import net.minecraft.network.login.client.C00PacketLoginStart
import net.minecraft.network.login.client.C01PacketEncryptionResponse
import net.minecraft.network.login.server.S00PacketDisconnect
import net.minecraft.network.login.server.S01PacketEncryptionRequest
import net.minecraft.network.login.server.S02PacketLoginSuccess
import net.minecraft.network.login.server.S03PacketEnableCompression
import net.minecraft.network.play.client.*
import net.minecraft.network.play.server.*
import net.minecraft.network.status.client.C00PacketServerQuery
import net.minecraft.network.status.client.C01PacketPing
import net.minecraft.network.status.server.S00PacketServerInfo
import net.minecraft.network.status.server.S01PacketPong
import net.minecraft.util.BlockPos
import net.minecraft.util.EnumFacing
import kotlin.math.PI

object Constants: MinecraftInstance() {

	const val PI_PER_DEGREE = PI / 180

    val SCAFFOLD_BLOCK_BLACKLIST = listOf(
        Blocks.chest, Blocks.ender_chest, Blocks.trapped_chest, Blocks.anvil, Blocks.sand, Blocks.web,
        Blocks.torch, Blocks.crafting_table, Blocks.furnace, Blocks.waterlily, Blocks.dispenser,
        Blocks.stone_pressure_plate, Blocks.wooden_pressure_plate, Blocks.noteblock, Blocks.dropper,
        Blocks.tnt, Blocks.standing_banner, Blocks.wall_banner, Blocks.redstone_torch, Blocks.ladder
    )

    var INVENTORIES_INVALID_ITEMS = listOf(
        Blocks.enchanting_table, Blocks.carpet, Blocks.glass_pane, Blocks.ladder, Blocks.web, Blocks.stained_glass_pane, Blocks.iron_bars,
        Blocks.air, Blocks.water, Blocks.flowing_water, Blocks.lava, Blocks.ladder, Blocks.soul_sand, Blocks.ice, Blocks.packed_ice,
        Blocks.sand, Blocks.flowing_lava, Blocks.snow_layer, Blocks.chest, Blocks.ender_chest, Blocks.torch, Blocks.anvil, Blocks.trapped_chest,
        Blocks.noteblock, Blocks.jukebox, Blocks.wooden_pressure_plate, Blocks.stone_pressure_plate, Blocks.light_weighted_pressure_plate,
        Blocks.heavy_weighted_pressure_plate, Blocks.stone_button, Blocks.tnt, Blocks.wooden_button, Blocks.lever, Blocks.crafting_table,
        Blocks.furnace, Blocks.stone_slab, Blocks.wooden_slab, Blocks.stone_slab2, Blocks.brown_mushroom, Blocks.red_mushroom, Blocks.gold_block,
        Blocks.red_flower, Blocks.yellow_flower, Blocks.flower_pot
    )

    val MOVING_ACTION_KEYS = arrayOf(
        mc.gameSettings.keyBindForward,
        mc.gameSettings.keyBindBack,
        mc.gameSettings.keyBindLeft,
        mc.gameSettings.keyBindRight,
        mc.gameSettings.keyBindSprint,
        mc.gameSettings.keyBindSneak,
        mc.gameSettings.keyBindJump
    )

    val MOVING_KEYS = arrayOf(
        mc.gameSettings.keyBindForward,
        mc.gameSettings.keyBindBack,
        mc.gameSettings.keyBindLeft,
        mc.gameSettings.keyBindRight
    )

    val clientPacketClasses = arrayOf(
        C00PacketKeepAlive::class.java, C01PacketChatMessage::class.java,
        C02PacketUseEntity::class.java, C03PacketPlayer::class.java,
        C03PacketPlayer.C04PacketPlayerPosition::class.java, C03PacketPlayer.C05PacketPlayerLook::class.java,
        C03PacketPlayer.C06PacketPlayerPosLook::class.java, C07PacketPlayerDigging::class.java,
        C08PacketPlayerBlockPlacement::class.java, C09PacketHeldItemChange::class.java,
        C0APacketAnimation::class.java, C0BPacketEntityAction::class.java,
        C0CPacketInput::class.java, C0DPacketCloseWindow::class.java,
        C0EPacketClickWindow::class.java, C0FPacketConfirmTransaction::class.java,
        C10PacketCreativeInventoryAction::class.java, C11PacketEnchantItem::class.java,
        C12PacketUpdateSign::class.java, C13PacketPlayerAbilities::class.java,
        C14PacketTabComplete::class.java, C15PacketClientSettings::class.java,
        C16PacketClientStatus::class.java, C17PacketCustomPayload::class.java,
        C18PacketSpectate::class.java, C19PacketResourcePackStatus::class.java)

    val serverPacketClasses = arrayOf(
        S00PacketKeepAlive::class.java, S01PacketJoinGame::class.java,
        S02PacketChat::class.java, S03PacketTimeUpdate::class.java,
        S04PacketEntityEquipment::class.java, S05PacketSpawnPosition::class.java,
        S07PacketRespawn::class.java, S0DPacketCollectItem::class.java,
        S08PacketPlayerPosLook::class.java, S09PacketHeldItemChange::class.java,
        S0APacketUseBed::class.java, S0BPacketAnimation::class.java,
        S0EPacketSpawnObject::class.java, S0FPacketSpawnMob::class.java,
        S10PacketSpawnPainting::class.java, S11PacketSpawnExperienceOrb::class.java,
        S12PacketEntityVelocity::class.java, S13PacketDestroyEntities::class.java,
        S14PacketEntity::class.java, S14PacketEntity.S15PacketEntityRelMove::class.java,
        S14PacketEntity.S16PacketEntityLook::class.java, S14PacketEntity.S17PacketEntityLookMove::class.java,
        S18PacketEntityTeleport::class.java, S19PacketEntityHeadLook::class.java,
        S19PacketEntityStatus::class.java, S1BPacketEntityAttach::class.java,
        S1CPacketEntityMetadata::class.java, S1DPacketEntityEffect::class.java,
        S1EPacketRemoveEntityEffect::class.java, S1FPacketSetExperience::class.java,
        S20PacketEntityProperties::class.java, S21PacketChunkData::class.java,
        S22PacketMultiBlockChange::class.java, S23PacketBlockChange::class.java,
        S24PacketBlockAction::class.java, S25PacketBlockBreakAnim::class.java,
        S26PacketMapChunkBulk::class.java, S27PacketExplosion::class.java,
        S28PacketEffect::class.java, S3FPacketCustomPayload::class.java,
        S2APacketParticles::class.java, S2BPacketChangeGameState::class.java,
        S2CPacketSpawnGlobalEntity::class.java, S2DPacketOpenWindow::class.java,
        S2EPacketCloseWindow::class.java, S2FPacketSetSlot::class.java,
        S30PacketWindowItems::class.java, S31PacketWindowProperty::class.java,
        S32PacketConfirmTransaction::class.java, S33PacketUpdateSign::class.java,
        S34PacketMaps::class.java, S35PacketUpdateTileEntity::class.java,
        S36PacketSignEditorOpen::class.java, S37PacketStatistics::class.java,
        S38PacketPlayerListItem::class.java, S39PacketPlayerAbilities::class.java,
        S3APacketTabComplete::class.java, S3BPacketScoreboardObjective::class.java,
        S3CPacketUpdateScore::class.java, S3DPacketDisplayScoreboard::class.java,
        S40PacketDisconnect::class.java, S41PacketServerDifficulty::class.java,
        S42PacketCombatEvent::class.java, S43PacketCamera::class.java,
        S44PacketWorldBorder::class.java, S45PacketTitle::class.java,
        S46PacketSetCompressionLevel::class.java, S47PacketPlayerListHeaderFooter::class.java,
        S48PacketResourcePackSend::class.java, S49PacketUpdateEntityNBT::class.java
    )

    val clientOtherPacketClasses = arrayOf(
        C00Handshake::class.java,
        C00PacketLoginStart::class.java, C00PacketServerQuery::class.java,
        C01PacketEncryptionResponse::class.java, C01PacketPing::class.java
    )

    val serverOtherPacketClasses = arrayOf(
        S00PacketDisconnect::class.java, S01PacketEncryptionRequest::class.java,
        S02PacketLoginSuccess::class.java, S03PacketEnableCompression::class.java,
        S00PacketServerInfo::class.java, S01PacketPong::class.java,
        S06PacketUpdateHealth::class.java, S29PacketSoundEffect::class.java,
        S3EPacketTeams::class.java, S0CPacketSpawnPlayer::class.java,
    )

    val MODIFIER_BY_TICK = hashMapOf(
        0 to 0.0f,
        1 to 0.00037497282f,
        2 to 0.0015000105f,
        3 to 0.0033749938f,
        4 to 0.0059999824f,
        5 to 0.009374976f,
        6 to 0.013499975f,
        7 to 0.01837498f,
        8 to 0.023999989f,
        9 to 0.030375004f,
        10 to 0.037500024f,
        11 to 0.04537499f,
        12 to 0.05400002f,
        13 to 0.063374996f,
        14 to 0.07349998f,
        15 to 0.084375024f,
        16 to 0.096000016f,
        17 to 0.10837501f,
        18 to 0.121500015f,
        19 to 0.13537502f,
        20 to 0.14999998f
    )

    val BLOCKFACINGS = arrayOf(
        BlockPos(0, -1, 0) to EnumFacing.UP,
        BlockPos(0, -1, 1) to EnumFacing.UP,
        BlockPos(0, -1, -1) to EnumFacing.UP,
        BlockPos(1, -1, 0) to EnumFacing.UP,
        BlockPos(-1, -1, 0) to EnumFacing.UP,
        BlockPos(0, 0, 1) to EnumFacing.NORTH,
        BlockPos(-1, 0, 1) to EnumFacing.NORTH,
        BlockPos(0, 0, -1) to EnumFacing.SOUTH,
        BlockPos(1, 0, -1) to EnumFacing.SOUTH,
        BlockPos(1, 0, 0) to EnumFacing.WEST,
        BlockPos(1, 0, 1) to EnumFacing.WEST,
        BlockPos(-1, 0, 0) to EnumFacing.EAST,
        BlockPos(-1, 0, -1) to EnumFacing.EAST,
    )

}