package net.bloom.bloomclient.value.values

import com.google.gson.JsonElement
import com.google.gson.JsonPrimitive
import net.bloom.bloomclient.BloomClient
import net.bloom.bloomclient.features.mode.Mode
import net.bloom.bloomclient.features.mode.Stateable
import net.bloom.bloomclient.ui.clickgui.rise.button.value.ModeSelector
import net.bloom.bloomclient.ui.clickgui.rise.button.value.ValueButton
import net.bloom.bloomclient.utils.ClassUtils
import net.bloom.bloomclient.value.Value

open class ModeValue<T: Mode>(name: String, description: String, val modes: Array<T>, val parent: Stateable, displayable: () -> Boolean): Value<T>(name, description, modes.first(), displayable) {
    constructor(name: String, description: String, modes: Array<T>, parent: Stateable): this(name, description, modes, parent, { true })
    constructor(name: String, modes: Array<T>, parent: Stateable): this(name, "No description available", modes, parent, { true })
    constructor(name: String, modes: Array<T>, parent: Stateable, displayable: () -> Boolean): this(name, "No description available", modes, parent, displayable)

    init {
        for (mode in modes) {
            val values = ClassUtils.getValues(mode.javaClass, mode)
            values.forEach {
                val valueName = it.name
                it.name = "$name-${mode.name}-$valueName"
            }
        }
    }

    fun setValue(value: String): Boolean {
        for (mode in modes) {
            if (mode.name.equals(value, true)) {
                set(mode)
                return true
            }
        }

        return false
    }

    override fun onPreChange(oldValue: T, newValue: T) {
        if (parent.state)
            BloomClient.eventManager.unregister(oldValue)
    }

    override fun onPostChange(oldValue: T, newValue: T) {
        if (parent.state)
            BloomClient.eventManager.register(newValue)
    }

    override fun createButton() = ModeSelector(this)

    override fun toJson() = JsonPrimitive(value.name)

    override fun fromJson(element: JsonElement) {
        if (element.isJsonPrimitive) {
            val modeString = element.asString
            val mode = modes.find { it.name.equals(modeString, true) } ?: return
            set(mode)
        }
    }
}