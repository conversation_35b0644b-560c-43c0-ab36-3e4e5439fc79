package net.bloom.bloomclient.font.sizeable

import net.bloom.bloomclient.font.renderer.legacy.LegacyFontRenderer
import net.minecraft.client.gui.FontSizeable
import java.awt.Font

class LegacyFontSizable(name: String, val font: Font): FontSizeable(name) {
    private val sizesMap = hashMapOf<Int, LegacyFontRenderer>()
    private val defaultFont = LegacyFontRenderer(font.deriveFont(Font.PLAIN, 24f)).also { sizesMap[24] = it }

    operator fun get(size: Int) = sizesMap[size] ?: defaultFont

    override fun addSize(size: Int) {
        sizesMap[size] = LegacyFontRenderer(font.deriveFont(Font.PLAIN, size.toFloat()))
    }

    override fun addSizes(vararg sizes: Int) {
        for (size in sizes) addSize(size)
    }
}