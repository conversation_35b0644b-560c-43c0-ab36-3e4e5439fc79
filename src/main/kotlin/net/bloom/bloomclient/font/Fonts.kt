package net.bloom.bloomclient.font

import net.bloom.bloomclient.file.FileManager
import net.bloom.bloomclient.font.sizeable.LegacyFontSizable
import net.bloom.bloomclient.font.sizeable.NanoVGFontSizeable
import net.bloom.bloomclient.utils.ClientUtils
import net.bloom.bloomclient.utils.io.FileUtils
import net.bloom.bloomclient.utils.io.HashUtils
import net.bloom.bloomclient.utils.io.UnzipUtils
import net.bloom.bloomclient.utils.http.HttpUtils
import net.minecraft.client.Minecraft
import net.minecraft.client.gui.FontSizeable
import net.minecraft.client.gui.MinecraftFontRenderer
import java.io.File

object Fonts {

    lateinit var fontSFUI: LegacyFontSizable
    lateinit var fontConsolas: LegacyFontSizable
    lateinit var fontESP: LegacyFontSizable
    lateinit var fontNano: NanoVGFontSizeable
    lateinit var fontLexend: NanoVGFontSizeable
    lateinit var fontIconGUI: NanoVGFontSizeable
    lateinit var fontIconUI: NanoVGFontSizeable

    fun downloadFonts() {
        if (isFontFilesMatches())
            return

        val cacheFontFolder = File(FileManager.cacheFolder, "fonts")
        val fontZipHashFile = File(cacheFontFolder, "fonts.zip.hash")
        val fontZipHashFileEtag = File(cacheFontFolder, "fonts.zip.hash.etag")
        val fontZipHash = HttpUtils.getWithEtag("https://raw.githubusercontent.com/BloomProjects/bloom-resources/refs/heads/main/fonts/fonts.zip.hash", fontZipHashFile, fontZipHashFileEtag).trim()

        // check file hash
        val fontsZip = File(cacheFontFolder, "fonts.zip")
        if (!fontsZip.isFile || !HashUtils.isFileMatchHash(fontsZip, fontZipHash))
            HttpUtils.downloadFile("https://raw.githubusercontent.com/BloomProjects/bloom-resources/refs/heads/main/fonts/fonts.zip", fontsZip, verbose = true)

        UnzipUtils.unzip(fontsZip, FileManager.dir)
    }

    private fun isFontFilesMatches(): Boolean {
        val cacheFontFolder = File(FileManager.cacheFolder, "fonts")

        if (!cacheFontFolder.exists())
            cacheFontFolder.mkdirs()

        val hashFile = File(cacheFontFolder, "fonts.hash")
        val hashEtagFile = File(cacheFontFolder, "fonts.hash.etag")
        val hashFileText = HttpUtils.getWithEtag("https://raw.githubusercontent.com/BloomProjects/bloom-resources/refs/heads/main/fonts/fonts.hash", hashFile, hashEtagFile)

        val filesHashMap = hashFileText.trim().lines().map { it.split(" ") }.associate { it[0] to it[1] }
        for ((filePath, hash) in filesHashMap) {
            val fontFile = File(FileManager.fontsFolder, filePath)
            fontFile.parentFile.mkdirs()

            if (!fontFile.isFile || !HashUtils.isFileMatchHash(fontFile, hash))
                return false
        }

        return true
    }

    fun loadFonts() {
        val l = System.currentTimeMillis()
        ClientUtils.LOGGER.info("Loading Fonts.")
        fontSFUI = LegacyFontSizable("SF UI Pro Display", FileUtils.getFont("sfprodisplay/SFProDisplay-Medium.otf")).also {
            it.addSizes(30, 50, 70, 90, 110)
        }
        fontConsolas = LegacyFontSizable("Consolas", FileUtils.getFont("consolas/Consolas.ttf")).also {
            it.addSizes(40)
        }
        fontESP = LegacyFontSizable("ESP", FileUtils.getFont("esp/ESP.ttf")).also {
            it.addSizes(64)
        }
        fontNano = NanoVGFontSizeable("Nano", "nano/Nano-Regular.ttf").also {
            it.addSizes(16)
        }
        fontLexend = NanoVGFontSizeable("Lexend", "lexend/Lexend-Medium.ttf").also {
            it.addSizes(7, 8, 9, 10, 12, 14, 15, 16, 18)
        }
        fontIconGUI = NanoVGFontSizeable("IconGUI", "icons/bloomUI-icons.ttf").also {
            it.addSizes(12, 16, 24, 30)
        }
        fontIconUI = NanoVGFontSizeable("IconGUI", "icons/Icons-UI.ttf").also {
            it.addSizes(24)
        }
        ClientUtils.LOGGER.info("Loaded Fonts. (${System.currentTimeMillis() - l}ms)")
    }

    fun getFont(name: String): FontSizeable? {
        for (field in Fonts::class.java.declaredFields) {
            try {
                field.isAccessible = true
                val o = field[null]
                if (o is FontSizeable && o.name == name)
                    return o
            } catch (e: IllegalAccessException) {
                e.printStackTrace()
            }
        }

        return null
    }
}