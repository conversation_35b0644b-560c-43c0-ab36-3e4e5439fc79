package net.bloom.bloomclient.font.renderer

import net.bloom.bloomclient.utils.io.FileUtils
import net.bloom.bloomclient.utils.render.NanoVGUtils
import net.minecraft.client.MinecraftInstance
import net.minecraft.client.gui.IFontRenderer
import net.minecraft.client.gui.Side
import org.lwjgl.nanovg.NVGColor
import org.lwjgl.nanovg.NanoVG
import java.awt.Color
import java.nio.ByteBuffer

/**
 * A font renderer using NanoVG
 * <AUTHOR> Blend Client
 */
class NanoVGFontRenderer(val name: String, val height: Int, val path: String, setup: Boolean = true): MinecraftInstance(), IFontRenderer {
    val buffer: ByteBuffer = FileUtils.getFontBuffer(path)

    init {
        if (setup) setupFont()
    }

    private fun setupFont() {
        val context = NanoVGUtils.context
        if (context == 0L) {
            throw IllegalStateException("Failed to initialize NanoVG context.")
        }

        val fontId = NanoVG.nvgCreateFontMem(context, name, buffer, false)
        if (fontId == -1) {
            throw IllegalStateException("Failed to load font: $name")
        }
    }

    override fun drawString(text: String?, x: Int, y: Int, color: Int): Int {
        return drawString(text, x.toFloat(), y.toFloat(), color, false)
    }

    override fun drawString(text: String?, x: Float, y: Float, color: Int): Int {
        return drawString(text, x, y, color, false)
    }

    override fun drawString(text: String?, x: Float, y: Float, color: Int, dropShadow: Boolean): Int {
        return drawString(text, x, y, color, Side.DEFAULT, dropShadow)
    }

    override fun drawString(text: String?, x: Float, y: Float, color: Int, side: Side): Int {
        return drawString(text, x, y, color, side, false)
    }

    override fun drawString(text: String?, x: Float, y: Float, color: Int, side: Side, dropShadow: Boolean): Int {
        text ?: return 0

        val context = NanoVGUtils.context
        if (context == 0L)
            return 0

        var width = 0

        val nvgColor = NVGColor.calloc()

        try {
            NanoVG.nvgBeginPath(context)
            NanoVG.nvgShapeAntiAlias(context, true)

            val objColor = Color(color, true)

            NanoVG.nvgRGBAf(
                objColor.red.toFloat() / 255f,
                objColor.green.toFloat() / 255f,
                objColor.blue.toFloat() / 255f,
                objColor.alpha.toFloat() / 255f,
                nvgColor
            )
            NanoVG.nvgFillColor(context, nvgColor)
            NanoVG.nvgFontFace(context, name)
            NanoVG.nvgFontSize(context, height.toFloat())
            NanoVG.nvgTextAlign(context, NanoVGUtils.getNanoVGSide(side))
            width = NanoVG.nvgText(context, x, y, text).toInt()
            NanoVG.nvgClosePath(context)
        } catch (e: Throwable) {
            e.printStackTrace()
        }

        nvgColor.close()
        return width
    }

    override fun drawCenteredString(text: String?, x: Float, y: Float, color: Int): Int {
        return drawCenteredString(text, x, y, color, true)
    }

    override fun drawCenteredString(text: String?, x: Float, y: Float, color: Int, dropShadow: Boolean): Int {
        return drawString(text, x, y, color, Side(Side.Horizontal.MIDDLE, Side.Vertical.TOP), true)
    }

    override fun drawStringWithShadow(text: String?, x: Float, y: Float, color: Int): Int {
        return drawString(text, x, y, color, true)
    }

    override fun getStringWidth(text: String?): Int {
        text ?: return 0

        val bounds = floatArrayOf(0f, 0f, 0f, 0f)

        val context = NanoVGUtils.context
        NanoVG.nvgFontFace(context, name)
        NanoVG.nvgFontSize(context, height.toFloat())
        NanoVG.nvgTextBounds(context, 0f, 0f, text, bounds)

        val left = bounds[0]
        val right = bounds[2]

        return (right - left).toInt()
    }
}
