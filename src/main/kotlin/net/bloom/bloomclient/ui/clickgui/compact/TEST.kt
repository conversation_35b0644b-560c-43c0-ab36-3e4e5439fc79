/*package net.bloom.bloomclient.ui.clickgui.compact

import net.bloom.bloomclient.BloomClient
import net.bloom.bloomclient.features.module.Module
import net.bloom.bloomclient.features.module.ModuleCategory
import net.bloom.bloomclient.font.Fonts
import net.bloom.bloomclient.utils.MathUtils
import net.bloom.bloomclient.utils.render.NanoVGUtils
import net.bloom.bloomclient.utils.render.animation.EaseUtils
import net.bloom.bloomclient.value.values.BoolValue
import net.bloom.bloomclient.value.values.FloatValue
import net.bloom.bloomclient.value.values.IntegerValue
import net.bloom.bloomclient.value.values.ListValue
import net.minecraft.client.gui.GuiScreen
import org.lwjgl.input.Keyboard
import org.lwjgl.input.Mouse
import java.awt.Color
import kotlin.math.max
import kotlin.math.min

/**
 * Enhanced ClickGUI for Bloom Client
 * @author: tlowng(longathelstan), improved by BloomAI
 * Updates: Redesigned UI, smoother animations, better user experience
 */
object TEST : GuiScreen() {

    // --- GUI Dimensions ---
    private const val GUI_WIDTH = 490f
    private const val GUI_HEIGHT = 300f
    private const val GUI_RADIUS = 10f

    // Title Bar
    private const val TITLE_BAR_HEIGHT = 35f

    // Panels
    private const val CATEGORY_PANEL_WIDTH = 110f
    private const val MODULE_PANEL_WIDTH = 135f

    // --- Animation Values ---
    private var guiOpacity = 0f
    private var moduleSlideAnimation = 0f
    private var valuesPanelAnimation = 0f
    private var lastFrameTime = System.currentTimeMillis()
    private val deltaTimeCalculator = {
        val currentTime = System.currentTimeMillis()
        val deltaTime = (currentTime - lastFrameTime) / 1000f
        lastFrameTime = currentTime
        min(0.05f, deltaTime) // Cap to avoid spikes
    }

    // --- Colors (Enhanced) ---
    private val BG_COLOR = Color(28, 32, 37)
    private val TITLE_BAR_COLOR = Color(18, 20, 22)
    private val HIGHLIGHT_COLOR = Color(35, 40, 45, 200)
    private val HOVER_COLOR = Color(40, 45, 50, 230)
    private val TEXT_COLOR = Color(0xdf, 0xe2, 0xe7)
    private val ACCENT_COLOR = Color(132, 115, 201)
    private val SECONDARY_ACCENT_COLOR = Color(105, 88, 174)
    private val TRANSPARENT = Color(0, 0, 0, 0)
    private val BOOL_ON_COLOR = Color(133, 196, 81)
    private val BOOL_OFF_COLOR = Color(212, 52, 65)
    private val SLIDER_BG_COLOR = Color(60, 60, 60)
    private val SLIDER_PROGRESS_COLOR = ACCENT_COLOR
    private val DROPDOWN_BG_COLOR = Color(25, 27, 29, 240)

    // --- Animation Data ---
    private data class Animation(
        var value: Float = 0f,
        var target: Float = 0f,
        var speed: Float = 0.2f
    ) {
        fun update(deltaTime: Float) {
            value += (target - value) * speed * (deltaTime * 60f)
        }
    }

    private val categoryAnimations = mutableMapOf<ModuleCategory, Animation>()
    private val moduleAnimations = mutableMapOf<Module, Animation>()
    private val sliderAnimations = mutableMapOf<Any, Animation>()
    private val boolAnimations = mutableMapOf<BoolValue, Animation>()

    // Initialize animations
    init {
        ModuleCategory.values().forEach { categoryAnimations[it] = Animation() }
    }

    // --- Dragging and Scrolling ---
    private var guiX = 50f
    private var guiY = 50f
    private var draggingGUI = false
    private var dragOffsetX = 0f
    private var dragOffsetY = 0f

    private var categoryScroll = 0f
    private var moduleScroll = 0f
    private var valueScroll = 0f
    private var targetCategoryScroll = 0f
    private var targetModuleScroll = 0f
    private var targetValueScroll = 0f
    private const val SCROLL_SPEED = 0.3f

    private var draggingSlider: Any? = null
    private var hoveredModule: Module? = null

    // --- Current Selection ---
    private var currentCategory = ModuleCategory.COMBAT
    private var currentModule: Module? = null
    private var previousModule: Module? = null

    // --- Fonts ---
    private val titleFont = Fonts.fontNano[18]       // Increased size
    private val categoryFont = Fonts.fontLexend[10]  // Increased size
    private val moduleFont = Fonts.fontLexend[10]    // Increased size
    private val settingFont = Fonts.fontLexend[9]    // Increased size
    private val valueFont = Fonts.fontLexend[8]      // For values
    private val icon12Font = Fonts.fontIconGUI[12]   // Small icons
    private val icon16Font = Fonts.fontIconGUI[16]   // Medium icons
    private val icon24Font = Fonts.fontIconGUI[24]   // Large icons

    // --- Row Heights & Spacing ---
    private const val MODULE_ROW_HEIGHT = 24f
    private const val SETTING_ROW_HEIGHT = 20f
    private const val CATEGORY_ROW_HEIGHT = 28f
    private const val CAT_SPACING = 3f
    private const val CATEGORY_TOP_MARGIN = 10f
    private const val MODULE_TOP_MARGIN = 10f
    private const val MODULE_SPACING = 3f
    private const val SETTINGS_PADDING = 8f

    // --- Ripple Effect ---
    private data class Ripple(
        val x: Float,
        val y: Float,
        val startTime: Long = System.currentTimeMillis(),
        var alpha: Float = 1.0f
    ) {
        fun update() {
            val age = System.currentTimeMillis() - startTime
            alpha = max(0f, 1f - age / 800f)
        }

        fun getRadius(): Float {
            val age = System.currentTimeMillis() - startTime
            return age / 7f
        }
    }

    private val ripples = mutableListOf<Ripple>()

    // --- Icon Strings (Custom Font) ---
    private const val BLOOM_ICON = "Q"
    private const val COMBAT_ICON = "R"
    private const val MOVE_ICON = "S"
    private const val USER_ICON = "T"
    private const val SEARCH_ICON = "U"
    private const val EYE_ICON = "V"
    private const val SETTINGS_ICON = "W"
    private const val HIDDEN_EYE_ICON = "X"
    private const val ALERT_ICON = "Y"
    private const val CHECK_ICON = "Z"
    private const val PUZZLE_ICON = "B"
    private const val ARROW_ICON = "\uE5CF" // Down arrow
    private const val TOGGLE_ON_ICON = "\uE876" // Check
    private const val TOGGLE_OFF_ICON = "\uE5CD" // Close

    private val listValueDropdownOpen = mutableMapOf<ListValue, Boolean>()
    private val listValueClickMap = mutableMapOf<ListValue, Boolean>()
    private val listValueHoverIndex = mutableMapOf<ListValue, Int>()

    // --- Helper Functions ---
    private fun getCategoryDisplayName(cat: ModuleCategory): String =
        cat.name.lowercase().replaceFirstChar { it.titlecase() }

    private fun getCategoryIcon(cat: ModuleCategory): String = when (cat) {
        ModuleCategory.COMBAT   -> COMBAT_ICON
        ModuleCategory.MOVEMENT -> MOVE_ICON
        ModuleCategory.PLAYER   -> USER_ICON
        ModuleCategory.RENDER   -> EYE_ICON
        ModuleCategory.CLIENT   -> BLOOM_ICON
        else                    -> PUZZLE_ICON
    }

    // Color utilities
    private fun Color.withAlpha(alpha: Int): Color = Color(red, green, blue, alpha)
    private fun Color.withAlpha(alpha: Float): Color = withAlpha((alpha * 255).toInt().coerceIn(0, 255))

    // Easing functions
    private fun ease(current: Float, target: Float, speed: Float, deltaTime: Float): Float {
        return current + (target - current) * min(1f, speed * deltaTime * 60)
    }

    // --- Rendering ---
    override fun drawScreen(mouseX: Int, mouseY: Int, partialTicks: Float) {
        val deltaTime = deltaTimeCalculator()

        // Update animations
        guiOpacity = ease(guiOpacity, 1f, 0.1f, deltaTime)
        moduleSlideAnimation = ease(moduleSlideAnimation, if (currentModule != null) 1f else 0f, 0.2f, deltaTime)
        valuesPanelAnimation = ease(valuesPanelAnimation, if (currentModule != null) 1f else 0f, 0.15f, deltaTime)

        // Update ripples and remove faded ones
        ripples.forEach { it.update() }
        ripples.removeIf { it.alpha <= 0.01f }

        // Update scroll animations
        categoryScroll = ease(categoryScroll, targetCategoryScroll, SCROLL_SPEED, deltaTime)
        moduleScroll = ease(moduleScroll, targetModuleScroll, SCROLL_SPEED, deltaTime)
        valueScroll = ease(valueScroll, targetValueScroll, SCROLL_SPEED, deltaTime)

        // Update boolean animations
        boolAnimations.forEach { (_, anim) -> anim.update(deltaTime) }

        // Update selection animations
        categoryAnimations.forEach { (category, anim) ->
            anim.target = if (category == currentCategory) 1f else 0f
            anim.update(deltaTime)
        }

        // Update dragging
        if (draggingGUI && Mouse.isButtonDown(0)) {
            guiX = mouseX - dragOffsetX
            guiY = mouseY - dragOffsetY
        } else {
            draggingGUI = false
        }

        // Prepare rendering
        NanoVGUtils.preRender()
        NanoVGUtils.save()

        // Apply global opacity
        val alpha = (guiOpacity * 255).toInt()

        // Draw main container with drop shadow
        drawShadow(guiX, guiY, GUI_WIDTH, GUI_HEIGHT, 20f, 0.3f)

        NanoVGUtils.drawOutlineRoundedRect(
            guiX, guiY, GUI_WIDTH, GUI_HEIGHT, 5f, GUI_RADIUS,
            BG_COLOR.withAlpha(alpha), BG_COLOR.withAlpha(alpha)
        )

        // Draw title bar with gradient
        val titleBarGradient = NanoVGUtils.createLinearGradient(
            guiX, guiY,
            guiX + GUI_WIDTH, guiY,
            TITLE_BAR_COLOR.withAlpha(alpha),
            TITLE_BAR_COLOR.darker().withAlpha(alpha)
        )

        NanoVGUtils.drawRoundedRectWithGradient(
            guiX, guiY, GUI_WIDTH, TITLE_BAR_HEIGHT,
            GUI_RADIUS, GUI_RADIUS, 0f, 0f,
            titleBarGradient
        )

        // Draw logo and title
        icon24Font.drawString(
            BLOOM_ICON,
            guiX + 16f,
            guiY + (TITLE_BAR_HEIGHT - icon24Font.height) / 2f,
            Color.WHITE.withAlpha(alpha).rgb
        )

        // Draw client title with gradient text
        val titleGradient = NanoVGUtils.createLinearGradient(
            guiX + 45f, guiY + TITLE_BAR_HEIGHT/2,
            guiX + 100f, guiY + TITLE_BAR_HEIGHT/2,
            ACCENT_COLOR.withAlpha(alpha),
            SECONDARY_ACCENT_COLOR.withAlpha(alpha)
        )

        titleFont.drawGradientString(
            "bloom client",
            guiX + 45f,
            guiY + (TITLE_BAR_HEIGHT - titleFont.height) / 2f + 2f,
            titleGradient
        )

        // Draw search icon
        icon16Font.drawString(
            SEARCH_ICON,
            guiX + GUI_WIDTH - 30f,
            guiY + (TITLE_BAR_HEIGHT - icon16Font.height) / 2f,
            TEXT_COLOR.withAlpha(alpha).rgb
        )

        // Draw dividers between panels
        NanoVGUtils.drawRect(
            guiX + CATEGORY_PANEL_WIDTH,
            guiY + TITLE_BAR_HEIGHT,
            2f, GUI_HEIGHT - TITLE_BAR_HEIGHT,
            TITLE_BAR_COLOR.withAlpha((alpha * 0.8f).toInt())
        )

        // Draw dynamic divider with animation
        val modulesPanelWidth = CATEGORY_PANEL_WIDTH + 2f + MODULE_PANEL_WIDTH
        NanoVGUtils.drawRect(
            guiX + modulesPanelWidth,
            guiY + TITLE_BAR_HEIGHT,
            2f, GUI_HEIGHT - TITLE_BAR_HEIGHT,
            TITLE_BAR_COLOR.withAlpha((alpha * 0.8f * moduleSlideAnimation).toInt())
        )

        // Draw category panel
        drawCategoryPanel(mouseX, mouseY, alpha)

        // Draw module panel
        drawModulePanel(mouseX, mouseY, alpha)

        // Draw settings panel with slide animation
        if (currentModule != null || moduleSlideAnimation > 0.02f) {
            currentModule?.let {
                drawSettingsPanel(it, mouseX, mouseY, alpha)
            } ?: previousModule?.let {
                // Draw fading settings panel during transition
                if (moduleSlideAnimation > 0.02f) {
                    drawSettingsPanel(it, mouseX, mouseY, (alpha * moduleSlideAnimation).toInt())
                }
            }
        }

        // Draw ripple effects on top
        ripples.forEach { ripple ->
            NanoVGUtils.drawCircle(
                ripple.x, ripple.y, ripple.getRadius(),
                Color(255, 255, 255, (ripple.alpha * 40).toInt())
            )
        }

        NanoVGUtils.restore()
        NanoVGUtils.postRender()

        // Store previous module for animations
        if (currentModule != null) {
            previousModule = currentModule
        }
    }

    private fun drawShadow(x: Float, y: Float, width: Float, height: Float, blur: Float, opacity: Float) {
        NanoVGUtils.setShadow(x, y, width, height, blur, opacity)
    }

    private fun drawCategoryPanel(mouseX: Int, mouseY: Int, alpha: Int) {
        val x = guiX
        val y = guiY + TITLE_BAR_HEIGHT + CATEGORY_TOP_MARGIN
        val w = CATEGORY_PANEL_WIDTH
        val h = GUI_HEIGHT - TITLE_BAR_HEIGHT - CATEGORY_TOP_MARGIN
        val categories = ModuleCategory.values()
        val totalCatHeight = categories.size * (CATEGORY_ROW_HEIGHT + CAT_SPACING)
        val minScroll = -(totalCatHeight - h).coerceAtLeast(0f)
        val maxScroll = 0f

        if (MathUtils.isHover(mouseX, mouseY, x.toInt(), y.toInt(), (x + w).toInt(), (y + h).toInt())) {
            val dWheel = Mouse.getDWheel() * 0.15f
            targetCategoryScroll = (targetCategoryScroll + dWheel).coerceIn(minScroll, maxScroll)
        }

        // Draw "Categories" header
        settingFont.drawString(
            "CATEGORIES",
            x + 15f,
            y - CATEGORY_TOP_MARGIN + 2f,
            TEXT_COLOR.withAlpha(alpha * 0.7f).rgb
        )

        NanoVGUtils.renderScissorBox(x, y, w, h) {
            categories.forEachIndexed { i, cat ->
                val yPos = y + categoryScroll + i * (CATEGORY_ROW_HEIGHT + CAT_SPACING)
                if (yPos + CATEGORY_ROW_HEIGHT < y || yPos > y + h) return@forEachIndexed

                val hovered = MathUtils.isHover(mouseX, mouseY, x.toInt(), yPos.toInt(), (x + w - 10f).toInt(), (yPos + CATEGORY_ROW_HEIGHT).toInt())
                if (hovered && hoveredModule == null) {
                    hoveredModule = null // Reset hover when over categories
                }

                // Get animation value for this category
                val anim = categoryAnimations[cat]?.value ?: 0f
                val animatedAlpha = EaseUtils.easeInOutQuad(anim)
                val hoverAnim = if (hovered) 0.3f else 0f

                // Draw background with animation
                val bgColor = if (cat == currentCategory) {
                    ACCENT_COLOR.withAlpha((alpha * 0.2f + animatedAlpha * 0.3f).coerceIn(0f, 1f))
                } else if (hovered) {
                    HIGHLIGHT_COLOR.withAlpha((alpha * 0.7f * hoverAnim).toInt())
                } else {
                    TRANSPARENT
                }

                // Draw indicator bar on the left for selected category
                if (cat == currentCategory) {
                    NanoVGUtils.drawRect(
                        x, yPos,
                        3f, CATEGORY_ROW_HEIGHT,
                        ACCENT_COLOR.withAlpha(alpha)
                    )
                }

                // Draw category background
                NanoVGUtils.drawRoundedRect(
                    x + 5f,
                    yPos,
                    w - 15f,
                    CATEGORY_ROW_HEIGHT,
                    8f,
                    bgColor
                )

                // Draw icon with animation
                val iconStr = getCategoryIcon(cat)
                val iconAlpha = if (cat == currentCategory)
                    alpha
                else
                    (alpha * 0.75f).toInt()

                val iconColor = if (cat == currentCategory)
                    ACCENT_COLOR.withAlpha(iconAlpha)
                else
                    TEXT_COLOR.withAlpha(iconAlpha)

                icon16Font.drawString(
                    iconStr,
                    x + 20f,
                    yPos + (CATEGORY_ROW_HEIGHT - icon16Font.height) / 2f,
                    iconColor.rgb
                )

                // Draw category name with subtle animation
                val nameX = x + 50f
                val nameY = yPos + (CATEGORY_ROW_HEIGHT - categoryFont.height) / 2f

                val textColor = if (cat == currentCategory) {
                    ACCENT_COLOR.withAlpha(alpha)
                } else {
                    TEXT_COLOR.withAlpha(alpha)
                }

                val displayName = getCategoryDisplayName(cat)
                categoryFont.drawString(
                    displayName,
                    nameX,
                    nameY,
                    textColor.rgb
                )

                // Draw module count badge for each category
                val moduleCount = BloomClient.moduleManager.modules
                    .count { it.category == cat }

                val countText = moduleCount.toString()
                val countWidth = categoryFont.getStringWidth(countText)

                NanoVGUtils.drawRoundedRect(
                    x + w - 35f,
                    yPos + (CATEGORY_ROW_HEIGHT - 18f) / 2f,
                    countWidth + 16f,
                    18f,
                    9f,
                    if (cat == currentCategory) ACCENT_COLOR.withAlpha((alpha * 0.2f).toInt())
                    else HIGHLIGHT_COLOR.withAlpha((alpha * 0.5f).toInt())
                )

                categoryFont.drawString(
                    countText,
                    x + w - 27f + (8f - countWidth / 2),
                    yPos + (CATEGORY_ROW_HEIGHT - categoryFont.height) / 2f,
                    if (cat == currentCategory) ACCENT_COLOR.withAlpha(alpha).rgb
                    else TEXT_COLOR.withAlpha(alpha).rgb
                )
            }
        }
    }

    private fun drawModulePanel(mouseX: Int, mouseY: Int, alpha: Int) {
        val x = guiX + CATEGORY_PANEL_WIDTH + 2f
        val y = guiY + TITLE_BAR_HEIGHT
        val w = MODULE_PANEL_WIDTH
        val h = GUI_HEIGHT - TITLE_BAR_HEIGHT

        val modules = BloomClient.moduleManager.modules
            .filter { it.category == currentCategory }
            .sortedBy { it.displayName }

        val totalModHeight = modules.size * (MODULE_ROW_HEIGHT + MODULE_SPACING)
        val minScroll = -(totalModHeight - h + MODULE_TOP_MARGIN * 2).coerceAtLeast(0f)
        val maxScroll = 0f

        if (MathUtils.isHover(mouseX, mouseY, x.toInt(), y.toInt(), (x + w).toInt(), (y + h).toInt())) {
            val dWheel = Mouse.getDWheel() * 0.15f
            targetModuleScroll = (targetModuleScroll + dWheel).coerceIn(minScroll, maxScroll)
        }

        // Draw MODULES header
        settingFont.drawString(
            "MODULES",
            x + 15f,
            y + 10f,
            TEXT_COLOR.withAlpha((alpha * 0.7f).toInt()).rgb
        )

        // Draw module count
        val moduleCountText = "(${modules.size})"
        val moduleCountWidth = settingFont.getStringWidth(moduleCountText)
        settingFont.drawString(
            moduleCountText,
            x + w - 15f - moduleCountWidth,
            y + 10f,
            TEXT_COLOR.withAlpha((alpha * 0.5f).toInt()).rgb
        )

        NanoVGUtils.renderScissorBox(x, y + MODULE_TOP_MARGIN + 10f, w, h - MODULE_TOP_MARGIN - 10f) {
            modules.forEachIndexed { i, mod ->
                val yPos = y + MODULE_TOP_MARGIN + 15f + moduleScroll + i * (MODULE_ROW_HEIGHT + MODULE_SPACING)
                if (yPos + MODULE_ROW_HEIGHT < y || yPos > y + h) return@forEachIndexed

                val hovered = MathUtils.isHover(mouseX, mouseY, x.toInt(), yPos.toInt(), (x + w).toInt(), (yPos + MODULE_ROW_HEIGHT).toInt())
                if (hovered) {
                    hoveredModule = mod
                }

                // Make sure module has an animation
                if (!moduleAnimations.containsKey(mod)) {
                    moduleAnimations[mod] = Animation()
                }

                // Update animation target
                val moduleAnim = moduleAnimations[mod]!!
                moduleAnim.target = when {
                    mod == currentModule -> 1f
                    mod == hoveredModule -> 0.5f
                    else -> 0f
                }

                moduleAnim.update(0.05f)

                // Background color based on state
                val selected = (mod == currentModule)
                val isHovered = (mod == hoveredModule)

                // Calculate the background colors
                val baseColor = when {
                    selected -> ACCENT_COLOR.withAlpha((alpha * 0.15f).toInt())
                    isHovered -> HIGHLIGHT_COLOR.withAlpha((alpha * 0.5f).toInt())
                    else -> TRANSPARENT
                }

                // Enhanced active module visualization
                if (mod.state) {
                    // Draw activation indicator on the left
                    NanoVGUtils.drawRect(
                        x, yPos,
                        3f, MODULE_ROW_HEIGHT,
                        BOOL_ON_COLOR.withAlpha(alpha)
                    )
                }

                // Draw module card with rounded corners
                NanoVGUtils.drawRoundedRect(
                    x + 6f,
                    yPos,
                    w - 12f,
                    MODULE_ROW_HEIGHT,
                    6f,
                    baseColor
                )

                // Draw module name with color based on state
                val textColor = when {
                    mod.state && selected -> BOOL_ON_COLOR
                    mod.state -> BOOL_ON_COLOR.darker()
                    selected -> ACCENT_COLOR
                    else -> TEXT_COLOR
                }

                // Add slight offset for selected module text
                val textOffsetX = if (selected) 2f else 0f

                moduleFont.drawString(
                    mod.displayName,
                    x + 20f + textOffsetX,
                    yPos + (MODULE_ROW_HEIGHT - moduleFont.height) / 2f,
                    textColor.withAlpha(alpha).rgb
                )

                // Draw toggle indicator
                val toggleIcon = if (mod.state) TOGGLE_ON_ICON else TOGGLE_OFF_ICON
                val toggleColor = if (mod.state) BOOL_ON_COLOR else Color(150, 150, 150)

                icon12Font.drawString(
                    toggleIcon,
                    x + w - 25f,
                    yPos + (MODULE_ROW_HEIGHT - icon12Font.height) / 2f,
                    toggleColor.withAlpha(alpha).rgb
                )

                // Draw settings indicator if module has values
                if (mod.values.isNotEmpty()) {
                    icon12Font.drawString(
                        SETTINGS_ICON,
                        x + w - 45f,
                        yPos + (MODULE_ROW_HEIGHT - icon12Font.height) / 2f,
                        (if (selected) ACCENT_COLOR else Color(150, 150, 150)).withAlpha(alpha).rgb
                    )
                }
            }
        }
    }

    private fun drawSettingsPanel(module: Module, mouseX: Int, mouseY: Int, alpha: Int) {
        val x = guiX + CATEGORY_PANEL_WIDTH + 2f + MODULE_PANEL_WIDTH + 2f
        val y = guiY + TITLE_BAR_HEIGHT
        val w = GUI_WIDTH - (CATEGORY_PANEL_WIDTH + 2f + MODULE_PANEL_WIDTH + 2f)
        val h = GUI_HEIGHT - TITLE_BAR_HEIGHT

        // Apply slide-in animation
        val slideOffset = (1f - valuesPanelAnimation) * w
        val panelX = x + slideOffset
        val fadeAlpha = (alpha * valuesPanelAnimation).toInt()

        // Draw settings header with gradient
        val headerGradient = NanoVGUtils.createLinearGradient(
            panelX, y,
            panelX + w, y,
            ACCENT_COLOR.withAlpha((fadeAlpha * 0.2f).toInt()),
            ACCENT_COLOR.darker().withAlpha((fadeAlpha * 0.05f).toInt())
        )

        NanoVGUtils.drawRoundedRectWithGradient(
            panelX, y,
            w, 35f,
            0f, 0f, 0f, 0f,
            headerGradient
        )

        // Draw module name with large font
        moduleFont.drawString(
            "${module.displayName} Settings",
            panelX + 15f,
            y + 15f,
            ACCENT_COLOR.withAlpha(fadeAlpha).rgb
        )

        // Draw toggle button
        val toggleBgColor = if (module.state) BOOL_ON_COLOR else BOOL_OFF_COLOR
        NanoVGUtils.drawRoundedRect(
            panelX + w - 65f,
            y + 10f,
            50f,
            20f,
            10f,
            toggleBgColor.withAlpha(fadeAlpha)
        )

        val toggleText = if (module.state) "ON" else "OFF"
        settingFont.drawString(
            toggleText,
            panelX + w - 65f + 25f - settingFont.getStringWidth(toggleText)/2f,
            y + 10f + (20f - settingFont.height) / 2f,
            Color.WHITE.withAlpha(fadeAlpha).rgb
        )

        // Draw settings content
        val values = module.displayableValues
        var offsetY = 45f
        val totalValHeight = calculateSettingsHeight(values)
        val settingsAreaHeight = h - offsetY - 10f
        val minScroll = -(totalValHeight - settingsAreaHeight).coerceAtLeast(0f)
        val maxScroll = 0f

        if (MathUtils.isHover(mouseX, mouseY, x.toInt(), y.toInt(), (x + w).toInt(), (y + h).toInt())) {
            val dWheel = Mouse.getDWheel() * 0.15f
            targetValueScroll = (targetValueScroll + dWheel).coerceIn(minScroll, maxScroll)
        }

        // Draw settings background panel
        NanoVGUtils.drawRoundedRect(
            panelX + 10f,
            y + offsetY - 5f,
            w - 20f,
            h - offsetY,
            8f,
            HIGHLIGHT_COLOR.withAlpha((fadeAlpha * 0.8f).toInt())
        )

        NanoVGUtils.renderScissorBox(panelX, y + offsetY, w, h - offsetY) {
            values.forEachIndexed { index, v ->
                val yPos = y + offsetY + valueScroll + index * SETTING_ROW_HEIGHT

                if (yPos + SETTING_ROW_HEIGHT < y || yPos > y + h) {
                    return@forEachIndexed
                }

                // Draw dividers between settings
                if (index > 0) {
                    NanoVGUtils.drawRect(
                        panelX + 25f,
                        yPos - 1f,
                        w - 50f,
                        1f,
                        Color(60, 60, 60, (fadeAlpha * 0.3f).toInt())
                    )
                }

                // Draw setting name
                settingFont.drawString(
                    v.name,
                    panelX + 25f,
                    yPos + (SETTING_ROW_HEIGHT - settingFont.height) / 2f,
                    TEXT_COLOR.withAlpha(fadeAlpha).rgb
                )

                // Draw different value types
                when (v) {
                    is BoolValue    -> drawBoolValue(v, panelX + w - 80f, yPos, fadeAlpha)
                    is IntegerValue -> drawIntValue(v, panelX + w - 115f, yPos, mouseX, mouseY, fadeAlpha)
                    is FloatValue   -> drawFloatValue(v, panelX + w - 115f, yPos, mouseX, mouseY, fadeAlpha)
                    is ListValue    -> drawListValue(v, panelX + w - 125f, yPos, mouseX, mouseY, fadeAlpha)
                }
            }
        }
    }

    // --- Individual Value Renders ---
    private fun drawBoolValue(value: BoolValue, x: Float, y: Float, alpha: Int) {
        // Ensure we have an animation for this boolean
        if (!boolAnimations.containsKey(value)) {
            boolAnimations[value] = Animation(value = if (value.value) 1f else 0f)
        }

        // Update animation target
        val anim = boolAnimations[value]!!
        anim.target = if (value.value) 1f else 0f

        // Draw toggle background
        val bgWidth = 42f
        val bgHeight = SETTING_ROW_HEIGHT - 6f
        val cornerRadius = bgHeight / 2f

        val onColor = BOOL_ON_COLOR.withAlpha(alpha)
        val offColor = BOOL_OFF_COLOR.withAlpha(alpha)

        // Blend colors based on animation
        val r = offColor.red + (onColor.red - offColor.red) * anim.value
        val g = offColor.green + (onColor.green - offColor.green) * anim.value
        val b = offColor.blue + (onColor.blue - offColor.blue) * anim.value
        val blendedColor = Color(r.toInt(), g.toInt(), b.toInt(), alpha)

        NanoVGUtils.drawRoundedRect(
            x, y + 3f,
            bgWidth, bgHeight,
            cornerRadius,
            blendedColor
        )

        // Draw sliding toggle indicator
        val circleRadius = (bgHeight - 4f) / 2f
        val circleX = x + 2f + (bgWidth - 2f * circleRadius - 4f) * anim.value

        NanoVGUtils.drawCircle(
            circleX + circleRadius,
            y + 3f + bgHeight / 2f,
            circleRadius,
            Color.WHITE.withAlpha(alpha)
        )

        // Draw text label
        val textX = x + bgWidth / 2f - settingFont.getStringWidth(if (value.value) "ON" else "OFF") / 2f
        settingFont.drawString(
            if (value.value) "ON" else "OFF",
            textX,
            y + (SETTING_ROW_HEIGHT - settingFont.height) / 2f,
            Color.WHITE.withAlpha((alpha * 0.8f).toInt()).rgb
        )
    }

    private fun drawIntValue(value: IntegerValue, x: Float, y: Float, mouseX: Int, mouseY: Int, alpha: Int) {
        drawSlider(
            current = value.value.toFloat(),
            min = value.minRange.toFloat(),
            max = value.maxRange.toFloat(),
            x = x, y = y,
            mouseX = mouseX, mouseY = mouseY,
            alpha = alpha
        ) { newVal -> value.set(newVal.toInt()) }
    }

    private fun drawFloatValue(value: FloatValue, x: Float, y: Float, mouseX: Int, mouseY: Int, alpha: Int) {
        drawSlider(
            current = value.value,
            min = value.minRange,
            max = value.maxRange,
            x = x, y = y,
            mouseX = mouseX, mouseY = mouseY,
            alpha = alpha
        ) { newVal -> value.set(newVal) }
    }

    private fun drawListValue(value: ListValue, x: Float, y: Float, mouseX: Int, mouseY: Int, alpha: Int) {
        val isOpen = listValueDropdownOpen.getOrDefault(value, false)
        val currentValue = value.value
        val boxWidth = 110f

        // Create dropdown container
        NanoVGUtils.drawRoundedRect(
            x, y + 2f,
            boxWidth, SETTING_ROW_HEIGHT - 4f,
            4f,
            HIGHLIGHT_COLOR.withAlpha(alpha)
        )

        // Draw current value
        settingFont.drawString(
            currentValue,
            x + 10f,
            y + (SETTING_ROW_HEIGHT - settingFont.height) / 2f,
            TEXT_COLOR.withAlpha(alpha).rgb
        )

        // Draw dropdown arrow
        settingFont.drawString(
            if (isOpen) "▲" else "▼",
            x + boxWidth - 15f,
            y + (SETTING_ROW_HEIGHT - settingFont.height) / 2f,
            TEXT_COLOR.withAlpha(alpha).rgb
        )

        // Handle clicking on dropdown
        if (MathUtils.isHover(mouseX, mouseY, x.toInt(), y.toInt(), (x + boxWidth).toInt(), (y + SETTING_ROW_HEIGHT).toInt()) &&
            Mouse.isButtonDown(0)) {
            if (listValueClickMap.getOrDefault(value, false).not()) {
                listValueDropdownOpen[value] = !isOpen
                listValueClickMap[value] = true

                // Add ripple effect
                ripples.add(Ripple(mouseX.toFloat(), mouseY.toFloat()))
            }
        } else {
            listValueClickMap[value] = false
        }

        // Draw dropdown options when open
        if (isOpen) {
            val values = value.values
            val dropdownHeight = values.size * SETTING_ROW_HEIGHT

            // Draw dropdown background
            NanoVGUtils.drawRoundedRect(
                x, y + SETTING_ROW_HEIGHT,
                boxWidth, dropdownHeight,
                4f,
                DROPDOWN_BG_COLOR.withAlpha(alpha)
            )

            // Initialize hover index if needed
            if (!listValueHoverIndex.containsKey(value)) {
                listValueHoverIndex[value] = -1
            }

            values.forEachIndexed { index, option ->
                val optionY = y + SETTING_ROW_HEIGHT + index * SETTING_ROW_HEIGHT
                val optionHovered = MathUtils.isHover(
                    mouseX, mouseY,
                    x.toInt(), optionY.toInt(),
                    (x + boxWidth).toInt(), (optionY + SETTING_ROW_HEIGHT).toInt()
                )

                if (optionHovered) {
                    listValueHoverIndex[value] = index
                }

                // Draw option background
                val isSelected = option == currentValue
                val isHovered = index == listValueHoverIndex[value]
                val bgColor = when {
                    isSelected -> ACCENT_COLOR.withAlpha((alpha * 0.3f).toInt())
                    isHovered -> HIGHLIGHT_COLOR.withAlpha((alpha * 0.5f).toInt())
                    else -> TRANSPARENT
                }

                NanoVGUtils.drawRoundedRect(
                    x + 2f, optionY,
                    boxWidth - 4f, SETTING_ROW_HEIGHT,
                    0f,
                    bgColor
                )

                // Draw option text
                val textColor = if (isSelected) ACCENT_COLOR else TEXT_COLOR
                settingFont.drawString(
                    option,
                    x + 10f,
                    optionY + (SETTING_ROW_HEIGHT - settingFont.height) / 2f,
                    textColor.withAlpha(alpha).rgb
                )

                // Handle selection
                if (optionHovered && Mouse.isButtonDown(0)) {
                    value.set(option)
                    listValueDropdownOpen[value] = false

                    // Add ripple effect
                    ripples.add(Ripple(mouseX.toFloat(), mouseY.toFloat()))
                }
            }
        }
    }

    private fun drawSlider(
        current: Float,
        min: Float,
        max: Float,
        x: Float,
        y: Float,
        mouseX: Int,
        mouseY: Int,
        alpha: Int,
        setter: (Float) -> Unit
    ) {
        val sliderWidth = 90f
        val barHeight = 4f
        val progress = (current - min) / (max - min).coerceAtLeast(0.00001f)

        // Draw slider track
        NanoVGUtils.drawRoundedRect(
            x,
            y + (SETTING_ROW_HEIGHT - barHeight) / 2f,
            sliderWidth,
            barHeight,
            barHeight / 2f,
            SLIDER_BG_COLOR.withAlpha(alpha)
        )

        // Draw progress fill
        NanoVGUtils.drawRoundedRect(
            x,
            y + (SETTING_ROW_HEIGHT - barHeight) / 2f,
            sliderWidth * progress,
            barHeight,
            barHeight / 2f,
            SLIDER_PROGRESS_COLOR.withAlpha(alpha)
        )

        // Draw handle with shadow
        val handleX = x + sliderWidth * progress
        val handleY = y + SETTING_ROW_HEIGHT / 2f
        val handleRadius = 6f

        // Draw handle shadow
        NanoVGUtils.drawCircle(
            handleX,
            handleY,
            handleRadius + 1f,
            Color(0, 0, 0, (alpha * 0.2f).toInt())
        )

        // Draw handle
        NanoVGUtils.drawCircle(
            handleX,
            handleY,
            handleRadius,
            Color.WHITE.withAlpha(alpha)
        )

        // Display value
        val displayFormat = if (current >= 10) "%.1f" else "%.2f"
        val display = String.format(displayFormat, current)
        valueFont.drawString(
            display,
            x + sliderWidth + 5f,
            y + (SETTING_ROW_HEIGHT - valueFont.height) / 2f,
            TEXT_COLOR.withAlpha(alpha).rgb
        )

        // Handle dragging
        if (draggingSlider == current && Mouse.isButtonDown(0)) {
            val newVal = ((mouseX - x) / sliderWidth) * (max - min) + min
            setter(newVal.coerceIn(min, max))
        } else if (
            MathUtils.isHover(mouseX, mouseY, (x - handleRadius).toInt(), (y - handleRadius).toInt(),
                (x + sliderWidth + handleRadius).toInt(), (y + SETTING_ROW_HEIGHT + handleRadius).toInt()) &&
            Mouse.isButtonDown(0)
        ) {
            draggingSlider = current
            val newVal = ((mouseX - x) / sliderWidth) * (max - min) + min
            setter(newVal.coerceIn(min, max))
        }
    }
}
*/