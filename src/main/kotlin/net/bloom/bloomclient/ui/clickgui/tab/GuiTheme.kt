package net.bloom.bloomclient.ui.clickgui.tab

import net.bloom.bloomclient.font.Fonts
import java.awt.Color

object GuiTheme {
    // --- GUI Dimensions ---
    private const val GUI_WIDTH = 470f
    private const val GUI_HEIGHT = 280f
    private const val GUI_RADIUS = 8f

    // Title Bar
    private const val TITLE_BAR_HEIGHT = 31f

    // Panels
    private const val CATEGORY_PANEL_WIDTH = 103f
    private const val MODULE_PANEL_WIDTH = 125f

    // --- Colors (Theme) ---
    private val BG_COLOR = Color(28, 32, 38)
    private val TITLE_BAR_COLOR = Color(14, 16, 19)
    private val HIGHLIGHT_COLOR = Color(36, 40, 46)
    private val HOVER_COLOR = Color(44, 48, 54)
    private val TEXT_COLOR = Color(220, 225, 230)
    private val SUBTITLE_COLOR = Color(180, 185, 190)
    private val TRANSPARENT = Color(0, 0, 0, 0)
    private val BOOL_ON_COLOR = Color(133, 196, 81)
    private val BOOL_OFF_COLOR = Color(212, 52, 65)
    private val SLIDER_BG_COLOR = Color(60, 63, 67)
    private val SLIDER_PROGRESS_COLOR = Color(132, 115, 201)
    private val ACCENT_COLOR = Color(132, 115, 201)
    private val SEARCH_BAR_COLOR = Color(24, 27, 32)

    // --- Fonts ---
    private val titleFont = Fonts.fontNano[16]       // Title
    private val categoryFont = Fonts.fontLexend[10]   // Categories
    private val moduleFont = Fonts.fontLexend[10]     // Modules
    private val settingFont = Fonts.fontLexend[9]    // Settings
    private val settingHeaderFont = Fonts.fontLexend[10] // Setting headers
    private val icon12Font = Fonts.fontIconGUI[12]     // Small icons
    private val icon24Font = Fonts.fontIconGUI[24]     // Large icons
    private val icon16Font = Fonts.fontIconGUI[16]     // Medium icons

    // --- Row Heights & Spacing ---
    private const val MODULE_ROW_HEIGHT = 20f
    private const val SETTING_ROW_HEIGHT = 18f
    private const val CAT_SPACING = 2f
    private const val CATEGORY_TOP_MARGIN = 8f
    private const val MODULE_TOP_MARGIN = 8f
    private const val MODULE_SPACING = 2f
    private const val SETTING_SPACING = 7f

    // --- Icon Strings (Custom Font) ---
    private const val BLOOM_ICON = "Q"
    private const val COMBAT_ICON = "R"
    private const val MOVE_ICON = "S"
    private const val USER_ICON = "T"
    private const val SEARCH_ICON = "U"
    private const val EYE_ICON = "V"
    private const val SETTINGS_ICON = "W"
    private const val HIDDEN_EYE_ICON = "X"
    private const val ALERT_ICON = "Y"
    private const val CHECK_ICON = "Z"
    private const val PUZZLE_ICON = "B" // fallback
}