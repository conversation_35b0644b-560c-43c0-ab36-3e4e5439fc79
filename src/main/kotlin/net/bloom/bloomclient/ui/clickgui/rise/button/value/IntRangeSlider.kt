package net.bloom.bloomclient.ui.clickgui.rise.button.value

import net.bloom.bloomclient.value.values.IntRangeValue
import java.awt.Color

class IntRangeSlider(value: IntRangeValue) : ValueButton(value) {
    override fun render(mouseX: Float, mouseY: Float) {
        super.render(mouseX, mouseY)

        if (value is IntRangeValue) {
            f8.drawString(value.name, pos.x, pos.y, Color(200, 200, 200, 250))
        }
    }
}