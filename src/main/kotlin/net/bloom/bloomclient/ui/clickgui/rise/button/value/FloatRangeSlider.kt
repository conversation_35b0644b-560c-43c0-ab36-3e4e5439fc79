package net.bloom.bloomclient.ui.clickgui.rise.button.value

import net.bloom.bloomclient.value.values.FloatRangeValue
import java.awt.Color

class FloatRangeSlider(value: FloatRangeValue) : ValueButton(value) {
    override fun render(mouseX: Float, mouseY: Float) {
        super.render(mouseX, mouseY)

        if (value is FloatRangeValue) {
            f8.drawString(value.name, pos.x, pos.y, Color(200, 200, 200, 250))
        }
    }
}