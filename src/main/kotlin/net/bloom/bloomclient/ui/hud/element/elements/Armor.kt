package net.bloom.bloomclient.ui.hud.element.elements

import net.bloom.bloomclient.ui.hud.element.Border
import net.bloom.bloomclient.ui.hud.element.Element
import net.bloom.bloomclient.ui.hud.element.ElementInfo
import net.minecraft.client.gui.Side
import net.bloom.bloomclient.value.values.ListValue
import net.minecraft.block.material.Material
import net.minecraft.client.renderer.GlStateManager
import net.minecraft.client.renderer.RenderHelper
import org.lwjgl.opengl.GL11

@ElementInfo(name = "Armor", single = true)
class Armor(
    x: Double = -8.0,
    y: Double = 57.0,
    scale: Float = 1F,
    side: Side = Side(Side.Horizontal.MIDDLE, Side.Vertical.BOTTOM)
): Element("Armor", x, y, scale, side) {

    private val modeValue = ListValue("Alignment", "Horizontal", arrayOf("Horizontal", "Vertical"))

    override fun drawElement(): Border {
        if (mc.playerController.isNotCreative) {
            GL11.glPushMatrix()

            val renderItem = mc.renderItem
            val isInsideWater = mc.thePlayer.isInsideOfMaterial(Material.water)

            var x = 1
            var y = if (isInsideWater) -10 else 0

            GL11.glColor4f(1F, 1F, 1F, 1F)

            for (index in 3 downTo 0) {
                val stack = mc.thePlayer.inventory.armorInventory[index] ?: continue

                GL11.glPushMatrix()
                GL11.glEnable(GL11.GL_BLEND)
                GL11.glBlendFunc(GL11.GL_SRC_ALPHA, GL11.GL_ONE_MINUS_SRC_ALPHA)
                RenderHelper.enableGUIStandardItemLighting()
                renderItem.renderItemIntoGUI(stack, x, y)
                renderItem.renderItemOverlays(mc.minecraftFontRendererObj, stack, x, y)
                RenderHelper.disableStandardItemLighting()
                GL11.glDisable(GL11.GL_BLEND)
                GL11.glPopMatrix()

                when (modeValue.get()) {
                    "Horizontal" -> x += 18
                    "Vertical" -> y += 18
                }
            }

            GlStateManager.enableAlpha()
            GlStateManager.disableBlend()
            GlStateManager.disableLighting()
            GlStateManager.disableCull()
            GL11.glPopMatrix()
        }

        return when (modeValue.get()) {
            "Horizontal" -> Border(0F, 0F, 72F, 17F)
            else -> Border(0F, 0F, 18F, 72F)
        }
    }

}