package net.bloom.bloomclient.ui.hud.designer.panel

import net.bloom.bloomclient.font.Fonts
import net.bloom.bloomclient.ui.hud.HUD
import net.bloom.bloomclient.ui.hud.designer.GuiHUDDesigner
import net.bloom.bloomclient.utils.MathUtils
import net.bloom.bloomclient.utils.extension.lerpWith
import net.bloom.bloomclient.utils.render.RenderUtils
import org.lwjgl.opengl.GL11
import java.awt.Color
import kotlin.math.max

class SelectionPanel(x: Float, y: Float): DesignerPanel(x, y, 150, 100) {
    private val font = Fonts.fontConsolas[40]

    private var showConfirmation = false

    override fun drawPanel(mouseX: Int, mouseY: Int) {
        var yPos = 15
        val panelWidth = if (HUD.elements.isNotEmpty()) {
            val maxStringWidthOfElements = HUD.elements.maxOf { font.getStringWidth(it.name) }
            max(width, maxStringWidthOfElements)
        } else width

        if (panelWidth > width)
            width = panelWidth

        RenderUtils.drawRect(x, y + 12, x + panelWidth, y + height, color = Color(0, 0, 0, 150))

        font.drawString("§lCreate element", x + 2, y + yPos, Color.WHITE)
        yPos += 10

        font.drawString("§lReset", x + 2, y + yPos, Color.WHITE)
        yPos += 15

        font.drawString("§lAvailable Elements", x + 2, y + yPos, Color.WHITE)
        yPos += 10

        for (element in HUD.elements) {
            font.drawString(element.name, x + 2, y + yPos, Color.WHITE)
            yPos += 10
        }

        RenderUtils.drawRect(x, y, x + panelWidth, y + 12, color = Color(0, 160, 255))
        GL11.glColor4f(1f, 1f, 1f, 1f)
        val centerX = MathUtils.lerp(x, x + panelWidth.toFloat(), 0.5F)
        font.drawCenteredString("§lElement Editor", centerX, y + 3.5f, Color.WHITE)

        if (showConfirmation) {
            val confirmationMessage = "Are you sure you want to reset?"
            val textWidth = font.getStringWidth(confirmationMessage)

            val dialogX = x
            val dialogX2 = x + textWidth
            val dialogY = y + height
            val padding = 5
            val dialogHeight = 30
            val centerDialogX = (dialogX..dialogX2).lerpWith(0.5F)

            RenderUtils.drawRect(
                dialogX - padding,
                dialogY + 10,
                dialogX2 + padding,
                dialogY + dialogHeight + 10,
                color = Color(0, 0, 0, 150)
            )

            font.drawCenteredString(confirmationMessage, centerDialogX, dialogY + 12f, Color.WHITE.rgb)

            val buttonData = listOf(
                "Yes" to Color.GREEN to (dialogX..centerDialogX),
                "No" to Color.RED to (centerDialogX..dialogX2)
            )

            val answerButtonY = MathUtils.lerp(dialogY + 12 + font.height, dialogY + dialogHeight + 10, 0.5f)
            val buttonWidth = font.getStringWidth("Yes")
            val paddingY = buttonWidth / 2

            buttonData.forEach { (labelAndColor, bounds) ->
                val (label, color) = labelAndColor
                val buttonX = bounds.lerpWith(0.5F)
                val isHovered =
                    mouseX.toFloat() in (buttonX - buttonWidth..buttonX + buttonWidth) && mouseY.toFloat() in (answerButtonY - paddingY..answerButtonY + paddingY)

                RenderUtils.drawRect(
                    buttonX - buttonWidth,
                    answerButtonY - paddingY,
                    buttonX + buttonWidth,
                    answerButtonY + paddingY,
                    color = color.let { if (isHovered) it.darker() else it })

                font.drawCenteredString(label, buttonX, answerButtonY - 2, Color.WHITE.rgb, true)
            }
        }
    }

    override fun handleMouseClick(mouseX: Int, mouseY: Int, mouseButton: Int) {
        var yPos = 15 + scroll

        val createElementStringWidth = font.getStringWidth("§lCreate element")
        if (MathUtils.isHover(mouseX, mouseY, x + 2, y + yPos, x + 2 + createElementStringWidth, y + yPos + font.height)) {
            GuiHUDDesigner.currentPanel = CreatePanel(x, y)
            return
        }
        yPos += 10

        val resetStringWidth = font.getStringWidth("§lReset")
        if (MathUtils.isHover(mouseX, mouseY, x + 2, y + yPos, x + 2 + resetStringWidth, y + yPos + font.height)) {
            showConfirmation = true
            return
        }
        yPos += 25

        for (element in HUD.elements) {
            val stringWidth = font.getStringWidth(element.name) + 8

            if (MathUtils.isHover(mouseX, mouseY, x + 2, y + yPos, x + 2 + stringWidth, y + yPos + font.height)) {
                GuiHUDDesigner.currentPanel = EditorPanel(element, x, y)
                return
            }

            yPos += 10
        }

        if (showConfirmation) {
            val confirmationMessage = "Are you sure you want to reset?"
            val textWidth = font.getStringWidth(confirmationMessage)

            val dialogX = x
            val dialogX2 = x + textWidth
            val dialogY = y + height
            val dialogHeight = 30
            val centerDialogX = (dialogX..dialogX2).lerpWith(0.5F)

            val buttonData = listOf(
                "Yes" to Color.GREEN to (dialogX..centerDialogX),
                "No" to Color.RED to (centerDialogX..dialogX2)
            )

            val answerButtonY = MathUtils.lerp(dialogY + 12 + font.height, dialogY + dialogHeight + 10, 0.5f)
            val buttonWidth = font.getStringWidth("Yes")
            val paddingY = buttonWidth / 2

            buttonData.forEach { (labelAndColor, bounds) ->
                val label = labelAndColor.first
                val buttonX = bounds.lerpWith(0.5F)
                val isHovered =
                    mouseX.toFloat() in (buttonX - buttonWidth..buttonX + buttonWidth) && mouseY.toFloat() in (answerButtonY - paddingY..answerButtonY + paddingY)

                if (isHovered) {
                    if (label == "Yes")
                        HUD.setDefault()
                    showConfirmation = false
                    return
                }
            }


        }
    }

}