package net.bloom.bloomclient.ui.hud.designer.panel

import net.bloom.bloomclient.font.Fonts
import net.bloom.bloomclient.ui.hud.HUD
import net.bloom.bloomclient.ui.hud.designer.GuiHUDDesigner
import net.bloom.bloomclient.utils.MathUtils
import net.bloom.bloomclient.utils.render.RenderUtils
import java.awt.Color
import kotlin.math.max

class CreatePanel(x: Float, y: Float): DesignerPanel(x, y, 150, 100) {
    private val font = Fonts.fontConsolas[40]

    override fun drawPanel(mouseX: Int, mouseY: Int) {
        var yPos = 15 + scroll
        val filteredElements = HUD.ELEMENTS.filter { (element, info) -> !info.single || !HUD.elements.any { it.javaClass == element } }

        val panelWidth = if (filteredElements.isNotEmpty()) {
            val maxStringWidthForElements = filteredElements.maxOf { (_, info) -> font.getStringWidth(info.name) + 8 }
            max(width, maxStringWidthForElements)
        } else width

        if (panelWidth > width)
            width = panelWidth

        RenderUtils.drawRect(x, y + 12, x + width, y + height, color = Color(0, 0, 0, 150))
        RenderUtils.drawRect(x, y, x + width, y + 12, color = Color(0, 160, 255))
        val centerX = MathUtils.lerp(x.toFloat(),x + panelWidth.toFloat(), 0.5F)
        font.drawCenteredString("§lCreate element", centerX, y + 3.5F, Color.WHITE)

        for ((_, info) in filteredElements) {
            val name = info.name
            font.drawString(name, x + 2f, y + yPos, Color.WHITE)
            yPos += 10
        }
    }

    override fun handleMouseClick(mouseX: Int, mouseY: Int, mouseButton: Int) {
        var yPos = 15 + scroll
        val filteredElements = HUD.ELEMENTS.filter { (element, info) ->
            !info.single || !HUD.elements.any { it.javaClass == element }
        }

        for ((element, info) in filteredElements) {
            val name = info.name
            val stringWidth = font.getStringWidth(name)

            if (MathUtils.isHover(mouseX, mouseY, x + 2, y + yPos, x + 2 + stringWidth, y + yPos + font.height)) {
                try {
                    val newElement = element.getConstructor().newInstance()
                    if (newElement.createElement())
                        HUD.addElement(newElement)
                } catch (e: InstantiationException) {
                    e.printStackTrace()
                } catch (e: IllegalAccessException) {
                    e.printStackTrace()
                }

                GuiHUDDesigner.currentPanel = GuiHUDDesigner.selectionPanel.also {
                    it.x = x
                    it.y = y
                }
                return
            }
            yPos += 10
        }
    }


}