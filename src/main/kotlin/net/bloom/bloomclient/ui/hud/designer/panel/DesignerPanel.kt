package net.bloom.bloomclient.ui.hud.designer.panel

import net.bloom.bloomclient.utils.MathUtils
import net.minecraft.client.MinecraftInstance
import org.lwjgl.input.Mouse

open class DesignerPanel(var x: Float, var y: Float, var width: Int, var height: Int): MinecraftInstance() {

    var scroll = 0f
    private var dragX = 0f
    private var dragY = 0f

    private var drag = false

    open fun drawPanel(mouseX: Int, mouseY: Int) {}

    open fun handleMouseClick(mouseX: Int, mouseY: Int, mouseButton: Int) {}

    fun drag(mouseX: Int, mouseY: Int) {
        if (Mouse.isButtonDown(0) && MathUtils.isHover(mouseX, mouseY, x, y, x + width, y + 12)) {
            drag = true
            dragX = mouseX - x
            dragY = mouseY - y
        }

        if (Mouse.isButtonDown(0) && drag) {
            x = mouseX - dragX
            y = mouseY - dragY
        } else drag = false
    }

}