package net.bloom.bloomclient.ui.hud.element.elements

import net.bloom.bloomclient.features.mode.Stateable
import net.bloom.bloomclient.ui.hud.element.Border
import net.bloom.bloomclient.ui.hud.element.Element
import net.bloom.bloomclient.ui.hud.element.ElementInfo
import net.bloom.bloomclient.ui.hud.element.elements.arraylist.BloomArrayListStyle
import net.bloom.bloomclient.value.values.ModeValue
import net.minecraft.client.gui.Side

@ElementInfo(name = "ArrayList", single = true)
class ArrayList(
    x: Double = 6.0,
    y: Double = 2.0,
    scale: Float = 1F,
    side: Side = Side(Side.Horizontal.RIGHT, Side.Vertical.TOP)
): Element("Arraylist", x, y, scale, side), Stateable {
    private val style by ModeValue("Style", arrayOf(
        BloomArrayListStyle(this)
    ), this)

    override fun drawElement(): Border = style.drawElement()

    override val state = false // no use event handler
}