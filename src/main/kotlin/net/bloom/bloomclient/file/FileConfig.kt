package net.bloom.bloomclient.file

import net.bloom.bloomclient.utils.io.FileUtils
import com.google.gson.JsonNull
import com.google.gson.JsonObject
import com.google.gson.JsonParser
import java.io.BufferedReader
import java.io.File
import java.io.FileReader

abstract class FileConfig(val file: File) {
	abstract fun fromJson(json: JsonObject)

	abstract fun toJson(): JsonObject

	open fun loadConfig() {
		val jsonElement = JsonParser.parseReader(BufferedReader(FileReader(file)))

		if (jsonElement is JsonNull || !jsonElement.isJsonObject)
			return

		fromJson(jsonElement.asJsonObject)
	}

	open fun saveConfig() {
		val jsonObject = toJson()
		FileUtils.writeJson(file, jsonObject)
	}

	fun createConfig() = file.createNewFile()

	fun hasConfig() = file.exists()

}